{"name": "uni-preset-vue", "version": "0.0.0", "scripts": {"build:mp-weixin-test": "uni build -p mp-weixin --mode development", "build:mp-weixin-prod": "uni build -p mp-weixin --mode production"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-3090820231124001", "@dcloudio/uni-app-plus": "3.0.0-3090820231124001", "@dcloudio/uni-components": "3.0.0-3090820231124001", "@dcloudio/uni-h5": "3.0.0-3090820231124001", "@dcloudio/uni-mp-alipay": "3.0.0-3090820231124001", "@dcloudio/uni-mp-baidu": "3.0.0-3090820231124001", "@dcloudio/uni-mp-jd": "3.0.0-3090820231124001", "@dcloudio/uni-mp-kuaishou": "3.0.0-3090820231124001", "@dcloudio/uni-mp-lark": "3.0.0-3090820231124001", "@dcloudio/uni-mp-qq": "3.0.0-3090820231124001", "@dcloudio/uni-mp-toutiao": "3.0.0-3090820231124001", "@dcloudio/uni-mp-weixin": "3.0.0-3090820231124001", "@dcloudio/uni-mp-xhs": "3.0.0-3090820231124001", "@dcloudio/uni-quickapp-webview": "3.0.0-3090820231124001", "axios": "^0.26.0", "axios-miniprogram-adapter": "^0.3.5", "pinia": "^2.0.36", "uni-mini-router": "^0.1.5", "uni-read-pages-vite": "^0.0.6", "uniapp-axios-adapter": "^0.3.2", "uview-plus": "^3.1.41", "vue": "3.2.47", "vue-i18n": "9.8.0"}, "devDependencies": {"@dcloudio/types": "3.4.3", "@dcloudio/uni-automator": "3.0.0-3090820231124001", "@dcloudio/uni-cli-shared": "3.0.0-3090820231124001", "@dcloudio/uni-stacktracey": "3.0.0-3090820231124001", "@dcloudio/vite-plugin-uni": "3.0.0-3090820231124001", "@vue/runtime-core": "3.3.13", "@vue/tsconfig": "^0.1.3", "dayjs": "^1.11.10", "lodash": "^4.17.21", "pinia-plugin-persistedstate": "^3.2.0", "sass": "^1.69.5", "typescript": "^4.9.4", "uni-parse-pages": "^0.0.1", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "4.0.3", "vue-tsc": "^1.0.24"}}