import request from "../utils/http";
// 分页
// http://yapi.vbeehive.com:81/project/24/interface/api/114
export function queryPageData (data) {
    return request({
        url: '/api/project/queryProjectPage',
        method: 'get',
        data:data
    })
}

// 新增/修改
// http://yapi.vbeehive.com:81/project/24/interface/api/115
export function saveEdit (data) {
    return request({
        url: '/api/project/saveProject',
        method: 'post',
        data:data
    })
}


// 详细
// http://yapi.vbeehive.com:81/project/24/interface/api/116
export function queryDetails (id) {
    return request({
        url: '/api/project/queryProjectDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}
