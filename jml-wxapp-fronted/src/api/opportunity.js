import request from "../utils/http";
// 分页
// http://yapi.vbeehive.com:81/project/24/interface/api/125
export function queryPageData (data) {
    return request({
        url: '/api/opportunity/queryOpportunityPageBy',
        method: 'get',
        data:data
    })
}

// 新增/修改
// http://yapi.vbeehive.com:81/project/24/interface/api/128
export function saveEdit (data) {
    return request({
        url: '/api/opportunity/saveOpportunity',
        method: 'post',
        data:data
    })
}


// 详细
// http://yapi.vbeehive.com:81/project/24/interface/api/129
export function queryDetails (id) {
    return request({
        url: '/api/opportunity/queryOpportunityDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}

// 切换阶段
// http://yapi.vbeehive.com:81/project/24/interface/api/146
export function changeStage (data) {
    return request({
        url: '/api/opportunity/changeStage',
        method: 'post',
        data:data
    })
}
