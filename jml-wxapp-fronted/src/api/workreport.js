import request from "../utils/http";
// 分页
// http://yapi.vbeehive.com:81/project/24/interface/api/133
export function queryPageData (data) {
    return request({
        url: '/api/salesReport/querySalesReportPageBy',
        method: 'get',
        data:data
    })
}

// 不分页
// http://yapi.vbeehive.com:81/project/24/interface/api/134
export function queryListData (data) {
    return request({
        url: '/api/salesReport/queryListBy',
        method: 'get',
        data:data
    })
}

// 新增/修改
// http://yapi.vbeehive.com:81/project/24/interface/api/135
export function saveEdit (data) {
    return request({
        url: '/api/salesReport/saveSalesReport',
        method: 'post',
        data:data
    })
}


// 详细
// http://yapi.vbeehive.com:81/project/24/interface/api/136
export function queryDetails (id,readFlag = null) {
    let data={id:id,readFlag}
    return request({
        url: '/api/salesReport/querySalesReportDetails',
        method: 'get',
        data:data
    })
}

// 一键阅读
// http://yapi.vbeehive.com:81/project/24/interface/api/147
export function gmRead (data) {
    return request({
        url: '/api/salesReport/gmRead',
        method: 'get',
        data:data
    })
}
// 经理-报表
// http://yapi.vbeehive.com:81/project/24/interface/api/221
export function queryGmReport (data) {
    return request({
        url: '/api/salesReport/queryGmReport',
        method: 'get',
        data:data
    })
}

// 删除
// http://yapi.vbeehive.com:81/project/24/interface/api/341
export function deleteReport (id) {
    return request({
        url: '/api/salesReport/deleteReport',
        method: 'get',
        data:{id}
    })
}

// 提交
// http://yapi.vbeehive.com:81/project/24/interface/api/333
export function submitReport (id) {
    return request({
        url: '/api/salesReport/submitReport',
        method: 'get',
        data:{id}
    })
}


// 分页获取评论
// http://yapi.vbeehive.com:81/project/24/interface/api/145
export function queryCommentsPageBy (data) {
    return request({
        url: '/api/comments/queryCommentsPageBy',
        method: 'get',
        data
    })
}

// 评论提交
// http://yapi.vbeehive.com:81/project/24/interface/api/151
export function saveComment (data) {
    debugger
    return request({
        url: '/api/comments/saveComment',
        method: 'post',
        data:data
    })
}

// @用户列表
// http://yapi.vbeehive.com:81/project/24/interface/api/157
export function queryByMentionedUser (data) {
    return request({
        url: '/api/comments/queryByMentionedUser',
        method: 'get',
        data
    })
}