import request from "../utils/http";
// 分页
// http://yapi.vbeehive.com:81/project/24/interface/api/107
export function queryAccountPage (data) {
    return request({
        url: '/api/account/queryAccountPage',
        method: 'get',
        data:data
    })
}

// 公司列表
// http://yapi.vbeehive.com:81/project/24/interface/api/108
export function queryListByStaff (data) {
    return request({
        url: '/api/account/queryListByStaff',
        method: 'get',
        data:data
    })
}

// 新增/修改
// http://yapi.vbeehive.com:81/project/24/interface/api/109
export function saveAccount (data) {
    return request({
        url: '/api/account/saveAccount',
        method: 'post',
        data:data
    })
}

// 详细
// http://yapi.vbeehive.com:81/project/24/interface/api/111
export function queryAccountDetails (id) {
    return request({
        url: '/api/account/queryAccountDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}

// 地址保存
// http://yapi.vbeehive.com:81/project/24/interface/api/112
export function saveAddress (data) {
    return request({
        url: '/api/account/saveAddress',
        method: 'post',
        data:data
    })
}

// 公司地址详情
// http://yapi.vbeehive.com:81/project/24/interface/api/113
export function queryAddressDetails (id) {
    return request({
        url: '/api/account/queryAddressDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}

//根据公司Id获取联系人和机会列表
//http://yapi.vbeehive.com:81/project/24/interface/api/245
export function queryListByAccountId (accountId) {
    return request({
        url: '/api/account/queryListByAccountId',
        method: 'get',
        data:{
            accountId:accountId,
        }
    })
}
