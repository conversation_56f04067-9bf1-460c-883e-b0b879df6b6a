import request from "../utils/http";

/**
 * 根据code获取用户信息
 * http://yapi.vbeehive.com:81/project/31/interface/api/262
 * @returns
 */
export function byCode(code) {
    return request({
        url: "/api/weixin/wxapp/auth/byCode",
        method: "post",
        data: {
            code:code
        },
    });
}


/**
 * 小程序用户登录
 * http://yapi.vbeehive.com:81/project/31/interface/api/244
 * @returns
 */
export function staffLogin(data) {
    return request({
        url: "/api/jml/staff/login",
        method: "post",
		headers: {
		    'Content-Type': 'application/json'
		},
        data,
    });
}


/**
 * 小程序用户重置密码
 * http://yapi.vbeehive.com:81/project/31/interface/api/250
 * @returns
 */
export function resetPwd(newPwd) {
    return request({
        url: "/api/jml/staff/resetPwd",
        method: "post",
        data: {
            newPwd
        },
    });
}

