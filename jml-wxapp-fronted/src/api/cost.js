import request from "../utils/http";
// 分页
// http://yapi.vbeehive.com:81/project/24/interface/api/137
export function queryPageData (data) {
    return request({
        url: '/api/reimbursement/queryPage',
        method: 'get',
        data:data
    })
}

// 新增/修改
// http://yapi.vbeehive.com:81/project/24/interface/api/141
export function saveEdit (data) {
    return request({
        url: '/api/reimbursement/saveReimbursement',
        method: 'post',
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        },
        data:data
    })
}


// 详细
// http://yapi.vbeehive.com:81/project/24/interface/api/138
export function queryDetails (id) {
    return request({
        url: '/api/reimbursement/queryDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}

// 子项详细
// http://yapi.vbeehive.com:81/project/24/interface/api/138
export function queryItemDetails (id) {
    return request({
        url: '/api/reimbursement/queryReimDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}

// 提交接口
export function submitReim (id) {
    return request({
        url: '/api/reimbursement/submitReim',
        method: 'get',
        data:{id}
    })
}

// 删除接口
export function deleteReim (id) {
    return request({
        url: '/api/reimbursement/deleteReim',
        method: 'get',
        data:{id}
    })
}
