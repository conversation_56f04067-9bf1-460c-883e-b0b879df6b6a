import request from "../utils/http";
// 分页
// http://yapi.vbeehive.com:81/project/24/interface/api/121
export function queryPageData (data) {
    return request({
        url: '/api/contact/queryContactPageBy',
        method: 'get',
        data:data
    })
}

// 不分页
// http://yapi.vbeehive.com:81/project/24/interface/api/121
export function queryListData (data) {
    return request({
        url: '/api/contact/queryListByStaff',
        method: 'get',
        data:data
    })
}

// 新增/修改
// http://yapi.vbeehive.com:81/project/24/interface/api/123
export function saveEdit (data) {
    return request({
        url: '/api/contact/saveContact',
        method: 'post',
        data:data
    })
}


// 详细
// http://yapi.vbeehive.com:81/project/24/interface/api/124
export function queryDetails (id) {
    return request({
        url: '/api/contact/queryContactDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}
