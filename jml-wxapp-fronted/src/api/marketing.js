import request from "../utils/http";
// 市场活动分页
// http://yapi.vbeehive.com:81/project/24/interface/api/90
export function queryMarketingPage (data) {
    return request({
        url: '/api/marketing/queryMarketingPage',
        method: 'get',
        data:data
    })
}

// 新增/修改 市场活动
// http://yapi.vbeehive.com:81/project/24/interface/api/91
export function saveMarketing (data) {
    return request({
        url: '/api/marketing/saveMarketing',
        method: 'post',
        data:data
    })
}


// 市场活动详情查询
// http://yapi.vbeehive.com:81/project/24/interface/api/93
export function queryMarketingDetails (id) {
    return request({
        url: '/api/marketing/queryMarketingDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}

// 市场活动成员分页
// http://yapi.vbeehive.com:81/project/24/interface/api/94
export function queryMemberPageBy (data) {
    return request({
        url: '/api/marketing/queryMemberPageBy',
        method: 'get',
        data:data
    })
}

// 新增/修改 市场活动成员
// http://yapi.vbeehive.com:81/project/24/interface/api/95
export function saveMember (data) {
    return request({
        url: '/api/marketing/saveMember',
        method: 'post',
        data:data
    })
}


// 市场活动成员详情查询
// http://yapi.vbeehive.com:81/project/24/interface/api/96
export function queryMemberDetails (id) {
    return request({
        url: '/api/marketing/queryMemberDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}

