import request from "../utils/http";
import config from '@/common/config.js'
/**
 * 企业微信签名
 * http://yapi.vbeehive.com:81/project/24/interface/api/269
 * @returns
 */
export function sign() {
    let  url  = location.href.split('#')[0]
    return request({
        url: "/openapi/wxwork/auth/sign",
        method: "post",
        data: {url:url},
    });
}


/**
 * 加载企业微信配置
 * http://yapi.vbeehive.com:81/project/24/interface/api/277
 * @returns
 */
export function loadConf(data) {
    return request({
        url: "/openapi/wxwork/auth/loadConf",
        method: "get",
        data: data,
    });
}

/**
 * 获取企业微信用户的详细信息
 * http://yapi.vbeehive.com:81/project/24/interface/api/301
 * @returns
 */
export function userInfo(code) {
    return request({
        url: "/openapi/wxwork/auth/detail",
        method: "post",
        data: {
            code:code
        },
    });
}

/**
 * 产品列表
 * http://yapi.vbeehive.com:81/project/24/interface/api/120
 * @returns
 */
export function queryProductList() {
    return request({
        url: "/api/product/queryList",
        method: "get",
    });
}

/**
 * 查询子级员工-包含自己
    http://yapi.vbeehive.com:81/project/24/interface/api/165
 */
export function queryUserList(data) {
    return request({
        url: "/api/jml/staff/list4SubStaff",
        method: "get",
        data: data,
    });
}
/**
 * 获取全部字典类型
 * http://yapi.vbeehive.com:81/project/24/interface/api/99
 * @param {*} data
 * @returns
 */
export function getDictAllType(data) {
    return request({
        url: "/api/core/dict/all",
        method: "get",
        data: data,
    });
}

/**
 * 获取单个字典
 * http://yapi.frensworkz.com/project/160/interface/api/8051
 * @param {*} tcode
 * @returns
 */
export function getDictList(code) {
    return request({
        url: "/api/core/dict/queryItem",
        method: "get",
        data: { tcode: code },
    });
}

/**
 * 获取多字典
 * http://yapi.frensworkz.com/project/160/interface/api/8416
 * @param {*} code 逗号分割code
 * @returns
 */
export function getDictBatch(code) {
    return request({
        url: "/api/core/dict/queryDictBatch",
        method: "get",
        data: { tcode: code },
    });
}

/**
 * 获取相关列表分页记录
 * http://yapi.frensworkz.com/project/160/interface/api/8416
 * @param {*} code 逗号分割code
 * @returns
 */
export function getRecordData(url,data) {
    return request({
        url: url,
        method: "get",
        data: data,
    });
}

//获取所有菜单
//http://yapi.vbeehive.com:81/project/24/interface/api/144
export function getAllMenu(data) {
    return request({
        url: '/api/jml/staff/menuFunction',
        method: "post",
        data: data,
    });
}



/**
 * 获取省市树
 * http://yapi.vbeehive.com:81/project/24/interface/api/155
 * @param {*} tcode
 * @returns
 */
export function getRegionTree() {
    return request({
        url: "/api/core/region/tree",
        method: "get",
        // data: { tcode: code },
    });
}
/**
 * 附件上传
 * http://yapi.vbeehive.com:81/project/24/interface/api/189
 * @param {*} data
 * @returns
 */
export function upload(data) {
    return request({
        url: "/api/core/file/upload",
        headers: {
            "Content-Type": "multipart/form-data",
        },
        method: "post",
        data: data,
        isFile: true,
        loadText: "正在上传...",
  });
}

/**
 * UNI附件上传
 * http://yapi.vbeehive.com:81/project/24/interface/api/189
 * @param {*} data
 * @returns
 */
export function asyncUpload(filePath,data = {}){
	return new Promise((resolve, reject) => {
		if(!filePath){
			uni.showToast({
				title: "asyncUpload:参数错误",
				icon:"none"
			})
			reject('asyncUpload:参数错误');
		}
		uni.uploadFile({
			url: config.baseURL+"/api/core/file/upload",
			// url:"https://qa-crm.chameleon-artec.com/jml/api/core/file/upload",
			filePath: filePath,
			name: 'file',
			// header: {'content-type':'application/x-www-form-urlencoded' },
			formData: data,
			success: (res) => {
				if(res.statusCode == 200){
					var json = JSON.parse(res.data)
					if (json.code=='1') {
						resolve(json)
					} else {
						uni.showToast({
							title: json.msg,
							icon:"none"
						})
						reject(json.msg)
					}
				}else{
					reject(res.data)
				}
			},
			fail:(res) =>{
				reject(res)
				console.log(res)
			}
		});
	} )
}


/**
 * 附件删除
 * http://yapi.frensworkz.com/project/160/interface/api/8461
 * @param {*} data
 * @returns
 */
 export function attachmentDelete(url) {
    return request({
      url: "/api/attachment/attachmentDelete",
      headers: {
        "Content-Type": "multipart/form-data",
      },
      method: "get",
      data: {
        url:url
      },
    });
}



//根据tcode获取字典tree
//http://yapi.vbeehive.com:81/project/24/interface/api/357
export function dictTree(code) {
    return request({
        url: '/api/core/dict/dictTree',
        method: "get",
        data: { tcode: code },
    });
}

/**
 * 百度地图获取地址-根据经纬度 【换成了腾讯地图】
 * http://yapi.vbeehive.com:81/project/31/interface/api/160
 * @param {Object} lng
 * @param {Object} lat
 */
export function getAddrByLngAndLat(lng,lat) {
    return request({
		url: "/api/qqMap/getAddrByLngAndLat",
        // url: "/api/baiduMap/getAddrByLngAndLat",
        method: "get",
        data: {lng,lat},
    });
}


/**
 * 查询工厂上下级
    http://yapi.vbeehive.com:81/project/24/interface/api/165
 */
export function list4FactorySubStaffWithoutSelf(data) {
    return request({
        url: "/api/jml/staff/list4FactorySubStaffWithoutSelf",
        method: "get",
        data: data,
    });
}