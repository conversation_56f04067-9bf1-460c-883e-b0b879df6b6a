import request from "../utils/http";
// 分页
// http://yapi.vbeehive.com:81/project/24/interface/api/130
export function queryPageData (data) {
    return request({
        url: '/api/opportunity/queryOppProductPageBy',
        method: 'get',
        data:data
    })
}

// 新增/修改
// http://yapi.vbeehive.com:81/project/24/interface/api/132
export function saveEdit (data) {
    return request({
        url: '/api/opportunity/saveOppProduct',
        method: 'post',
        data:data
    })
}


// 详细
// http://yapi.vbeehive.com:81/project/24/interface/api/131
export function queryDetails (id) {
    return request({
        url: '/api/opportunity/queryOppProductDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}
