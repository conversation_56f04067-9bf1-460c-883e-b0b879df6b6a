import request from "../utils/http";
// 销售线索列表-分页
// http://yapi.vbeehive.com:81/project/24/interface/api/101
export function querySalesLeadPage (data) {
    return request({
        url: '/api/salesLead/querySalesLeadPage',
        method: 'get',
        data:data
    })
}

// 新增/修改 销售线索
// http://yapi.vbeehive.com:81/project/24/interface/api/103
export function saveSalesLead (data) {
    return request({
        url: '/api/salesLead/saveSalesLead',
        method: 'post',
        data:data
    })
}


// 线索详细
// http://yapi.vbeehive.com:81/project/24/interface/api/105
export function querySalesLeadDetails (id) {
    return request({
        url: '/api/salesLead/querySalesLeadDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}

// 线索转换
// http://yapi.vbeehive.com:81/project/24/interface/api/104
export function convertSalesLead (id) {
    return request({
        url: '/api/salesLead/convertSalesLead',
        method: 'post',
        data:{
            id:id,
        }
    })
}

// 线索状态切换
// http://yapi.vbeehive.com:81/project/24/interface/api/104
export function changeStatus (data) {
    return request({
        url: '/api/salesLead/changeStatus',
        method: 'post',
        data:data
    })
}

