import request from "../utils/http";
// http://yapi.vbeehive.com:81/project/31/interface/api/325
export function queryPageData (data) {
    return request({
        url: '/api/internalTicket/queryTicketPage',
        method: 'get',
        data:data
    })
}

// 内部工单保存
// http://yapi.vbeehive.com:81/project/31/interface/api/313
export function saveEdit (data) {
    return request({
        url: '/api/internalTicket/saveTicket',
        method: 'post',
        data:data
    })
}


// 内部工单明细
// http://yapi.vbeehive.com:81/project/31/interface/api/319
export function queryDetails (id) {
    return request({
        url: '/api/internalTicket/queryTicketDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}




// 内部工单删除
// http://yapi.vbeehive.com:81/project/31/interface/api/323
export function deleteTicket (id) {
    return request({
        url: '/api/internalTicket/deleteTicket',
        method: 'get',
        data:{
            id:id,
        }
    })
}

// 内部工单受理
// http://yapi.vbeehive.com:81/project/31/interface/api/321
export function ticketDo (data) {
    return request({
        url: '/api/internalTicket/ticketDo',
        method: 'post',
        data:data
    })
}

