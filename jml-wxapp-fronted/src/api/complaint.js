import request from "../utils/http";
// 客诉分页查询-分页
// http://yapi.vbeehive.com:81/project/24/interface/api/161
export function queryPageData (data) {
    return request({
        url: '/api/case/queryCasePage',
        method: 'get',
        data:data
    })
}

// 客诉保存-客服提交
// http://yapi.vbeehive.com:81/project/24/interface/api/164
export function saveEdit (data) {
    return request({
        url: '/api/case/saveCase',
        method: 'post',
		headers: {
		    'Content-Type': 'application/json;charset=UTF-8'
		},
        data:data
    })
}


// 客诉明细
// http://yapi.vbeehive.com:81/project/24/interface/api/167
export function querySalesLeadDetails (id) {
    return request({
        url: '/api/case/queryCaseDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}


// 客诉订单明细保存
// http://yapi.vbeehive.com:81/project/24/interface/api/170
export function saveCaseOrderDetails (data) {
    return request({
        url: '/api/case/saveCaseOrderDetails',
        method: 'post',
        data:data
    })
}

// 客诉订单明细
// http://yapi.vbeehive.com:81/project/24/interface/api/173
export function queryCaseOrderDetails (id) {
    return request({
        url: '/api/case/queryCaseOrderDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}

// 客诉删除
// http://yapi.vbeehive.com:81/project/24/interface/api/176
export function deleteReport (id) {
    return request({
        url: '/api/case/deleteCase',
        method: 'get',
        data:{
            id:id,
        }
    })
}

// 客诉受理
// http://yapi.vbeehive.com:81/project/24/interface/api/179
export function caseDo (data) {
    return request({
        url: '/api/case/caseDo',
        method: 'post',
		headers: {
		    'Content-Type': 'application/json;charset=UTF-8'
		},
        data:data
    })
}

// 问题分类
// http://yapi.vbeehive.com:81/project/24/interface/api/185
export function queryProblemClass (factory,problemType) {
    return request({
        url: '/api/case/queryProblemClass',
        method: 'get',
        data:{
            factory,problemType
        }
    })
}

// 服务产品删除
// http://yapi.vbeehive.com:81/project/31/interface/api/351
export function deleteCaseDetail (id) {
    return request({
        url: '/api/case/deleteCaseDetail',
        method: 'get',
        data:{
            id:id,
        }
    })
}

// 服务产品结案
// http://yapi.vbeehive.com:81/project/31/interface/api/353
export function caseClose (data) {
    return request({
        url: '/api/case/caseClose',
        method: 'post',
		headers: {
		    'Content-Type': 'application/json;charset=UTF-8'
		},
        data:data
    })
}

// 质量意见保存/提交
// http://yapi.vbeehive.com:81/project/31/interface/api/349
export function saveOpinion (data) {
    return request({
        url: '/api/case/saveOpinion',
        method: 'post',
		headers: {
		    'Content-Type': 'application/json;charset=UTF-8'
		},
        data:data
    })
}

// 服务单退回
// http://yapi.vbeehive.com:81/project/31/interface/api/355
export function caseReturn (data) {
    return request({
        url: '/api/case/caseReturn',
        method: 'post',
		headers: {
		    'Content-Type': 'application/json;charset=UTF-8'
		},
        data:data
    })
}


// 服务订单产品修改
// http://yapi.vbeehive.com:81/project/31/interface/api/365
export function updateOrderDetail (data) {
    return request({
        url: '/api/case/updateOrderDetail',
        method: 'post',
		headers: {
		    'Content-Type': 'application/json;charset=UTF-8'
		},
        data:data
    })
}

// 服务单提交质量部
// http://yapi.vbeehive.com:81/project/31/interface/api/384
export function submitQa (data) {
    return request({
        url: '/api/case/submitQa',
        method: 'post',
		headers: {
		    'Content-Type': 'application/json;charset=UTF-8'
		},
        data:data
    })
}

// 服务单提交质量部
//http://yapi.vbeehive.com:81/project/31/interface/api/389
export function queryOrderDetailHistory (id) {
    return request({
        url: '/api/case/queryOrderDetailHistory',
        method: 'get',
        data:{
            orderDetailId:id,
        }
    })
}

