import request from "../utils/http";

// 查询菜单
export function menu () {
    return request({
        url: '/api/jml/staff/menu',
        method: 'get',
        // blockApiErrer:false,
    })
}

// 拜访提醒-查询当天
//http://yapi.vbeehive.com:81/project/24/interface/api/157
export function queryReportNoticeByDay (query) {
    return request({
        url: '/api/salesReport/queryReportNoticeByDay',
        method: 'get',
        data: query
    })
}

// 拜访提醒-按月查询
//http://yapi.vbeehive.com:81/project/24/interface/api/156
export function queryDayListByDate (query) {
    return request({
        url: '/api/salesReport/queryDayListByDate',
        method: 'get',
        data: query
    })
}


export function factoryMenu () {
    return request({
        url: '/api/jml/staff/factoryMenu',
        method: 'get',
    })
}


export function queryNoticeDay (query) {
    return request({
        url: '/api/factoryNotice/queryNoticeDay',
        method: 'get',
        data: query
    })
}

export function queryNoticeInfoByDay (query) {
    return request({
        url: '/api/factoryNotice/queryNoticeInfoByDay2',
        method: 'get',
        data: query
    })
}

export function queryNoticeInfoByDB (query) {
    return request({
        url: '/api/factoryNotice/queryNoticeInfoByDB',
        method: 'get',
        data: query
    })
}