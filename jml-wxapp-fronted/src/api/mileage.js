import request from "../utils/http";
// 分页
// http://yapi.vbeehive.com:81/project/24/interface/api/97
export function queryPageData (data) {
    return request({
        url: '/api/mileageCer/queryMileageCerPage',
        method: 'get',
        data:data
    })
}

// 不分页
// http://yapi.vbeehive.com:81/project/24/interface/api/134
// export function queryListData (data) {
//     return request({
//         url: '/api/salesReport/queryListBy',
//         method: 'get',
//         data:data
//     })
// }

// 新增/修改
// http://yapi.vbeehive.com:81/project/24/interface/api/94
export function saveEdit (data) {
    return request({
        url: '/api/mileageCer/saveMileageCer',
        method: 'post',
        data:data
    })
}


// 详细
// http://yapi.vbeehive.com:81/project/24/interface/api/100
export function queryDetails (id) {
    return request({
        url: '/api/mileageCer/queryDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}


// 删除
// http://yapi.vbeehive.com:81/project/24/interface/api/109
export function deleteMileageCer (id) {
    return request({
        url: '/api/mileageCer/deleteMileageCer',
        method: 'get',
        data:{id}
    })
}

