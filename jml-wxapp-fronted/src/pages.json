{
	"easycom": {
		// 注意一定要放在custom里，否则无效，https://ask.dcloud.net.cn/question/131175
		"custom": {
			"^u--(.*)": "uview-plus/components/u-$1/u-$1.vue",
			"^up-(.*)": "uview-plus/components/u-$1/u-$1.vue",
			"^u-([^-].*)": "uview-plus/components/u-$1/u-$1.vue"
		}
	},
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/user/login",
			"name":"login",
			"aliasPath":"/login",
			"meta":{
				"workAuth": false
			},
			"style": {
				"navigationBarTitleText": "登录"
			}
		},
		{
			"path": "pages/user/changePwd",
			"name":"changePwd",
			"aliasPath":"/changePwd",
			"meta":{
				"workAuth": true
			},
			"style": {
				"navigationBarTitleText": "修改密码"
			}
		},
		{
		    "path" : "pages/home",
			"name": "home",
			"aliasPath": "/home",
			"meta":{
				"workAuth": true
			},
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "工作台",
				"navigationStyle":"custom"
		    }
		},
		{
		    "path" : "pages/test",
			"name": "test",
			"aliasPath": "/test",
			"meta":{
				"workAuth": false
			},
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "测试",
				"navigationStyle":"custom"
		    }
		},
		{
		    "path" : "pages/company/Index",
			"name": "company",
			"aliasPath": "/company",
			"meta":{
				"workAuth": true
			},
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "公司列表",
				"enablePullDownRefresh": false
				// "navigationStyle":"custom"
		    }
		},
		{
		    "path" : "pages/company/Edit",
			"name": "companyEdit",
			"aliasPath": "/companyEdit",
			"meta":{
				"workAuth": true,
				"title": "公司"
			},
		    "style" :{
		        "navigationBarTitleText": "公司",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/company/Detailed",
			"name": "companyInfo",
			"aliasPath": "/companyInfo",
			"meta":{
				"workAuth": true,
				"title": "公司信息详情"
			},
		    "style" :{
		        "navigationBarTitleText": "公司信息详情",
				"enablePullDownRefresh": false
		    }
		},
		
		{
		    "path" : "pages/contact/Index",
			"name": "contact",
			"aliasPath": "/contact",
			"meta":{
				"workAuth": true
			},
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "联系人列表",
				"enablePullDownRefresh": false
				// "navigationStyle":"custom"
		    }
		},
		{
		    "path" : "pages/contact/Edit",
			"name": "contactEdit",
			"aliasPath": "/contactEdit",
			"meta":{
				"workAuth": true,
				"title": "联系人"
			},
		    "style" :{
		        "navigationBarTitleText": "联系人",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/contact/Detailed",
			"name": "contactInfo",
			"aliasPath": "/contactInfo",
			"meta":{
				"workAuth": true,
				"title": "联系人详情"
			},
		    "style" :{
		        "navigationBarTitleText": "联系人详情",
				"enablePullDownRefresh": false
		    }
		},
		// 地址
		{
		    "path" : "pages/address/Detailed",
			"name": "addressInfo",
			"aliasPath": "/addressInfo",
			"meta":{
				"workAuth": true,
				"title": "公司地址详情"
			},
		    "style" :{
		        "navigationBarTitleText": "公司地址详情",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/address/Edit",
			"name": "addressEdit",
			"aliasPath": "/addressEdit",
			"meta":{
				"workAuth": true,
				"title": "公司地址"
			},
		    "style" :{
		        "navigationBarTitleText": "公司地址",
				"enablePullDownRefresh": false
		    }
		},
		{
			"path": "pages/index/index",
			"name":"index",
			"aliasPath":"/index",
			"meta":{
				"workAuth": false
			},
			"style": {
				"navigationBarTitleText": "登录"
			}
		},
		{
		   "path": "pages/workreport/Index",
		   "name":"workReport",
		   "aliasPath":"/workReport",
			"meta":{
				"workAuth": true
			},
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "工作报告",
				"enablePullDownRefresh": false
				// "navigationStyle":"custom"
		    }
		},
		{
		    "path" : "pages/workreport/Edit",
			"name": "workreportEdit",
			"aliasPath": "/workreportEdit",
			"meta":{
				"workAuth": true,
				"title": "工作报告"
			},
		    "style" :{
		        "navigationBarTitleText": "工作报告",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/workreport/Detailed",
			"name": "workreportInfo",
			"aliasPath": "/workreportInfo",
			"meta":{
				"workAuth": true,
				"title": "工作报告详情"
			},
		    "style" :{
		        "navigationBarTitleText": "工作报告详情",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/workreport/SignedReport",
			"name": "signedReport",
			"aliasPath": "/signedReport",
			"meta":{
				"workAuth": true,
				"title": "我的签到报告"
			},
		    "style" :{
		        "navigationBarTitleText": "我的签到报告",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/director/Index",
			"name": "director",
			"aliasPath": "/director",
			"meta":{
				"workAuth": true,
				"title": "经理报告"
			},
		    "style" :{
		        "navigationBarTitleText": "经理报告",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/director/list",
			"name": "directolist",
			"aliasPath": "/directolist",
			"meta":{
				"workAuth": true,
				"title": "经理报告"
			},
		    "style" :{
		        "navigationBarTitleText": "经理报告",
				"enablePullDownRefresh": false
				
		    }
		},
		{
		    "path" : "pages/complaint/Index",
			"name": "complaint",
			"aliasPath": "/complaint",
			"meta":{
				"workAuth": true
			},
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "客服服务列表"
				// "navigationStyle":"custom"
		    }
		},
		{
		    "path" : "pages/complaint/ApproveList",
			"name": "complaintApprove",
			"aliasPath": "/complaintApprove",
			"meta":{
				"workAuth": true
			},
			"params": {
			      "paramName": "value"
			    },
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "客服服务审批列表"
				// "navigationStyle":"custom"
		    }
		},
		{
		    "path" : "pages/complaint/Edit",
			"name": "complaintEdit",
			"aliasPath": "/complaintEdit",
			"meta":{
				"workAuth": true,
				"title": "客服服务"
			},
		    "style" :{
		        "navigationBarTitleText": "客服服务",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/complaint/Detailed",
			"name": "complaintInfo",
			"aliasPath": "/complaintInfo",
			"meta":{
				"workAuth": true,
				"title": "客服服务详情"
			},
		    "style" :{
		        "navigationBarTitleText": "客服服务详情",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/mileage/Index",
			"name": "mileage",
			"aliasPath": "/mileage",
			"meta":{
				"workAuth": true,
				"title": "里程认证列表"
			},
		    "style" :{
		        "navigationBarTitleText": "里程认证列表",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/mileage/Detailed",
			"name": "mileageInfo",
			"aliasPath": "/mileageInfo",
			"meta":{
				"workAuth": true,
				"title": "里程认证详情"
			},
		    "style" :{
		        "navigationBarTitleText": "里程认证详情",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/internalTicket/Index",
			"name": "internalTicket",
			"aliasPath": "/internalTicket",
			"meta":{
				"workAuth": true
			},
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "内部工单列表"
				// "navigationStyle":"custom"
		    }
		},
		{
		    "path" : "pages/internalTicket/Edit",
			"name": "internalTicketEdit",
			"aliasPath": "/internalTicketEdit",
			"meta":{
				"workAuth": true
			},
		    "style" :{
		        "navigationBarTitleText": "内部工单",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/internalTicket/Detailed",
			"name": "internalTicketInfo",
			"aliasPath": "/internalTicketInfo",
			"meta":{
				"workAuth": true,
				"title": "内部工单详情"
			},
		    "style" :{
		        "navigationBarTitleText": "内部工单详情",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/lead/Index",
			"name": "lead",
			"aliasPath": "/lead",
			"meta":{
				"workAuth": true
			},
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "线索列表",
				"enablePullDownRefresh": false
				// "navigationStyle":"custom"
		    }
		},
		{
		    "path" : "pages/lead/Edit",
			"name": "leadEdit",
			"aliasPath": "/leadEdit",
			"meta":{
				"workAuth": true,
				"title": "线索"
			},
		    "style" :{
		        "navigationBarTitleText": "线索",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/lead/Detailed",
			"name": "leadInfo",
			"aliasPath": "/leadInfo",
			"meta":{
				"workAuth": true,
				"title": "线索详情"
			},
		    "style" :{
		        "navigationBarTitleText": "线索详情",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/project/Index",
			"name": "project",
			"aliasPath": "/project",
			"meta":{
				"workAuth": true
			},
		    "style" :                                                                                    
		    {
		        "navigationBarTitleText": "项目列表",
				"enablePullDownRefresh": false
				// "navigationStyle":"custom"
		    }
		},
		{
		    "path" : "pages/project/Detailed",
			"name": "projectInfo",
			"aliasPath": "/projectInfo",
			"meta":{
				"workAuth": true,
				"title": "项目详情"
			},
		    "style" :{
		        "navigationBarTitleText": "项目详情",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/project/Edit",
			"name": "projectEdit",
			"aliasPath": "/projectEdit",
			"meta":{
				"workAuth": true,
				"title": "项目"
			},
		    "style" :{
		        "navigationBarTitleText": "项目",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/opportunity/Index",
			"name": "opp",
			"aliasPath": "/opp",
			"meta":{
				"workAuth": true
			},
		    "style" :{
		        "navigationBarTitleText": "业务机会列表",
				"enablePullDownRefresh": false
				// "navigationStyle":"custom"
		    }
		},
		{
		    "path" : "pages/opportunity/Detailed",
			"name": "oppInfo",
			"aliasPath": "/oppInfo",
			"meta":{
				"workAuth": true,
				"title": "业务机会详情"
			},
		    "style" :{
		        "navigationBarTitleText": "业务机会详情",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/opportunity/Edit",
			"name": "oppEdit",
			"aliasPath": "/oppEdit",
			"meta":{
				"workAuth": true,
				"title": "业务机会"
			},
		    "style" :{
		        "navigationBarTitleText": "业务机会",
				"enablePullDownRefresh": false
		    }
		},
		
		{
		    "path" : "pages/opp_product/Index",
			"name": "opp_product",
			"aliasPath": "/opp_product",
			"meta":{
				"workAuth": true
			},
		    "style" :{
		        "navigationBarTitleText": "业务机会产品列表",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/opp_product/Detailed",
			"name": "opp_productInfo",
			"aliasPath": "/opp_productInfo",
			"meta":{
				"workAuth": true,
				"title": "业务机会产品详情"
			},
		    "style" :{
		        "navigationBarTitleText": "业务机会产品详情",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/opp_product/Edit",
			"name": "opp_productEdit",
			"aliasPath": "/opp_productEdit",
			"meta":{
				"workAuth": true,
				"title": "业务机会产品"
			},
		    "style" :{
		        "navigationBarTitleText": "业务机会产品",
				"enablePullDownRefresh": false
		    }
		},
		
		{
		    "path" : "pages/cost/Index",
			"name": "cost",
			"aliasPath": "/cost",
			"meta":{
				"workAuth": true
			},
		    "style" :{
		        "navigationBarTitleText": "费用报销",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/cost/Detailed",
			"name": "costInfo",
			"aliasPath": "/costInfo",
			"meta":{
				"workAuth": true,
				"title": "费用详情"
			},
		    "style" :{
		        "navigationBarTitleText": "费用详情",
				"enablePullDownRefresh": false
		    }
		},
		{
		    "path" : "pages/cost/Edit",
			"name": "costEdit",
			"aliasPath": "/costEdit",
			"meta":{
				"workAuth": true,
				"title": "费用详情"
			},
		    "style" :{
		        "navigationBarTitleText": "费用详情",
				"enablePullDownRefresh": false
		    }
		}
    ],
	"subPackages": [{
		"root": "factory",
		"pages": [
			{
				"path": "pages/paintLinProd/Index",
				"name":"paintLinProd",
				"aliasPath":"/paintLinProd",
				"meta":{
					"workAuth": true,
					"title": "烤漆线-生产工艺单"
				},
				"style": {
					"navigationBarTitleText": "烤漆线-生产工艺单"
				}
			},
			{
			    "path" : "pages/paintLinProd/Edit",
				"name": "paintLinProdEdit",
				"aliasPath": "/paintLinProdEdit",
				"meta":{
					"workAuth": true,
					"title": "烤漆线-生产工艺单"
				},
			    "style" :{
			        "navigationBarTitleText": "烤漆线-生产工艺单",
					"enablePullDownRefresh": false
			    }
			},
			{
			    "path" : "pages/paintLinProd/Detailed",
				"name": "paintLinProdInfo",
				"aliasPath": "/paintLinProdInfo",
				"meta":{
					"workAuth": true,
					"title": "烤漆线-生产工艺单"
				},
			    "style" :{
			        "navigationBarTitleText": "烤漆线-生产工艺单",
					"enablePullDownRefresh": false
			    }
			},
			{
				"path": "pages/pressLineReport/Index",
				"name":"pressLineReport",
				"aliasPath":"/pressLineReport",
				"meta":{
					"workAuth": true,
					"title": "压贴线-日报表"
				},
				"style": {
					"navigationBarTitleText": "压贴线-日报表"
				}
			},
			{
			    "path" : "pages/pressLineReport/Edit",
				"name": "pressLineReportEdit",
				"aliasPath": "/pressLineReportEdit",
				"meta":{
					"workAuth": true,
					"title": "压贴线-日报表"
				},
			    "style" :{
			        "navigationBarTitleText": "压贴线-日报表",
					"enablePullDownRefresh": false
			    }
			},
			{
			    "path" : "pages/pressLineReport/Detailed",
				"name": "pressLineReportInfo",
				"aliasPath": "/pressLineReportInfo",
				"meta":{
					"workAuth": true,
					"title": "压贴线-日报表"
				},
			    "style" :{
			        "navigationBarTitleText": "压贴线-日报表",
					"enablePullDownRefresh": false
			    }
			},
			{
				"path": "pages/printingLineReport/Index",
				"name":"printingLineReport",
				"aliasPath":"/printingLineReport",
				"meta":{
					"workAuth": true,
					"title": "打印线-日报表"
				},
				"style": {
					"navigationBarTitleText": "打印线-日报表"
				}
			},
			{
			    "path" : "pages/printingLineReport/Edit",
				"name": "printingLineReportEdit",
				"aliasPath": "/printingLineReportEdit",
				"meta":{
					"workAuth": true,
					"title": "打印线-日报表"
				},
			    "style" :{
			        "navigationBarTitleText": "打印线-日报表",
					"enablePullDownRefresh": false
			    }
			},
			{
			    "path" : "pages/printingLineReport/Detailed",
				"name": "printingLineReportInfo",
				"aliasPath": "/printingLineReportInfo",
				"meta":{
					"workAuth": true,
					"title": "打印线-日报表"
				},
			    "style" :{
			        "navigationBarTitleText": "打印线-日报表",
					"enablePullDownRefresh": false
			    }
			},
			
			{
				"path": "pages/pressLineProduction/Index",
				"name":"pressLineProduction",
				"aliasPath":"/pressLineProduction",
				"meta":{
					"workAuth": true
				},
				"style": {
					"navigationBarTitleText": "压贴线-生产工艺单"
				}
			},
			{
			    "path" : "pages/pressLineProduction/Edit",
				"name": "pressLineProductionEdit",
				"aliasPath": "/pressLineProductionEdit",
				"meta":{
					"workAuth": true
				},
			    "style" :{
			        "navigationBarTitleText": "压贴线-生产工艺单",
					"enablePullDownRefresh": false
			    }
			},
			{
			    "path" : "pages/pressLineProduction/Detailed",
				"name": "pressLineProductionInfo",
				"aliasPath": "/pressLineProductionInfo",
				"meta":{
					"workAuth": true
				},
			    "style" :{
			        "navigationBarTitleText": "压贴线-生产工艺单",
					"enablePullDownRefresh": false
			    }
			},
			
			{
				"path": "pages/sandingBelt/Index",
				"name":"sandingBelt",
				"aliasPath":"/sandingBelt",
				"meta":{
					"workAuth": true
				},
				"style": {
					"navigationBarTitleText": "砂带更换记录"
				}
			},
			{
			    "path" : "pages/sandingBelt/Edit",
				"name": "sandingBeltEdit",
				"aliasPath": "/sandingBeltEdit",
				"meta":{
					"workAuth": true
				},
			    "style" :{
			        "navigationBarTitleText": "砂带更换记录",
					"enablePullDownRefresh": false
			    }
			},
			{
			    "path" : "pages/sandingBelt/Detailed",
				"name": "sandingBeltInfo",
				"aliasPath": "/sandingBeltInfo",
				"meta":{
					"workAuth": true
				},
			    "style" :{
			        "navigationBarTitleText": "砂带更换记录",
					"enablePullDownRefresh": false
			    }
			},
			
			{
				"path": "pages/perDeviceMnt/Index",
				"name":"perDeviceMnt",
				"aliasPath":"/perDeviceMnt",
				"meta":{
					"workAuth": true
				},
				"style": {
					"navigationBarTitleText": "第三方维保"
				}
			},
			{
			    "path" : "pages/perDeviceMnt/Edit",
				"name": "perDeviceMntEdit",
				"aliasPath": "/perDeviceMntEdit",
				"meta":{
					"workAuth": true
				},
			    "style" :{
			        "navigationBarTitleText": "第三方维保",
					"enablePullDownRefresh": false
			    }
			},
			{
			    "path" : "pages/perDeviceMnt/Detailed",
				"name": "perDeviceMntInfo",
				"aliasPath": "/perDeviceMntInfo",
				"meta":{
					"workAuth": true
				},
			    "style" :{
			        "navigationBarTitleText": "第三方维保",
					"enablePullDownRefresh": false
			    }
			},
			
			{
				"path": "pages/nitrogenLog/Index",
				"name":"nitrogenLog",
				"aliasPath":"/nitrogenLog",
				"meta":{
					"workAuth": true
				},
				"style": {
					"navigationBarTitleText": "氮气登记表"
				}
			},
			{
			    "path" : "pages/nitrogenLog/Edit",
				"name": "nitrogenLogEdit",
				"aliasPath": "/nitrogenLogEdit",
				"meta":{
					"workAuth": true
				},
			    "style" :{
			        "navigationBarTitleText": "氮气登记表",
					"enablePullDownRefresh": false
			    }
			},
			{
			    "path" : "pages/nitrogenLog/Detailed",
				"name": "nitrogenLogInfo",
				"aliasPath": "/nitrogenLogInfo",
				"meta":{
					"workAuth": true
				},
			    "style" :{
			        "navigationBarTitleText": "氮气登记表",
					"enablePullDownRefresh": false
			    }
			},
			
			{
				"path": "pages/partsPurchase/Index",
				"name":"partsPurchase",
				"aliasPath":"/partsPurchase",
				"meta":{
					"workAuth": true
				},
				"style": {
					"navigationBarTitleText": "零配件采购"
				}
			},
			{
			    "path" : "pages/partsPurchase/Edit",
				"name": "partsPurchaseEdit",
				"aliasPath": "/partsPurchaseEdit",
				"meta":{
					"workAuth": true
				},
			    "style" :{
			        "navigationBarTitleText": "零配件采购",
					"enablePullDownRefresh": false
			    }
			},
			{
			    "path" : "pages/partsPurchase/Detailed",
				"name": "partsPurchaseInfo",
				"aliasPath": "/partsPurchaseInfo",
				"meta":{
					"workAuth": true
				},
			    "style" :{
			        "navigationBarTitleText": "零配件采购",
					"enablePullDownRefresh": false
			    }
			},
			
			{
				"path": "pages/deviceRepair/Index",
				"name":"deviceRepair",
				"aliasPath":"/deviceRepair",
				"meta":{
					"workAuth": true
				},
				"style": {
					"navigationBarTitleText": "设备维修"
				}
			},
			{
			    "path" : "pages/deviceRepair/Edit",
				"name": "deviceRepairEdit",
				"aliasPath": "/deviceRepairEdit",
				"meta":{
					"workAuth": true
				},
			    "style" :{
			        "navigationBarTitleText": "设备维修",
					"enablePullDownRefresh": false
			    }
			},
			{
			    "path" : "pages/deviceRepair/Detailed",
				"name": "deviceRepairInfo",
				"aliasPath": "/deviceRepairInfo",
				"meta":{
					"workAuth": true
				},
			    "style" :{
			        "navigationBarTitleText": "设备维修",
					"enablePullDownRefresh": false
			    }
			},
			
			{
				"path": "pages/jobAssignment/Index",
				"name":"jobAssignment",
				"aliasPath":"/jobAssignment",
				"meta":{
					"workAuth": true
				},
				"style": {
					"navigationBarTitleText": "岗位分工"
				}
			},
			{
			    "path" : "pages/jobAssignment/Edit",
				"name": "jobAssignmentEdit",
				"aliasPath": "/jobAssignmentEdit",
				"meta":{
					"workAuth": true
				},
			    "style" :{
			        "navigationBarTitleText": "岗位分工",
					"enablePullDownRefresh": false
			    }
			},
			{
			    "path" : "pages/jobAssignment/Detailed",
				"name": "jobAssignmentInfo",
				"aliasPath": "/jobAssignmentInfo",
				"meta":{
					"workAuth": true
				},
			    "style" :{
			        "navigationBarTitleText": "岗位分工",
					"enablePullDownRefresh": false
			    }
			},
			
			{
				"path": "pages/dailyInspection/Index",
				"name":"dailyInspection",
				"aliasPath":"/dailyInspection",
				"meta":{
					"workAuth": true
				},
				"style": {
					"navigationBarTitleText": "每日巡检报告"
				}
			},
			{
			    "path" : "pages/dailyInspection/Edit",
				"name": "dailyInspectionEdit",
				"aliasPath": "/dailyInspectionEdit",
				"meta":{
					"workAuth": true
				},
			    "style" :{
			        "navigationBarTitleText": "每日巡检报告",
					"enablePullDownRefresh": false
			    }
			},
			{
			    "path" : "pages/dailyInspection/Detailed",
				"name": "dailyInspectionInfo",
				"aliasPath": "/dailyInspectionInfo",
				"meta":{
					"workAuth": true
				},
			    "style" :{
			        "navigationBarTitleText": "每日巡检报告",
					"enablePullDownRefresh": false
			    }
			},

            {
				"path": "pages/naturalGasLog/Index",
				"name":"naturalGasLog",
				"aliasPath":"/naturalGasLog",
				"meta":{
					"workAuth": true
				},
				"style": {
					"navigationBarTitleText": "天然气登记表"
				}
			},
			{
			    "path" : "pages/naturalGasLog/Edit",
				"name": "naturalGasLogEdit",
				"aliasPath": "/naturalGasLogEdit",
				"meta":{
					"workAuth": true
				},
			    "style" :{
			        "navigationBarTitleText": "天然气登记表",
					"enablePullDownRefresh": false
			    }
			},
			{
			    "path" : "pages/naturalGasLog/Detailed",
				"name": "naturalGasLogInfo",
				"aliasPath": "/naturalGasLogInfo",
				"meta":{
					"workAuth": true
				},
			    "style" :{
			        "navigationBarTitleText": "天然气登记表",
					"enablePullDownRefresh": false
			    }
			},
            {
				"path": "pages/electricityLog/Index",
				"name":"electricityLog",
				"aliasPath":"/electricityLog",
				"meta":{
					"workAuth": true
				},
				"style": {
					"navigationBarTitleText": "电用量登记表"
				}
			},
			{
			    "path" : "pages/electricityLog/Edit",
				"name": "electricityLogEdit",
				"aliasPath": "/electricityLogEdit",
				"meta":{
					"workAuth": true
				},
			    "style" :{
			        "navigationBarTitleText": "电用量登记表",
					"enablePullDownRefresh": false
			    }
			},
			{
			    "path" : "pages/electricityLog/Detailed",
				"name": "electricityLogInfo",
				"aliasPath": "/electricityLogInfo",
				"meta":{
					"workAuth": true
				},
			    "style" :{
			        "navigationBarTitleText": "电用量登记表",
					"enablePullDownRefresh": false
			    }
			}
		]
	}],
	"globalStyle": {
		"navigationBarTextStyle": "black",//导航栏标题颜色及状态栏前景颜色
		"navigationBarTitleText": "加载中",//导航栏标题文字内容(全局)
		"navigationBarBackgroundColor": "#fff",//导航栏背景色
		"backgroundColor": "#F8F8F8",//窗口背景色(页面下拉加载的背景色)
		// "enablePullDownRefresh":true,//是否开启下拉刷新
		"backgroundTextStyle":"light"//加载的颜色变为白色，还有一种是黑色
		// "transparentTitle":"always"
	}
	// "topWindow": {
	//     "path": "responsive/top-window.vue",
	//     "style": {
	//       "height": "1px"
	//     },
	// 	"matchMedia": {
	// 	  "minWidth": 1 //生效条件，当窗口宽度大于768px时显示
	// 	}
	// }
}
