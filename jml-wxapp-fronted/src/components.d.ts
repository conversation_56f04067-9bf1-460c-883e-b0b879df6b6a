/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    DragBox: typeof import('./components/DragBox.vue')['default']
    FloatingWindow: typeof import('./components/FloatingWindow.vue')['default']
    FooterBtn: typeof import('./components/FooterBtn.vue')['default']
    LoadSelector: typeof import('./components/LoadSelector.vue')['default']
    MileageMap: typeof import('./components/MileageMap.vue')['default']
    ProgressStatus: typeof import('./components/ProgressStatus.vue')['default']
    RelatedList: typeof import('./components/RelatedList.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScanCodeInput: typeof import('./factory/components/scanCodeInput.vue')['default']
    SelectMap: typeof import('./components/SelectMap.vue')['default']
    Selector: typeof import('./components/Selector.vue')['default']
    Time: typeof import('./factory/components/time.vue')['default']
    ToolsPicker: typeof import('./factory/components/toolsPicker.vue')['default']
    UserPicker: typeof import('./factory/components/userPicker.vue')['default']
  }
}
