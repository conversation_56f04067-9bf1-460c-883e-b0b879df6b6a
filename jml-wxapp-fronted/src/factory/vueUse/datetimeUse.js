
import dayjs from "dayjs";
export function useDateTime(info,modelType = 'date') {
	const dateTimeShow = ref(false)
	const field = ref('date')
	const currentTime = ref(Date.now());
	const dateMode = ref(modelType)
	const onDateTimeShow = (modelField,type) =>{
		debugger
	    field.value = modelField
		if(type) {
			dateMode.value = type;
			if(type=='time') currentTime.value = `${dayjs().format(formatter.time)}`;
			else if(type) currentTime.value = `${dayjs().format(formatter[type])}`;
			else currentTime.value = Date.now();
		}
	    dateTimeShow.value = true;
	}
	const formatter = {
		'date':"YYYY-MM-DD",
		'time':"HH:mm",
		'datetime':"YYYY-MM-DD HH:mm:ss",
	}
	const onDateTimeConfirm = ({mode,value}) => {
		switch (mode) {
			case 'time':
				info.value[field.value] = value;
				break
			default:
				info.value[field.value] = `${dayjs(value).format(formatter[mode])}`;
				break
		}
		debugger
	    // info.value[field.value] = `${currentDate.value.join('-')} ${currentTime.value.join(':')}`
	    dateTimeShow.value = false
	};
	const minDate = ref(dayjs().add(-1, 'year').format('YYYY-MM-DD'))
	const maxDate = ref(dayjs().format('YYYY-MM-DD'))
    return {
        dateTimeShow,field,currentTime,onDateTimeShow,onDateTimeConfirm,dateMode,minDate,maxDate
    }
}