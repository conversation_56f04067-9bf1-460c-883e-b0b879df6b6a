
import dayjs from "dayjs";
import { reactive } from "vue";
export function useRangeTime(info) {
	const rangeField = reactive({
		startTime : "startTime",
		endTime : "endTime"
	})
	const rangeTime = ref(`${dayjs().format("HH:mm")}-23:59`);
	const rangeTimeShow = ref(false)
	const onRangeTimeShow = (startTime = "startTime",endTime = "endTime") =>{
	    rangeField.startTime = startTime
		rangeField.endTime = endTime
	    rangeTimeShow.value = true;
	}

	const onRangeTimeConfirm = (data) => {
		if(data.values.length==4 && data.valuesStr1){
			let rangeInfo = data.valuesStr1?.split('-')
			if(rangeInfo.length>0){
				info.value[rangeField.startTime] = rangeInfo[0]
				info.value[rangeField.endTime] = rangeInfo[1]
			}
			rangeTimeShow.value = false
		}else{
			return false
		}
	};
	
	
    return {
        rangeTime,rangeTimeShow,rangeField,onRangeTimeShow,onRangeTimeConfirm
    }
}