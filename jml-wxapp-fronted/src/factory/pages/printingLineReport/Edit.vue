<template>
    <div class="page pd100">
        <div class="card_info">
			<u--form labelPosition="top" labelWidth="auto" ref="form1" >
            <div class="card">				
					<u-form-item label="日期"  prop="date">
						<u--input v-model="info.date"  class="int" readonly  placeholder="日期"  />
						<template #right><u-icon name="calendar" size="22" @click="onDateTimeShow('date','date')"></u-icon></template>
					</u-form-item>
					
					<u-form-item label="开始/结束时间" required prop="startTime">
						<view style="display: flex; width: 100%;">
							<view class="start int-time" style="flex: 1;">
								<u--input inputAlign="left" v-model="info.startTime"  class="int" readonly  placeholder="--:--"  >
									<template #suffix>
										<u-icon name="clock" size="20" @click="onRangeTimeShow('startTime','endTime')"></u-icon>
									</template>
								</u--input>
							</view>
							<u-icon name="minus"  size="22"></u-icon>
							
							<view class="end int-time" style="flex: 1;">
								<u--input inputAlign="right" v-model="info.endTime"  class="int" readonly  placeholder="--:--"  >
									<template #suffix>
										<u-icon name="clock" size="20" @click="onRangeTimeShow('startTime','endTime')"></u-icon>
									</template>
								</u--input>
							</view>
							
						</view>
					</u-form-item>
					
					<!-- <u-form-item label="工单号" prop="prodOrderNo" required>
						<u--input  v-model="info.prodOrderNo"  class="int"   placeholder="请输入"  />
					</u-form-item> -->
					<u-form-item label="工单号" required prop="workNo">
						<scan-code-input  v-model="info.workNo"  />
					</u-form-item>
					<!-- <u-form-item label="打印数量" required prop="printQty">
						<u--input type="number"  v-model="info.printQty"  class="int"   placeholder="请输入"  />
					</u-form-item>
					<u-form-item label="人数" prop="peopleNum" required>
						<u--input type="number" v-model="info.peopleNum" class="int"   placeholder="请输入"  />
					</u-form-item>
					<u-form-item label="型号" required prop="model">
						<u--input v-model="info.model" class="int"   placeholder="请输入"  />
					</u-form-item>
					
					<u-form-item label="尺寸 " required prop="size">
						<u--input  v-model="info.size" class="int"   placeholder="请输入"  />
					</u-form-item>
					<u-form-item label="表面工艺" required prop="surfaceProcess">
						<zxz-uni-data-select  placeholder="请选择" v-model="info.surfaceProcess" :localdata="dict['surface_process']"></zxz-uni-data-select>
					</u-form-item> -->
					
					<u-form-item label="备注" prop="remark" >
						<u--textarea :count="!!info.remark" maxlength="800"  v-model="info.remark"  class="int"   placeholder="备注"  />
					</u-form-item>
					<u-form-item label="打印总数" prop="printQty">
						<u--input type="number" v-model="info.printQty" readonly disabled class="int"   placeholder="自动计算"  />
					</u-form-item>
				</div>

				<div class="card mt10 pd10">
					<div class="card_head">
						<div class="card_head_tit2">打印线明细</div>
					</div>
					
					<div class="detail_list">
						<uni-table border stripe emptyText="暂无更多数据">
							<!-- 表头行 -->
							<uni-tr>
								<uni-th width="80" align="center">物料号</uni-th>
                                <uni-th width="120" align="center">良品数量</uni-th>
                                <uni-th width="120" align="center">不良品数量</uni-th>
                                <uni-th width="120" align="center">不良说明</uni-th>
                                <uni-th width="120" align="center">打印数量</uni-th>
                                <uni-th width="80" operate align="center">操作</uni-th>
							</uni-tr>
							<!-- 表格数据行 -->
							<uni-tr v-for="(detail,i) in info.printingLineReportDetailList" :key="i">
								<uni-td align="center"> {{detail.material}} </uni-td>
                                <uni-td align="center"> {{detail.goodQty}}</uni-td>
                                <uni-td align="center"> {{detail.defectiveQty}}</uni-td>
                                <uni-td align="center"> {{detail.defectiveDesc}}</uni-td>
                                <uni-td align="center"> {{detail.printQty}}</uni-td>
								<uni-td  operate align="center">
									<view class="uni-group">
										<u-button class="uni-button" @click="handleShow(detail,i)" color="#849EB2" :plain="true" :hairline="true" size="mini" type="primary">查看</u-button>
										<div v-if="!detail.id">
										    <u-button class="uni-button"  @click="deleteRow(i)" :plain="true" :hairline="true" size="mini" type="primary">删除</u-button>
										</div>
									</view>
								</uni-td>
							</uni-tr>
						</uni-table>
					    <div @click="handleShow({material:''},info.printingLineReportDetailList.length)"   class="detail_add">
							<u-icon name="plus" color="#849EB2" size="12"></u-icon>
							添加打印线明细
						</div>
					</div>
					
				</div>

			</u--form>
        </div>

		<u-popup :show="showAddPop" :round="10" closeable mode="bottom" @close="showAddPop = false">
			<div class="addInfo_pop">
				<div class="card">
					<div class="card_head">
						<div class="card_head_tit">添加打印线明细</div>
					</div>
					<scroll-view style="height: 450px; max-height: 70vh;" scroll-y>
						<div class="card_from">
							<u--form labelPosition="top" labelWidth="auto"
								ref="popform" 
								errorType="toast"
								:model="popInfo"
								:rules="{
									material:[{ required: true, message: '请输入物料号'}],
									goodQty:[{ required: true, message: '请输入良品数量'}],
								}"
							>
								<!-- <u-form-item label="人数" prop="peopleNum"  >
									<u--input type="number" v-model.number="popInfo.peopleNum"  placeholder="请输入"  />
								</u-form-item> -->
								<u-form-item label="物料号" required prop="material"  >
									<!-- <u--input  v-model="popInfo.material"   placeholder="请输入"  /> -->
									<scan-code-input  v-model="popInfo.material"  />
								</u-form-item>
                                <u-form-item label="物料号备注" prop="remark" >
									<u--textarea :count="!!popInfo.remark" maxlength="150"  v-model="popInfo.remark"  class="int"   placeholder="请填写"  />
								</u-form-item>
                                <u-form-item label="良品数量" required prop="goodQty"  >
									<u--input type="number" v-model.number="popInfo.goodQty"  placeholder="请输入"  @change="calculatePrintQty" />
								</u-form-item>
                                <u-form-item label="不良品数量" prop="defectiveQty"  >
									<u--input type="number" v-model.number="popInfo.defectiveQty"  placeholder="请输入"  @change="calculatePrintQty" />
								</u-form-item>
                                <u-form-item label="不良说明" prop="defectiveDesc"  >
									<u--input  v-model="popInfo.defectiveDesc"   placeholder="请输入"  />
								</u-form-item>
                                <u-form-item label="打印数量" prop="printQty"  >
									<u--input type="number" v-model.number="popInfo.printQty" readonly disabled  placeholder="自动计算=良品数量 +不良品数量"  />
								</u-form-item>
							</u--form>
						</div>
					</scroll-view>
				</div>
				<footer-btn @onconfirm="handconfirm" @oncancel="showAddPop = false"  confirm_btn_text="确定"></footer-btn>
		    </div>
		</u-popup>

		<hbxw-time-range-picker
			v-model:visible="rangeTimeShow"
			@cancel="rangeTimeShow= false"
			isShowType
			:isShowSecond='false'
			:value="rangeTime" 
			delimiter="point"
			@sure="onRangeTimeConfirm"
		/>
		<u-datetime-picker
			:show="dateTimeShow"
			v-model="currentTime"
			confirmColor="#849EB2"
			closeOnClickOverlay
			@confirm="onDateTimeConfirm"
			@cancel="dateTimeShow = false"
			@close="dateTimeShow = false"
			:mode="dateMode"
		></u-datetime-picker>
        <footer-btn :cancelBtnShow="false"  confirm_btn_text="保存" @onconfirm="save()"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import LoadSelector from '@/components/LoadSelector.vue';
import Selector from '@/components/Selector.vue';
import scanCodeInput from '../../components/scanCodeInput.vue';
import dayjs from "dayjs";
import {queryDetails,saveEdit}  from  '../../api/printingLineReport'
import {queryListByStaff}  from  '@/api/company'
import {useDateTime}  from  '../../vueUse/datetimeUse'
import {useRangeTime}  from  '../../vueUse/rangeTimeUse'
	const prop =  defineProps({
			id: String,
			isComponent: {
				type: Boolean,
				default: false,
			},
			accountId:{
				type: String,
				default: null,
			}
		})
	const routerQuery = prop;
	const appStore = useAppStore();
	const router = useRouter();
	const { userInfo } = useUserStore();

    // 相关字典
    const dictStore = useDictStore()
    const companyList = ref([])
	const util = inject("util");
    const deleteFild = ['accName','createdStaffName','updateStaffName','salesLeadName','marketingName','areaName'];
    const requiredFild = [
        // {key:'name',message:'请填写机会名称',required: true,},
        // {key:'amount',message:'请填写金额',required: true,},
        // {key:'type',message:'请选择机会类型',required: true,},
    ]
    const save = () => {
        for(let index  in requiredFild){ 
            var item =  requiredFild[index]
            if(item.required && (!info.value[item.key] || info.value[item.key]=='')){
            	return uni.showToast({
            		title: item.message,
            		duration: 1500,
            		icon:'none'
            	});
            }
        };
        // let postData = {}
        // for(var key in info.value){
        //     if(!deleteFild.includes(key))
        //         postData[key] = info.value[key];
        // }
        saveEdit(info.value).then((res)=>{
			
            uni.showModal({
            	title: '提示',
            	content: info.value.id ?'修改成功':'保存成功',
            	showCancel:false,
            	success: function (confirmres) {
            		if (confirmres.confirm) {
            			if(prop.isComponent)emit('confirm', res)
            			else{
            				uni.$emit("refresh-printingLineReport", {refresh: true}); 
            				router.back();
            			}
            		} else if (confirmres.cancel) {
            			console.log('用户点击取消');
            		}
            	}
            });
        })
    }
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const info = ref({
		printingLineReportDetailList:[]
    });

    const printQty = computed(() => {
        return info.value?.printingLineReportDetailList.reduce((sum, item) => sum + item.printQty, 0);
    });
    watch(()=>printQty.value, (newVal) => {
        if (newVal) {
            info.value.printQty = newVal
        }
    }, { deep: false , immediate: false });

	const {dateTimeShow,field,currentTime,onDateTimeShow,onDateTimeConfirm,dateMode} = useDateTime(info)
	const {rangeTime,rangeTimeShow,onRangeTimeShow,onRangeTimeConfirm}  = useRangeTime(info)
    const init = async() =>{
		await dictStore.getDictBatch(['surface_process','printing_line_report_detail_size']);
        if(routerQuery.id){
            getInfo()
        }
    }

	const showAddPop = ref(false)
	const popInfo = ref({});
	const deleteRow = (index) => {
		uni.showModal({
			title: '提示',
			content: '确认删除？',
			success: function (res) {
				if (res.confirm) {
					info.value?.printingLineReportDetailList.splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
	let showindex = null;
	const calculatePrintQty = () => {
        nextTick(() => {
            const goodQty = popInfo.value.goodQty || 0;
            const defectiveQty = popInfo.value.defectiveQty || 0;
            popInfo.value.printQty = goodQty + defectiveQty;
        });
	};
	const popform = ref()
	const handconfirm = async () =>{
	    debugger
		try {
			await popform.value.validate();
			console.log('校验通过');
		} catch (err) {
			console.error('校验失败', err);
			return
		}
	    if(showindex || showindex>=0){
	        info.value.printingLineReportDetailList[showindex] = popInfo.value;
	    }else{
	        info.value.printingLineReportDetailList.push(popInfo.value)
	    }
	    showAddPop.value = false
	    popInfo.value = {};
	}
	const handleShow = (data,index) =>{
	    popInfo.value = data;

	    showindex = index
	    showAddPop.value = true
	}
	
    onMounted(() => {
        init()
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
	@import '../../style/common.scss';
</style>