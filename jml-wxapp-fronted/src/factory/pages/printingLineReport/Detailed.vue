<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        {{ info.name }}
						<view> <u-icon name="calendar" :label="info.date" labelSize="12"></u-icon> </view>
                    </div>
                </div>
            </div>
        </div>
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                       基本信息
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
					<div class="item">
					    <div class="left">工单号</div><div class="rigth">{{info.workNo}}</div>
					</div>
					<div class="item">
					    <div class="left">打印总数</div><div class="rigth">{{info.printQty}}</div>
					</div>
					<!-- <div class="item">
					    <div class="left">型号</div><div class="rigth">{{info.model}}</div>
					</div> -->
					<!-- <div class="item">
					    <div class="left">表面工艺</div><div class="rigth">{{info.surfaceProcess}}</div>
					</div>
                    <div class="item">
                        <div class="left">尺寸</div><div class="rigth">{{info.size}}</div>
                    </div>
                    <div class="item">
                        <div class="left">打印数量</div><div class="rigth">{{info.printQty}}</div>
                    </div>
                    <div class="item">
                        <div class="left">人数</div><div class="rigth">{{info.peopleNum}}</div>
                    </div> -->
                   
                    <div class="item">
                        <div class="left">备注</div>
                        <div class="rigth">{{info.remark}}</div>
                    </div>
                </div>
			 </div>
		</div>	 

		<div class="card_info">
			<div class="card">
				<div class="card_head">
					<div class="card_head_tit2" style="margin-top: 0;">
					   打印线明细
					</div>
				</div>
				
				<div class="detail_list">
					<uni-table border stripe emptyText="暂无更多数据">
						<!-- 表头行 -->
						<uni-tr>
							<uni-th width="80" align="center">物料号</uni-th>
							<uni-th width="120" align="center">良品数量</uni-th>
							<uni-th width="120" align="center">不良品数量</uni-th>
                            <uni-th width="120" align="center">不良说明</uni-th>
							<uni-th width="120" align="center">打印数量</uni-th>
							<uni-th width="80" operate align="center">操作</uni-th>
						</uni-tr>
						<!-- 表格数据行 -->
						<uni-tr v-for="(detail,i) in info.printingLineReportDetailList" :key="i">
							<uni-td align="center"> {{detail.material}} </uni-td>
							<uni-td align="center"> {{detail.goodQty}}</uni-td>
							<uni-td align="center"> {{detail.defectiveQty}}</uni-td>
							<uni-td align="center"> {{detail.defectiveDesc}}</uni-td>
							<uni-td align="center"> {{detail.printQty}}</uni-td>
							<uni-td  operate align="center">
								<view class="uni-group">
									<u-button class="uni-button" @click="handleShow(detail,i)" color="#849EB2" :plain="true" :hairline="true" size="mini" type="primary">查看</u-button>
								</view>
							</uni-td>
						</uni-tr>
					</uni-table>
				</div>
			</div>
		</div>
		<div class="card_info">	
			<div class="card">	
				<div class="card_head">
					<div class="card_head_tit2" style="margin-top: 0;">
					   系统信息
					</div>
				</div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.createdTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.updateTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>
		
		<!-- 底部弹出 -->
		<u-popup :show="showPopInfo" :round="10" closeable mode="bottom" @close="showPopInfo = false">
		    <div class="pop_info">
		        <div class="pop_info_tit">打印线详情</div>
		        <div class="card_info">
		            <div class="card">
		                <div class="card_main">
							<!-- <div class="item">
							    <div class="left">人数</div><div class="rigth">{{popinfo.peopleNum}}</div>
							</div> -->
		                    <div class="item">
		                        <div class="left">物料号</div><div class="rigth">{{popinfo.material}}</div>
		                    </div>
		                    <div class="item">
		                        <div class="left">物料号备注</div><div class="rigth">{{popinfo.remark}}</div>
		                    </div>
							<div class="item">
							    <div class="left">良品数量</div><div class="rigth">{{popinfo.goodQty}}</div>
							</div>
		                    <div class="item">
		                        <div class="left">不良品数量</div><div class="rigth">{{popinfo.defectiveQty}}</div>
		                    </div>
		                    <div class="item">
		                        <div class="left">不良说明</div><div class="rigth">{{popinfo.defectiveDesc}}</div>
		                    </div>
		                    <div class="item">
		                        <div class="left">打印数量</div><div class="rigth">{{popinfo.printQty}}</div>
		                    </div>
		                    <div class="item">
		                        <div class="left">创建日期</div><div class="rigth">{{popinfo.createdTime}}</div>
		                    </div>
		                </div>
		            </div>
		        </div>
		    </div>
		</u-popup>

        <footer-btn v-if="hasFactoryPermission('printingLineReport','U')"  :cancelBtnShow="false" @onconfirm="router.push({name: 'printingLineReportEdit',params: {id:routerQuery.id}})" confirm_btn_text="编辑"></footer-btn>
		<floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import {queryDetails}  from  '../../api/printingLineReport'

    const prop =  defineProps(['id'])
    const router = useRouter();
    const util = inject("util");
	const dictStore = useDictStore()
    const routerQuery =  prop;
    const info = ref({
    });
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const init = async() =>{
        getInfo()
    }
	const showPopInfo = ref(false)
	const popinfo = ref({})
	const handleShow = async(info,index) =>{
	    popinfo.value = info;
	    showPopInfo.value = true
	}
	onShow(() => {
		 init()
	})
	onMounted(() => {
	   
	})
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
	@import '@/styles/factory.scss';
	.card_info{
		.card{
			.item{
				padding-bottom: 5px;
				.left{
					line-height: 1.2;
				}
				.rigth{
					text-align: right;
				}
			}
			
			.head_status{
				background: #999 !important;
			}
		}
	}
	
</style>