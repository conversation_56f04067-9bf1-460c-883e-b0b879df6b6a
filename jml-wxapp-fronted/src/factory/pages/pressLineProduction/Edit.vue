<template>
    <div class="page pd100">
        <div class="card_info">
			<u--form labelPosition="top" labelWidth="auto" ref="form1" >
            <div class="card">
                <u-form-item required label="日期"  prop="date">
                    <u--input v-model="info.date"  class="int" readonly  placeholder="日期"  />
                    <template #right><u-icon name="calendar" size="22" @click="onDateTimeShow('date','date')"></u-icon></template>
                </u-form-item>
				<u-form-item required  label="时间" prop="startTime">
					<view style="display: flex; width: 100%;">
						<view class="start int-time" style="flex: 1;">
							<u--input inputAlign="left" v-model="info.startTime"  class="int" readonly  placeholder="开始时间--:--"  >
								<template #suffix>
									<u-icon name="clock" size="20" @click="onRangeTimeShow('startTime','endTime')"></u-icon>
								</template>
							</u--input>
						</view>
						<u-icon name="minus"  size="22"></u-icon>
						
						<view class="end int-time" style="flex: 1;">
							<u--input inputAlign="right" v-model="info.endTime"  class="int" readonly  placeholder="结束时间--:--"  >
								<template #suffix>
									<u-icon name="clock" size="20" @click="onRangeTimeShow('startTime','endTime')"></u-icon>
								</template>
							</u--input>
						</view>
					</view>
				</u-form-item>
                <u-form-item  label="工单号" required prop="workNo">
                    <scan-code-input  v-model="info.workNo"   />
                    <!-- <u--input  v-model="info.workNo"  class="int"   placeholder="请输入"  /> -->
                </u-form-item>
                <u-form-item label="备注" prop="remark" >
                    <u--textarea :count="!!info.remark" maxlength="800"  v-model="info.remark"  class="int"   placeholder="备注"  />
                </u-form-item>

                <!-- <u-form-item label="产品型号" required prop="materialNo">
                    <u--input  v-model="info.materialNo"  class="int"   placeholder="请输入"  />
                </u-form-item>
                <u-form-item label="温度°C" prop="temp" required>
                    <u--input v-model="info.temp" class="int"   placeholder="请输入"  />
                </u-form-item>
                <u-form-item label="时间（s）" required prop="time">
                    <u--input  v-model="info.time" class="int"   placeholder="请输入"  />
                </u-form-item>

                <u-form-item label="压力（kg/cm²) " required prop="pressure">
                    <u--input  v-model="info.pressure" class="int"   placeholder="请输入"  />
                </u-form-item> -->
            </div>
			<div class="card mt10 pd10">
					<div class="card_head">
						<div class="card_head_tit2">压贴线-生产工艺单明细</div>
					</div>
					<div class="detail_list">
						<uni-table border stripe emptyText="暂无更多数据">
							<!-- 表头行 -->
							<uni-tr>
								<uni-th width="80" align="center">压力</uni-th>
                                <uni-th width="80" align="center">产品型号</uni-th>
                                <uni-th width="120" align="center">温度°C</uni-th>
                                <uni-th width="120" align="center">时间（s）</uni-th>
                                <uni-th width="80" operate align="center">操作</uni-th>
							</uni-tr>
							<!-- 表格数据行 -->
							<uni-tr v-for="(detail,i) in info.pressLineProductionDetailList" :key="i">
								<uni-td align="center"> {{detail.pressure}} </uni-td>
                                <uni-td align="center"> {{detail.model}} </uni-td>
                                <uni-td align="center"> {{detail.temp}}</uni-td>
                                <uni-td align="center"> {{detail.time}}</uni-td>
								<uni-td  operate align="center">
									<view class="uni-group">
										<u-button class="uni-button" @click="handleShow(detail,i)" color="#849EB2" :plain="true" :hairline="true" size="mini" type="primary">查看</u-button>
										<div v-if="!detail.id">
										    <u-button class="uni-button"  @click="deleteRow(i)" :plain="true" :hairline="true" size="mini" type="primary">删除</u-button>
										</div>
									</view>
								</uni-td>
							</uni-tr>
						</uni-table>
					    <div @click="handleShow({model:null},info.pressLineProductionDetailList.length)"   class="detail_add">
							<u-icon name="plus" color="#849EB2" size="12"></u-icon>
							添加 压贴线-生产工艺单明细
						</div>
					</div>
				</div>

			</u--form>
        </div>

        <u-popup :show="showAddPop" :round="10" closeable mode="bottom" @close="showAddPop = false">
			<div class="addInfo_pop">
				<div class="card">
					<div class="card_head">
						<div class="card_head_tit">添加 压贴线-生产工艺单明细</div>
					</div>
					<scroll-view style="height: 450px; max-height: 70vh;" scroll-y>
						<div class="card_from">
							<u--form labelPosition="top" labelWidth="auto"
								ref="popform"
								errorType="toast"
								:model="popInfo"
								:rules="{
									model:[{ required: true, message: '请输入型号'}],
									pressure:[{ required: true, message: '请输入压力（kg/cm²)'}],
									temp:[{ required: true, message: '请输入温度°C'}],
									time:[{ required: true, message: '请输入时间（s）'}],
								}"
							>
								<u-form-item required label="产品型号" prop="model"  >
									<u--input  v-model="popInfo.model"  placeholder="请输入"  />
								</u-form-item>
								<u-form-item required label="压力（kg/cm²)" prop="pressure"  >
									<u--input  v-model="popInfo.pressure"   placeholder="请输入"  />
								</u-form-item>
                                
                                <u-form-item required label="温度°C" prop="temp"  >
									<u--input v-model="popInfo.temp"  placeholder="请输入"  />
								</u-form-item>
                                <u-form-item required label="时间（s）" prop="time"  >
									<u--input  v-model="popInfo.time"  placeholder="请输入" />
								</u-form-item>

                                <u-form-item label="备注" prop="remark" >
									<u--textarea :count="!!popInfo.remark" maxlength="150"  v-model="popInfo.remark"  class="int"   placeholder="请填写"  />
								</u-form-item>
							</u--form>
						</div>
					</scroll-view>
				</div>
				<footer-btn @onconfirm="handconfirm" @oncancel="showAddPop = false"  confirm_btn_text="确定"></footer-btn>
		    </div>
		</u-popup>
		<hbxw-time-range-picker
			v-model:visible="rangeTimeShow"
			@cancel="rangeTimeShow= false"
			isShowType
			:isShowSecond='false'
			:value="rangeTime" 
			delimiter="point"
			@sure="onRangeTimeConfirm"
		/>
		<u-datetime-picker
			:show="dateTimeShow"
			v-model="currentTime"
			confirmColor="#849EB2"
			closeOnClickOverlay
			@confirm="onDateTimeConfirm"
			@cancel="dateTimeShow = false"
			@close="dateTimeShow = false"
			:mode="dateMode"
		></u-datetime-picker>
        <footer-btn :cancelBtnShow="false"  confirm_btn_text="保存" @onconfirm="save()"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import LoadSelector from '@/components/LoadSelector.vue';
import Selector from '@/components/Selector.vue';
import scanCodeInput from '../../components/scanCodeInput.vue';
import dayjs from "dayjs";
import {queryDetails,saveEdit}  from  '../../api/pressLineProduction'
import {queryListByStaff}  from  '@/api/company'
import {useDateTime}  from  '../../vueUse/datetimeUse'
import {useRangeTime}  from  '../../vueUse/rangeTimeUse'
	const prop =  defineProps({
			id: String,
			isComponent: {
				type: Boolean,
				default: false,
			},
			accountId:{
				type: String,
				default: null,
			}
		})
	const routerQuery = prop;
	const appStore = useAppStore();
	const router = useRouter();
	const { userInfo } = useUserStore();

    // 相关字典
    const dictStore = useDictStore()
    const companyList = ref([])
	const util = inject("util");
    const deleteFild = ['accName','createdStaffName','updateStaffName','salesLeadName','marketingName','areaName'];
    const requiredFild = [
        // {key:'name',message:'请填写机会名称',required: true,},
        // {key:'amount',message:'请填写金额',required: true,},
        // {key:'type',message:'请选择机会类型',required: true,},
    ]
    const save = () => {
        for(let index  in requiredFild){ 
            var item =  requiredFild[index]
            if(item.required && (!info.value[item.key] || info.value[item.key]=='')){
            	return uni.showToast({
            		title: item.message,
            		duration: 1500,
            		icon:'none'
            	});
            }
        };
        // let postData = {}
        // for(var key in info.value){
        //     if(!deleteFild.includes(key))
        //         postData[key] = info.value[key];
        // }
        saveEdit(info.value).then((res)=>{
            uni.showModal({
            	title: '提示',
            	content: info.value.id ?'修改成功':'保存成功',
            	showCancel:false,
            	success: function (confirmres) {
            		if (confirmres.confirm) {
            			if(prop.isComponent)emit('confirm', res)
            			else{
            				uni.$emit("refresh-pressLineProduction", {refresh: true}); 
            				router.back();
            			}
            		} else if (confirmres.cancel) {
            			console.log('用户点击取消');
            		}
            	}
            });
        })
    }
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const info = ref({
        pressLineProductionDetailList: [],
    });
	const {dateTimeShow,field,currentTime,onDateTimeShow,onDateTimeConfirm,dateMode} = useDateTime(info)
	const {rangeTime,rangeTimeShow,onRangeTimeShow,onRangeTimeConfirm}  = useRangeTime(info)
    const init = async() =>{
        if(routerQuery.id){
            getInfo()
        }
    }
    const showAddPop = ref(false)
	const popInfo = ref({});
	const deleteRow = (index) => {
		uni.showModal({
			title: '提示',
			content: '确认删除？',
			success: function (res) {
				if (res.confirm) {
					info.value?.pressLineProductionDetailList.splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
	let showindex = null;

	const popform = ref()
	const handconfirm = async () =>{
	    debugger
		try {
			await popform.value.validate();
			console.log('校验通过');
		} catch (err) {
			console.error('校验失败', err);
			return
		}
	    if(showindex || showindex>=0){
	        info.value.pressLineProductionDetailList[showindex] = popInfo.value;
	    }else{
	        info.value.pressLineProductionDetailList.push(popInfo.value)
	    }
	    showAddPop.value = false
	    popInfo.value = {};
	}
	const handleShow = (data,index) =>{
	    popInfo.value = data;

	    showindex = index
	    showAddPop.value = true
	}
    onMounted(() => {
        init()
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
	@import '../../style/common.scss';
</style>