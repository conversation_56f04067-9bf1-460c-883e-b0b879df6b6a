<template>
    <div class="page pd100">
        <div class="card_info">
			<view class="tabInfo"> {{ info.prodLineOrDept }} </view>
			<u--form labelPosition="top" labelWidth="auto" ref="form1" >
				<!-- <div class="card">
					<div class="card_head">
						<div class="card_head_tit3">故障基本信息</div>
					</div>
				</div> -->
				<div class="card pd10">
					<div class="card_head" style="padding-bottom: 0;">
						<div class="card_head_tit2">基本派工信息 -{{info.assignType}}</div>
					</div>
                    <!-- <div class="card_head">
                        <div class="card_head_tit">{{info.assignType}}</div>
                    </div> -->
					<u-form-item v-if="info.assignType =='生产任务' && info.prodLineOrDept!='厂务'" label="工单号" prop="workNo" required>
						<!-- <u--input  v-model="info.workNo"  class="int"   placeholder="请输入"  /> -->
						<scan-code-input  v-model="info.workNo"  />
					</u-form-item>
					<u-form-item label="任务日期"  prop="taskDate" required>
						<u--input v-model="info.taskDate"  class="int" readonly  placeholder="日期"  />
						<template #right><u-icon name="calendar" size="22" @click="onDateTimeShow('taskDate')"></u-icon></template>
					</u-form-item>
                    <template v-if="info.assignType =='生产任务'">
                         <u-form-item  v-if="factoryMenuObj.jobAssignment.jobType=='生产线'" label="应完成数量" prop="expectedQty" required>
                            <u--input type="number"  v-model.number="info.expectedQty"  class="int"   placeholder="请输入"  />
                        </u-form-item>
                        <u-form-item v-if="factoryMenuObj.jobAssignment.jobType=='辅助部门'" label="应完成工作时长" prop="expectedWorkHours" required>
                            <u--input type="number"  v-model.number="info.expectedWorkHours"  class="int"   placeholder="请输入"  />
                        </u-form-item>
                    </template>
					<u-form-item label="分配员工" required prop="assignmentStaffPojoList">
						<load-selector placeholder="请选择" multiple  v-model="info.assignmentStaffPojoList" dataValue="staffId" filterable dataText="staffName"
						load_url="/api/jml/staff/list4FactorySubStaffWithoutSelf"></load-selector>
					</u-form-item>

					<u-form-item label="分配任务" prop="assignedRemark"  >
						<u--textarea :count="!!info.assignedRemark" maxlength="800"  v-model="info.assignedRemark"  class="int"   placeholder="请输入"  />
					</u-form-item>
				</div>
			</u--form>
        </div>
		<u-datetime-picker
			:show="dateTimeShow"
			v-model="currentTime"
			confirmColor="#849EB2"
			closeOnClickOverlay
			@confirm="onDateTimeConfirm"
			@cancel="dateTimeShow = false"
			@close="dateTimeShow = false"
			:mode="dateMode"
		></u-datetime-picker>
		<footer-btn :cancelBtnShow="false"  confirm_btn_text="提交" @onconfirm="save()"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import LoadSelector from '@/components/LoadSelector.vue';
import Selector from '@/components/Selector.vue';
import scanCodeInput from '../../components/scanCodeInput.vue';
import dayjs from "dayjs";
import {queryDetails,saveEdit}  from  '../../api/jobAssignment'
import {queryListByStaff}  from  '@/api/company'
import {useDateTime}  from  '../../vueUse/datetimeUse'
import { useRoute } from 'uni-mini-router';
import { isArray } from 'lodash';
import {upload,dictTree,asyncUpload}  from  '@/api/common'
// import {deviceRepairStatus} from '../../constant/index.js'
	const prop =  defineProps({
			id: String,
			isComponent: {
				type: Boolean,
				default: false,
			},
			assignType:{
				type: String,
				default: null,
			}
		})
	const routerQuery = prop;
	const appStore = useAppStore();
	const router = useRouter();
	const route = useRoute();
	const { userInfo,factoryMenuObj } = useUserStore();
    // 相关字典
    const dictStore = useDictStore()
    const companyList = ref([])
	const util = inject("util");
    const deleteFild = ['accName','createdStaffName','updateStaffName','salesLeadName','marketingName','areaName'];
    const requiredFild = [
        // {key:'name',message:'请填写机会名称',required: true,},
        // {key:'amount',message:'请填写金额',required: true,},
        // {key:'type',message:'请选择机会类型',required: true,},
    ]
	const info = ref({
        assignType:routerQuery.assignType,
		jobType: factoryMenuObj?.jobAssignment.jobType,
		prodLineOrDept:factoryMenuObj?.jobAssignment.prodLineOrDept,
	});
    const save = (type) => {
        for(let index  in requiredFild){ 
            var item =  requiredFild[index]
            if(item.required && (!info.value[item.key] || info.value[item.key]=='')){
            	return uni.showToast({
            		title: item.message,
            		duration: 1500,
            		icon:'none'
            	});
            }
        };
		let {assignmentStaffPojoList,...post} = info.value
		if(type)post.status = type

		if(isArray(assignmentStaffPojoList)) {
			post.assignmentStaffPojoList = assignmentStaffPojoList.map((i)=>{
				return {assignedEngineerStaffId:i}
			})
		}
		
        // let postData = {}
        // for(var key in info.value){
        //     if(!deleteFild.includes(key))
        //         postData[key] = info.value[key];
        // }
		debugger
        saveEdit(post).then((res)=>{
            uni.showModal({
            	title: '提示',
            	content: info.value.id ?'修改成功':'保存成功',
            	showCancel:false,
            	success: function (confirmres) {
            		if (confirmres.confirm) {
            			if(prop.isComponent)emit('confirm', res)
            			else{
            				uni.$emit("refresh-jobAssignment", {refresh: true}); 
            				router.back();
            			}
            		} else if (confirmres.cancel) {
            			console.log('用户点击取消');
            		}
            	}
            });
        })
    }
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
			if(res.data.damageImg && res.data.damageImg!=''){
			    info.value.damageImg = res.data.damageImg.split(';').map(i=>{
					return{url:import.meta.env.VUE_APP_BASE_IMG + i}
				})
			}else{
			    info.value.damageImg = []
			}
			
        }).catch((err) => {
        });
    }
	const {dateTimeShow,field,currentTime,onDateTimeShow,onDateTimeConfirm,dateMode} = useDateTime(info)
    
    const init = async() =>{
		await dictStore.getDictBatch(['impact_level','product_line']);
        if(routerQuery.id){
            getInfo()
        }
    }
	
	
	const afterRead = async(event,type) =>{
		let files = [].concat(event.file)
		if(!info.value[type]) info.value[type] = [];
		let fileListLen =  info.value[type].length
		files.map((item) => {
			info.value[type].push({
				...item,
				status: 'uploading',
				message: '上传中'
			})
		})
		for (let i = 0; i < files.length; i++) {
			try {
				const {data} = await asyncUpload(files[i].url)
				let item = info.value[type][fileListLen];
				info.value[type].splice(fileListLen, 1, {
				  ...item,
				  status: 'success',
				  message: '',
				  url: data.url,
				});
				fileListLen++;
			} catch (e) {
				console.log(e)
				uni.showToast({
					title: "图片上传失败",
					icon: "none"
				})
			}
		}
	}
	
	const beforeDelete = ({file,index},type) =>{
		uni.showModal({
			title: '提示',
			content: '是否删除该附件？',
			success: function (res) {
				if (res.confirm) {
					info.value[type].splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
	
	
    onMounted(() => {
        init()
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
	@import '../../style/common.scss';
</style>