<template>
    <div class="page pd100">
        <div class="card_info">
			<view class="tabInfo"> {{ info.jobType }} - {{ info.prodLineOrDept }} </view>
            <div class="card">
				<div class="card_head">
					<div class="card_head_tit2" style="margin-top: 0;">
                        {{ info.name }}
						<!-- <view> <u-icon name="calendar" :label="`${info.replaceDate} ${info.replaceStartTime}/${info.replaceEndTime}`" labelSize="12"></u-icon> </view> -->
                    </div>
					<div v-if="info.jobAssignStatus" class="head_status">
						<u-tag size="mini" :bgColor="jobAssignStatus[info.jobAssignStatus].bgColor" :borderColor="jobAssignStatus[info.jobAssignStatus].bgColor" :text="info.jobAssignStatus"></u-tag>
					</div>
					<!-- <u-divider></u-divider> -->
					
					<div v-if="info.taskDate"  class="card_head_tit3 mt10 align-items_center ">
						<view> <u-icon name="calendar" :label="`${info.taskDate} ${info.weekday}`" labelSize="12"></u-icon> </view>
					</div>
					
                </div>
				
            </div>
        </div>
		<!--  -->
		<!-- <div v-if="showDispense('confirm')"  class="card_info">
			<div class="card">
				<div class="card_head">
					<div class="card_head_tit2" style="margin-top: 0;">
					   主管确认
					</div>
				</div>
				<u--form labelPosition="top" labelWidth="auto" ref="form1" >
					<u-form-item v-if="jobType == '生产线'" label="实际完成数量" required prop="actualCompletedQty">
						<u--input type="number"  v-model.number="operateInfo.actualCompletedQty"  class="int"   placeholder="请输入"  />
					</u-form-item>
					<u-form-item v-if="jobType == '辅助部门'" label="实际完成数量" required prop="overtimeHours">
						<u--input type="number"  v-model.number="operateInfo.overtimeHours"  class="int"   placeholder="请输入"  />
					</u-form-item>
					
					<u-form-item label="确认备注" prop="confirmationRemark"  >
						<u--textarea :count="!!operateInfo.confirmationRemark" maxlength="800"  v-model="operateInfo.confirmationRemark"  class="int"   placeholder="请输入"  />
					</u-form-item>
				</u--form>	
			</div>
		</div> -->
		
		
        <div class="card_info">
            <div class="card">
				
				<!-- <template v-if="info.jobAssignStatus=='主管确认'">
					<div class="card_head">
					    <div class="card_head_tit2 flex flex-space-between" style="margin-top: 0;">
					       <view>主管确认信息</view>
					    </div>
					</div>
					<u-divider></u-divider>
					<div class="card_main">
						<div v-if="jobType=='生产线'" class="item">
						    <div class="left">实际完成数量</div><div class="rigth">{{info.actualCompletedQty}}</div>
						</div>
						<div v-if="jobType=='辅助部门'" class="item">
						    <div class="left">加班时长</div><div class="rigth">{{info.overtimeHours}}</div> 
						</div>
						
						<div class="item">
						    <div class="left">确认备注</div><div class="rigth">{{info.confirmationRemark}}</div>
						</div>
						<div class="item">
						    <div class="left">确认主管</div><div class="rigth">{{info.confirmationStaffName}}</div>
						</div>
					</div>
				</template> -->
				
                <div class="card_head">
                    <div class="card_head_tit2 flex flex-space-between" style="margin-top: 0;">
                       <view>岗位分工完成信息</view>
					   <view class="mlauto">
						   <u--text v-if="info.completionStatus=='已完成'" size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;" :text="info.completionStatus"></u--text>
						   <u--text v-if="info.completionStatus=='未完成'" size="14" bold color="#F57272" prefixIcon="close-circle-fill" iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;" :text="info.completionStatus"></u--text>
					   </view>
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
					<!-- <div class="item">
					    <div class="left">完成地点</div><div class="rigth">{{info.completionLocation}}</div>
					</div>
					
					<div v-if="jobType=='生产线'" class="item">
					    <div class="left">工作数量</div><div class="rigth">{{info.workQty}}</div>
					</div>
					<div v-if="jobType=='辅助部门'" class="item">
					    <div class="left">工作时长</div><div class="rigth">{{info.workHours}}</div> 
					</div>
					
					<div class="item">
					    <div class="left">工作内容</div><div class="rigth">{{info.workContent}}</div>
					</div>
					<div class="item">
					    <div class="left">完成备注</div><div class="rigth">{{info.completionRemark}}</div>
					</div>
					<div class="item">
					    <div class="left">完成员工</div><div class="rigth">{{info.completionStaffName}}</div>
					</div> -->
					<div class="detail_list">
						<uni-table border stripe emptyText="暂无更多数据">
							<!-- 表头行 -->
							<uni-tr>
								<uni-th width="80" align="center">员工</uni-th>
					            <uni-th width="80" align="center">完成状态</uni-th>
								<uni-th width="80" align="center">工作时长</uni-th>
								<!-- <template v-if="info.assignType=='生产任务'"> -->
									<uni-th width="120" v-if="info.jobType =='生产线'" align="center">工作数量</uni-th>
									<uni-th width="120" v-if="info.jobType =='辅助部门'" align="center">工作时长</uni-th>
								<!-- </template> -->
					            <uni-th width="80" operate align="center">操作</uni-th>
							</uni-tr>
							<!-- 表格数据行 -->
							<uni-tr v-for="(detail,i) in info.assignStaffList" :key="i">
								<uni-td align="center"> {{detail.staffName}} </uni-td>
					            <uni-td align="center"> {{detail.completionStatus}} </uni-td>
								 <uni-td align="center"> {{detail.startTime}} ~ {{detail.endTime}} </uni-td>
								<!-- <template v-if="info.assignType=='生产任务'"> -->
					            <uni-td align="center" v-if="info.jobType =='生产线'"> {{detail.workQty}}</uni-td>
								<uni-td align="center" v-if="info.jobType =='辅助部门'"> {{detail.workHours}}</uni-td>
								<!-- </template> -->
					            <uni-td  operate align="center">
					                <view class="uni-group">
										<u-button v-if="detail.completeBtn=='1'" class="uni-button"  @click="hdBtn({
											btnValue:'complete',
											btnCode:'complete',
											otherData:{
												id:detail.id,
												jobAssignmentId:info.id
											}
										})" :plain="true" :hairline="true" size="mini" type="primary">完成</u-button>
										<u-button v-else-if="detail.completeBtn=='2'" class="uni-button"  @click="hdBtn({
											btnValue:'confirm',
											btnCode:'confirm',
											otherData:{
												id:detail.id,
												jobAssignmentId:info.id,
												actualWorkHours:detail.actualWorkHours,
											}
										})" :plain="true" :hairline="true" size="mini" type="primary">确认</u-button>
					                    <u-button v-else class="uni-button" @click="handleShow(detail,i)" color="#849EB2" :plain="true" :hairline="true" size="mini" type="primary">查看</u-button>
					                </view>
					            </uni-td>
							</uni-tr>
						</uni-table>
					</div>
										
                </div>

				<div class="card_head">
				    <div class="card_head_tit2" style="margin-top: 0;">
				       基本派工信息 -{{info.assignType}}
				    </div>
				</div>
				<u-divider></u-divider>
				<div class="card_main">
                     <template v-if="info.assignType =='生产任务'">
                        <template v-if="info.jobType =='生产线'" >
							
                            <div class="item"><div class="left">应完成工作数量</div><div class="rigth">{{info.expectedQty}}</div></div>
							<div class="item"><div class="left">已完成工作数量</div><div class="rigth">{{info.completedWorkQty}}</div> </div>
							<div class="item"><div class="left">剩余工作数量</div><div class="rigth">{{info.remainingWorkQty}}</div> </div>
                        </template>
                        <template v-if="info.jobType=='辅助部门'" >
                            <div class="item"><div class="left">应完成工作时长</div><div class="rigth">{{info.expectedWorkHours}}</div></div>
							<div class="item"><div class="left">已完成工作时长</div><div class="rigth">{{info.completedWorkHours}}</div></div>
							<div class="item"><div class="left">剩余工作时长</div><div class="rigth">{{info.remainingWorkHours}}</div></div>
                        </template>
					</template>
					<div class="item">
					    <div class="left">任务日期</div><div class="rigth">{{info.taskDate}}</div>
					</div>
					<div class="item">
					    <div class="left">分配员工</div>
						<div class="rigth">
						{{Array.isArray(info.assignStaffList) ? info.assignStaffList.map(i=>i.staffName).join(','): ''}}
						</div>
					</div>
					<div class="item">
					    <div class="left">分配任务</div><div class="rigth">{{info.assignedRemark}}</div>
					</div>
					
				</div>
				
                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.createdTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.updateTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>
		
		<!-- 弹框操作 -->
		<u-modal v-model:show="dialogShow" @confirm="dialogConfirm"  @cancel="resetDialogShow" confirmColor="#849EB2" :title="operateInfo.dialogTitle" show-cancel-button>
		    <div class="addInfo">
				<template v-if="operateInfo.operateType=='complete'">
				<map
					enable-3D
					show-compass
					enable-traffic
					show-location
					enable-indoorMap
					enable-satellite

					:style="{ width: '100%' , height: '120px' }" :latitude="operateInfo.completionLatitude" :longitude="operateInfo.completionLongitude">
					<!-- <cover-view class="myMap_map__cover-view">
						<cover-view class="myMap_map__cover-view_mapControls">
							<cover-view class="myMap_map__cover-view_mapControls_drawControl"></cover-view>
						</cover-view>
					</cover-view> -->
				</map>
				<u--form labelPosition="top"   labelWidth="auto" >
					<u-form-item required  label="工作时长" prop="startTime">
						<view style="display: flex; width: 100%;">
							<view class="start int-time" style="flex: 1;">
								<u--input inputAlign="left" v-model="operateInfo.startTime"  class="int" readonly  placeholder="--:--"  >
									<template #suffix>
										<u-icon name="clock" size="20" @click="onRangeTimeShow('startTime','endTime')"></u-icon>
									</template>
								</u--input>
							</view>
							<u-icon name="minus"  size="22"></u-icon>
							<view class="end int-time" style="flex: 1;">
								<u--input inputAlign="right" v-model="operateInfo.endTime"  class="int" readonly  placeholder="--:--"  >
									<template #suffix>
										<u-icon name="clock" size="20" @click="onRangeTimeShow('startTime','endTime')"></u-icon>
									</template>
								</u--input>
							</view>
						</view>
					</u-form-item>
					
					<!-- <template v-if="info.assignType=='生产任务'"> -->
						<u-form-item v-if="jobType == '生产线'" label="工作数量" :required="info.assignType=='生产任务'" prop="workQty">
							<u--input type="number"  v-model.number="operateInfo.workQty"  class="int"   placeholder="请输入"  />
						</u-form-item>
						<!-- <u-form-item v-if="jobType == '辅助部门'" label="工作时长" :required="info.assignType=='生产任务'" prop="workHours">
							<u--input type="number"  v-model.number="operateInfo.workHours"  class="int"   placeholder="请输入"  />
						</u-form-item> -->
					<!-- </template> -->
		            <u-form-item  :required="info.assignType=='生产任务'" label="工作内容" prop="workContent">
		            	<u--textarea :count="!!operateInfo.workContent"   v-model="operateInfo.workContent"  class="int" height="50" maxlength="200"  :placeholder="`请填写`"  />
		            </u-form-item>
					<u-form-item label="完成备注" prop="completionRemark">
						<u--textarea :count="!!operateInfo.completionRemark"   v-model="operateInfo.completionRemark"  class="int" height="50" maxlength="200"  :placeholder="`请填写`"  />
					</u-form-item>
					<u-form-item label="完成图片" prop="completionImg" >
						<u-upload
						    :max-count="5"
						    multiple
						    @after-read="afterRead($event,'completionImg')"
						    :deletable="true"
							:sizeType="['compressed']"
						    :before-delete="(file,detail)=>beforeDelete(file,detail)"
							@delete="beforeDelete($event,'completionImg')"
							:fileList="operateInfo.completionImg"
						>
						</u-upload>
					</u-form-item>
				</u--form>
				</template>
				<template v-if="operateInfo.operateType=='confirm'">
					<u--form labelPosition="top" labelWidth="auto" ref="form1" >
						<!-- <template v-if="info.assignType=='生产任务'"> -->
							<u-form-item v-if="jobType == '生产线'" label="实际完成数量" :required="info.assignType=='生产任务'" prop="actualCompletedQty">
								<u--input type="number"  v-model.number="operateInfo.actualCompletedQty"  class="int"   placeholder="请输入"  />
							</u-form-item>
							<!-- v-if="jobType == '辅助部门'" -->
							<u-form-item  label="实际工作时长"  prop="actualWorkHours">
								<u--input type="number"  readonly disabled v-model.number="operateInfo.actualWorkHours"  class="int"   placeholder="请输入"  />
							</u-form-item>
						<!-- </template> -->
						<u-form-item  label="加班时长" prop="overtimeHours">
							<u--input type="number"  v-model.number="operateInfo.overtimeHours"  class="int"   placeholder="请输入"  />
						</u-form-item>
						<u-form-item label="确认备注" prop="confirmationRemark"  >
							<u--textarea :count="!!operateInfo.confirmationRemark" maxlength="800"  v-model="operateInfo.confirmationRemark"  class="int"   placeholder="请输入"  />
						</u-form-item>
					</u--form>	
				</template>
		    </div>
		</u-modal>
		<!-- 底部弹出 -->
		<u-popup :show="showPopInfo" :round="10" closeable mode="bottom" @close="showPopInfo = false">
		    <div class="pop_info">
		        <div class="pop_info_tit">详情</div>
		        <div class="card_info">
		            <div class="card" style="max-height: calc(90vh - 100px); overflow-y: auto;">
						
		                <div class="card_main">
							<div class="card_head_tit2 mt15">
							    完成信息
							</div>
							<div class="item">
							    <div class="left">员工</div><div class="rigth">{{popinfo.staffName}}</div>
							</div>
							<div class="item">
							    <div class="left">工作时长</div><div class="rigth">{{popinfo.startTime}} ~ {{popinfo.endTime}}</div>
							</div>
							<div class="item">
							    <div class="left">完成地点</div><div class="rigth">{{popinfo.completionLocation}}</div>
							</div>
		                    <div class="item">
		                        <div class="left">工作内容</div><div class="rigth">{{popinfo.workContent}}</div>
		                    </div>
							<template v-if="info.assignType =='生产任务'" >
								<div v-if="info.jobType=='生产线'" class="item">
									<div class="left">工作数量</div><div class="rigth">{{popinfo.workQty}}</div>
								</div>
								<!-- <div v-if="info.jobType=='辅助部门'" class="item">
									<div class="left">工作时长</div><div class="rigth">{{popinfo.workHours}}</div> 
								</div> -->
							</template>
							<div  class="item">
							    <div class="left">完成图片</div>
								<div v-if="popinfo.completionImg && popinfo.completionImg.length>0" class="rigth section-item-img">
							        <image v-for="(item,index) in popinfo.completionImg" :key="index" class="image"  @click="showImagePreview(popinfo.completionImg,index)"  mode="widthFix"  :src="imgBase+item" />
							    </div>
							</div>
		                    <div class="item">
		                        <div class="left">完成备注</div><div class="rigth">{{popinfo.completionRemark}}</div>
		                    </div>
							<div class="item">
							    <div class="left">完成时间</div><div class="rigth">{{popinfo.completionTime}}</div>
							</div>
							
							<div class="card_head_tit2 mt15">
							    确认信息
							</div>
							<!-- <template v-if="info.assignType =='生产任务'" > -->
								<div v-if="info.jobType=='生产线'" class="item">
									<div class="left">实际完成数量</div><div class="rigth">{{popinfo.actualCompletedQty}}</div>
								</div>
								<!-- v-if="info.jobType=='辅助部门'" -->
								<div  class="item">
									<div class="left">实际完工作时长</div><div class="rigth">{{popinfo.actualWorkHours}}</div> 
								</div>
							<!-- </template> -->
							<div class="item">
							    <div class="left">加班时长</div><div class="rigth">{{popinfo.overtimeHours}}</div>
							</div>
							<div class="item">
							    <div class="left">确认备注</div><div class="rigth">{{popinfo.confirmationRemark}}</div>
							</div>
							<div class="item">
							    <div class="left">确认时间</div><div class="rigth">{{popinfo.confirmationTime}}</div>
							</div>
							<div class="item">
							    <div class="left">确认人</div><div class="rigth">{{popinfo.confirmationStaffName}}</div>
							</div>
							
							<div class="card_head_tit2 mt15">
							    系统信息
							</div>
		                    <div class="item">
		                        <div class="left">创建日期</div><div class="rigth">{{popinfo.createdTime}}</div>
		                    </div>
		                     <div class="item">
		                        <div class="left">修改时间</div><div class="rigth">{{popinfo.updateTime}}</div>
		                    </div>
		                </div>
		            </div>
		        </div>
		    </div>
		</u-popup>
		
		<template v-if="!dialogShow">
        <footer-btn v-if="info.btnList"  :cancelBtnShow="false" :confirmBtnShow="false">
			<div  v-for="(item,index) in info.btnList"  :key="index"
				:style="`background: ${jobAssignBtnInfo[item.btnValue]?.btnColor};`" 
				@click="hdBtn(item)" :class="item.btnCode" class="confirm_btn btn">
				<u-icon
					v-if="jobAssignBtnInfo[item.btnValue]?.icon"
					class="btn_icon"
					size="16"
					color="#fff"
					:name="jobAssignBtnInfo[item.btnValue]?.icon"
				></u-icon>
				<view>{{ jobAssignBtnInfo[item.btnValue]?.btnName ?? item.btnName}}</view>
			</div>
		</footer-btn>
		</template>
		<hbxw-time-range-picker
			v-model:visible="rangeTimeShow"
			@cancel="rangeTimeShow= false"
			isShowType
			:isShowSecond='false'
			:value="rangeTime" 
			delimiter="point"
			@sure="onRangeTimeConfirm"
		/>
		<floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
	import ProgressStatus from '@/components/ProgressStatus.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import {queryDetails,operate}  from  '../../api/jobAssignment'
import {jobAssignBtnInfo,jobAssignStatus} from '../../constant/index.js'
import LoadSelector from '@/components/LoadSelector.vue';
import { isArray } from 'lodash';
import {upload,dictTree,asyncUpload}  from  '@/api/common'
import {getAddrByLngAndLat}  from  '@/api/common'
import {useRangeTime}  from  '../../vueUse/rangeTimeUse'
    const prop =  defineProps(['id'])
    const router = useRouter();
    const util = inject("util");
	const dictStore = useDictStore()
    const routerQuery =  prop;
    const info = ref({
    });
	const { userInfo,factoryMenuObj } = useUserStore();
	const jobType = ref(factoryMenuObj?.jobAssignment.jobType);
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const init = async() =>{
		await dictStore.getDictBatch(['impact_level','product_line','repairer_status','repair_status_c','solution','replace_part']);
        getInfo()
    }
	
	const showDispense = computed(()=>(code)=>{ 
		if(!info.value.btnList || !code) return false
		else{
			return  !!info.value.btnList.find(i=>i.btnValue===code)
		}
	})
	
	//操作用到
	const operateInfo = ref({
		id:routerQuery.id,
	})
	const resetDialogShow = ()=>{
		operateInfo.value = {
			id:routerQuery.id,
		}
		dialogShow.value = false
	}
	const afterRead = async(event,type) =>{
		let files = [].concat(event.file)
		if(!operateInfo.value[type]) operateInfo.value[type] = [];
		let fileListLen =  operateInfo.value[type].length
		files.map((item) => {
			operateInfo.value[type].push({
				...item,
				status: 'uploading',
				message: '上传中'
			})
		})
		for (let i = 0; i < files.length; i++) {
			try {
				const {data} = await asyncUpload(files[i].url)
				let item = operateInfo.value[type][fileListLen];
				operateInfo.value[type].splice(fileListLen, 1, {
				  ...item,
				  status: 'success',
				  message: '',
				  url: data.url,
				});
				fileListLen++;
			} catch (e) {
				console.log(e)
				uni.showToast({
					title: "图片上传失败",
					icon: "none"
				})
			}
		}
	}
	
	const beforeDelete = ({file,index},type) =>{
		uni.showModal({
			title: '提示',
			content: '是否删除该附件？',
			success: function (res) {
				if (res.confirm) {
					operateInfo.value[type].splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
	

	let isSubmit = false
	const dialogShow = ref(false)
	const dialogConfirm = ()=>{
		debugger
	 //    if((!approvalInfo.remark || approvalInfo.remark=='')) return uni.showToast({
		// 	title: '备注必须填写',
		// 	duration: 1500,
		// 	icon:'none'
		// });
		submit()
	}
	
	const hdBtn = (data) =>{
		debugger
		if(data?.otherData){
			operateInfo.value = {...operateInfo.value,...data?.otherData}
		}
		if(data.btnValue=='save'){
			return router.push({name: 'jobAssignmentEdit',params: {id:routerQuery.id}})
		}else if(jobAssignBtnInfo[data.btnValue]?.dialogShow){
			operateInfo.value.operateType = data.btnCode;
			operateInfo.value.dialogTitle = jobAssignBtnInfo[data.btnValue]?.dialogTitle;
			if(jobAssignBtnInfo[data.btnValue]?.otherData){
				operateInfo.value = {...operateInfo.value,...jobAssignBtnInfo[data.btnValue]?.otherData}
			}
			debugger
			dialogShow.value = true
		}else{
			submit(data.btnCode)
		}
	}
	
	const submit =(type)=>{
		if(isSubmit) return false
		if(!type && !operateInfo.value.operateType) return;
		let {operateType,dialogTitle,partImg,completionImg,...post} = operateInfo.value;
		// if(partImg && isArray(partImg)){
		// 	partImg.forEach((imgInfo,index) => {
		// 	    post[`partImg${index+1}`] = imgInfo.url
		// 	});
		// }
		if(completionImg && isArray(completionImg)){
			completionImg.forEach((imgInfo,index) => {
			    post[`completionImg${index+1}`] = imgInfo.url
			});
		}
		// if(repairImg && isArray(repairImg)){
		// 	repairImg.forEach((imgInfo,index) => {
		// 	    post[`repairImg${index+1}`] = imgInfo.url
		// 	});
		// }
		
		isSubmit = true;
		operate(type??operateType,post).then(res=>{
			uni.showModal({
				title: '提示',
				content: res.msg,
				showCancel:false,
				success: function (confirmres) {
					isSubmit = false
					resetDialogShow()
					getInfo()
					// if (confirmres.confirm) {
					// 	if(prop.isComponent)emit('confirm', res)
					// 	else{
					// 		uni.$emit("refresh", {refresh: true}); 
					// 		router.back();
					// 	}
					// } else if (confirmres.cancel) {
					// 	uni.$emit("refresh", {refresh: true}); 
					// 	console.log('用户点击取消');
					// }
				}
			});
		}).catch(e=>{
			isSubmit = false
		})
	}
	
	// 地图相关
	const location = reactive({
		latitude:null,
		longitude:null,
	})
	watch(() => dialogShow.value,(val) => {
			val && open()
		},{ immediate: false,deep:true }
	)
	const open = () =>{
		if(operateInfo.value?.operateType!='complete') return
		nextTick(()=>{
			uni.getLocation({
				type: 'gcj02',
				altitude:true,
				geocode:true,
				isHighAccuracy:true,
				success: function (res) {
					console.log('当前位置的经度：' + res.longitude);
					console.log('当前位置的纬度：' + res.latitude);
					operateInfo.value.completionLongitude = res.longitude
					operateInfo.value.completionLatitude = res.latitude
					getAddrByLngAndLat(res.longitude,res.latitude).then((addres)=>{
						operateInfo.value.completionLocation = addres.data
					})
				},
				fail:(e)=>{
					debugger
							// 如果用uni.chooseLocation没有获取到地理位置，则需要获取当前的授权信息，判断是否有地理授权信息
					uni.getSetting({
						success: (res) => {
							console.log(res);
							var status = res.authSetting;
							if(!status['scope.userLocation']){
							// 如果授权信息中没有地理位置的授权，则需要弹窗提示用户需要授权地理信息
								uni.showModal({
									title:"是否授权当前位置",
									content:"需要获取您的地理位置，请确认授权，否则地图功能将无法使用",
									success:(tip)=>{
										if(tip.confirm){
										// 如果用户同意授权地理信息，则打开授权设置页面，判断用户的操作
											uni.openSetting({
												success:(data)=>{
												// 如果用户授权了地理信息在，则提示授权成功
													if(data.authSetting['scope.userLocation']===true){
														uni.showToast({
															title:"授权成功",
															icon:"success",
															duration:1000
														})
														// 授权成功后，然后再次chooseLocation获取信息
														uni.getLocation({
															type: 'gcj02',
															altitude:true,
															geocode:true,
															isHighAccuracy:true,
															success: function (res) {
																console.log('当前位置的经度：' + res.longitude);
																console.log('当前位置的纬度：' + res.latitude);
																resultInfo.location = res
																getAddrByLngAndLat(res.longitude,res.latitude).then((addres)=>{
																	resultInfo.address = addres.data
																})
															},
														})
													}else{
														uni.showToast({
															title:"授权失败",
															icon:"none",
															duration:1000
														})
													}
												}
											})
										}
									}
								})
							}
						},
						fail: (res) => {
							uni.showToast({
								title:"调用授权窗口失败",
								icon:"none",
								duration:1000
							})
						}
					})
				}
			});
				
		})
	
	}
	const showPopInfo = ref(false)
	const popinfo = ref({})
	const {rangeTime,rangeTimeShow,onRangeTimeShow,onRangeTimeConfirm}  = useRangeTime(operateInfo)
	const handleShow = async(info,index) =>{
	    popinfo.value = info;
		if(info.completionImg && !Array.isArray(info.completionImg)){
		    popinfo.value.completionImg = info.completionImg.split(';')
		}else{
		    popinfo.value.completionImg = []
		}
	    showPopInfo.value = true
	}
	onShow(() => {
		 init()
	})
    onMounted(() => {
       
    })
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
	@import '@/styles/factory.scss';
	@import '../../style/common.scss';
	.card_info{
		.card{
			.item{
				padding-bottom: 5px;
				.left{
					line-height: 1.2;
				}
				.rigth{
					text-align: right;
				}
			}
		}
	}
	.addInfo{
		padding:0 10px;
		position: relative;
		border-radius: var(--card-border-radius);
		line-height: 1.4;
		width: 100%;
        max-height: 80vh;
        overflow: scroll;
		.card{
			background: none;
			padding: var(--card-padding);
			position: relative;
			border-radius: var(--card-border-radius);
			line-height: 1.4;
			.card_head{
				display: flex;
				align-items: center;
				justify-content: center;
				// border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
				padding: 15px 0 10px 0;
				&_tit{
					display: flex;
					align-items: center;
					font-size: 18px;
					font-weight: 600;
					color: #333333;
					span{
						font-weight: 700;
						font-size: 16px;
						color: #000000;
						margin-right: 5px;
					}
				}
			}
		}
		:deep(){
			.u-textarea{
				padding: 0;
			}
			.int{
				background: none !important;
				.van-field__value{
					background: #fff;
				}
				// &.van-cell:after{
				//     content: none;
				// }
			}
		}
	}
	
	
</style>