<template>
    <div class="page pd100">
        <div class="card_info">
			<view class="tabInfo"> {{ info.jobType }} - {{ info.prodLineOrDept }} </view>
			<u--form labelPosition="top" labelWidth="auto" ref="form1" >
				<!-- <div class="card">
					<div class="card_head">
						<div class="card_head_tit3">故障基本信息</div>
					</div>
				</div> -->
				<div class="card pd10">
					<div class="card_head">
						<div class="card_head_tit2">故障基本信息</div>
					</div>
					
					
					<u-form-item label="设备名称" required prop="deviceId">
						<load-selector placeholder="请选择" v-model="info.deviceId" value-key="id" filterable value-fild="name"   load_url="/api/deviceRepair/queryDevices"></load-selector>
					</u-form-item>
					
					<u-form-item label="影响程度" required prop="impactLevel">
						<zxz-uni-data-select  placeholder="请选择" v-model="info.impactLevel" :localdata="dict['impact_level']"></zxz-uni-data-select>
					</u-form-item>
					<u-form-item label="发生位置" required prop="location"  >
						<u--textarea :count="!!info.location" maxlength="800"  v-model="info.location"  class="int"   placeholder="请输入"  />
					</u-form-item>
					<u-form-item label="故障描述" required prop="faultDesc" >
						<u--textarea :count="!!info.faultDesc" maxlength="800"  v-model="info.faultDesc"  class="int"   placeholder="请输入"  />
					</u-form-item>
					<u-form-item label="故障照片" prop="damageImg" >
						<u-upload
						    :max-count="3"
						    multiple
						    @after-read="afterRead($event,'damageImg')"
						    :deletable="true"
							:sizeType="['compressed']"
						    :before-delete="(file,detail)=>beforeDelete(file,detail)"
							@delete="beforeDelete($event,'damageImg')"
							:fileList="info.damageImg"
						>
						</u-upload>
					</u-form-item>
					
				</div>
			</u--form>
        </div>
		<footer-btn :cancelBtnShow="false"  confirm_btn_text="提交" @onconfirm="save()"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import LoadSelector from '@/components/LoadSelector.vue';
import Selector from '@/components/Selector.vue';
import dayjs from "dayjs";
import {queryDetails,saveEdit}  from  '../../api/deviceRepair'
import {queryListByStaff}  from  '@/api/company'
import {useDateTime}  from  '../../vueUse/datetimeUse'
import { useRoute } from 'uni-mini-router';
import { isArray } from 'lodash';
import {upload,dictTree,asyncUpload}  from  '@/api/common'
// import {deviceRepairStatus} from '../../constant/index.js'
	const prop =  defineProps({
			id: String,
			isComponent: {
				type: Boolean,
				default: false,
			},
			deviceRepairId:{
				type: String,
				default: null,
			}
		})
	const routerQuery = prop;
	const appStore = useAppStore();
	const router = useRouter();
	const route = useRoute();
	const { userInfo,factoryMenuObj } = useUserStore();
    // 相关字典
    const dictStore = useDictStore()
    const companyList = ref([])
	const util = inject("util");
    const deleteFild = ['accName','createdStaffName','updateStaffName','salesLeadName','marketingName','areaName'];
    const requiredFild = [
        // {key:'name',message:'请填写机会名称',required: true,},
        // {key:'amount',message:'请填写金额',required: true,},
        // {key:'type',message:'请选择机会类型',required: true,},
    ]
    const save = (type) => {
        for(let index  in requiredFild){ 
            var item =  requiredFild[index]
            if(item.required && (!info.value[item.key] || info.value[item.key]=='')){
            	return uni.showToast({
            		title: item.message,
            		duration: 1500,
            		icon:'none'
            	});
            }
        };
		if(type)info.value.status = type
		if(isArray(info.value.damageImg)) {
		    info.value.damageImg.forEach((item,index) => {
		        info.value[`damageImg${index+1}`] = item.url
		    });
		}
        // let postData = {}
        // for(var key in info.value){
        //     if(!deleteFild.includes(key))
        //         postData[key] = info.value[key];
        // }
        saveEdit(info.value).then((res)=>{
            uni.showModal({
            	title: '提示',
            	content: info.value.id ?'修改成功':'保存成功',
            	showCancel:false,
            	success: function (confirmres) {
            		if (confirmres.confirm) {
            			if(prop.isComponent)emit('confirm', res)
            			else{
            				uni.$emit("refresh-deviceRepair", {refresh: true}); 
            				router.back();
            			}
            		} else if (confirmres.cancel) {
            			console.log('用户点击取消');
            		}
            	}
            });
        })
    }
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
			if(res.data.damageImg && res.data.damageImg!=''){
			    info.value.damageImg = res.data.damageImg.split(';').map(i=>{
					return{url:import.meta.env.VUE_APP_BASE_IMG + i}
				})
			}else{
			    info.value.damageImg = []
			}
			
        }).catch((err) => {
        });
    }
    const info = ref({
		jobType: factoryMenuObj?.deviceRepair.jobType,
		reparirStatus:'待办'
    });
    const init = async() =>{
		await dictStore.getDictBatch(['impact_level','product_line']);
        if(routerQuery.id){
            getInfo()
        }
    }
	
	
	const afterRead = async(event,type) =>{
		let files = [].concat(event.file)
		if(!info.value[type]) info.value[type] = [];
		let fileListLen =  info.value[type].length
		files.map((item) => {
			info.value[type].push({
				...item,
				status: 'uploading',
				message: '上传中'
			})
		})
		for (let i = 0; i < files.length; i++) {
			try {
				const {data} = await asyncUpload(files[i].url)
				let item = info.value[type][fileListLen];
				info.value[type].splice(fileListLen, 1, {
				  ...item,
				  status: 'success',
				  message: '',
				  url: data.url,
				});
				fileListLen++;
			} catch (e) {
				console.log(e)
				uni.showToast({
					title: "图片上传失败",
					icon: "none"
				})
			}
		}
	}
	
	const beforeDelete = ({file,index},type) =>{
		uni.showModal({
			title: '提示',
			content: '是否删除该附件？',
			success: function (res) {
				if (res.confirm) {
					info.value[type].splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
	
	
    onMounted(() => {
        init()
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
	@import '../../style/common.scss';
</style>