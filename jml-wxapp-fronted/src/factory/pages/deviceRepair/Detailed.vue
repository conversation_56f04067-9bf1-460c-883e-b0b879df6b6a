<template>
    <div class="page pd100">
        <div class="card_info">
			<view class="tabInfo"> {{ info.jobType }} - {{ info.prodLineOrDept }} </view>
            <div class="card">
				<div class="card_head">
					<div class="card_head_tit2" style="margin-top: 0;">
                        {{ info.name }}
						<!-- <view> <u-icon name="calendar" :label="`${info.replaceDate} ${info.replaceStartTime}/${info.replaceEndTime}`" labelSize="12"></u-icon> </view> -->
                    </div>
					<div v-if="info.impactLevel" class="head_status">
						<u-tag size="mini" :bgColor="impactLevel[info.impactLevel].bgColor" :borderColor="impactLevel[info.impactLevel].bgColor" :text="info.impactLevel"></u-tag>
						<!-- <image  class="van-icon__image" width="40"  mode="widthFix" src="/static/icon/mileage_incomplete.png" /> -->
					</div>
					<u-divider></u-divider>
					<progress-status v-if="info.repairStatus=='已取消'" dataKey="dval" dataLabel="dname" :status_list="dict['repair_status_c']" :active="info.repairStatus" readonly :data="info"></progress-status>
					<progress-status v-else dataKey="dval" dataLabel="dname" :status_list="dict['repair_status']" :active="info.repairStatus" readonly :data="info"></progress-status>
					
					<!-- v-if="info.repairProgressList && info.repairProgressList.length>0" -->
					<div v-if="info.addPartsPurchase==1"  class="card_head_tit3 mt10 align-items_center ">
						<!-- <view>零配件采购</view> -->
						<u--text size="14" bold color="#666" :prefixIcon="`${baseURL}/assets/fac/repair.png`" iconStyle="font-size: 14px;color:#A1A4A9; margin-right: 5upx;"
						text="零配件采购"></u--text>
						<view style="margin-left: auto;"><u-tag size="mini" borderColor="#849EB2" bgColor="#849EB2"  text="去发起"
							@click="router.push({name: 'partsPurchaseEdit',params: {deviceRepairId:routerQuery.id,deviceRepairName:info.name}})"
						></u-tag>
						</view>
					</div>
					
                </div>
				
            </div>
        </div>
		
		<div v-if="showDispense('assign')" class="card_info">
			<div class="card">
				<div class="card_head">
					<div class="card_head_tit2" style="margin-top: 0;">
					   维修分配信息
					</div>
				</div>
				<u--form labelPosition="top" labelWidth="auto" ref="form1" >
					<u-form-item label="分配维修员" required prop="repairerStaffId">
						<load-selector placeholder="请选择" v-model="operateInfo.repairerStaffId" dataText="name" filterable dataValue="staffId"   load_url="/api/jml/staff/list4Repairer"></load-selector>
					</u-form-item>
					
					<u-form-item label="维修机器状态" required prop="repairerStatus">
						<zxz-uni-data-select  placeholder="请选择" v-model="operateInfo.repairerStatus" :localdata="dict['repairer_status']"></zxz-uni-data-select>
					</u-form-item>
					<u-form-item label="分配备注" prop="assignRemark"  >
						<u--textarea :count="!!operateInfo.assignRemark" maxlength="800"  v-model="operateInfo.assignRemark"  class="int"   placeholder="请输入"  />
					</u-form-item>
				</u--form>	
			</div>
		</div>
		
		<template v-if="info.repairProgressList && info.repairProgressList.length>0">
		<div  class="card_info">
			<div class="card">
				<div class="card_head">
					<div class="card_head_tit2" style="margin-top: 0;">
					   维修进展
					</div>
				</div>
				
				<div class="detail_list">
					<uni-table border stripe emptyText="暂无更多数据">
						<!-- 表头行 -->
						<uni-tr>
							<uni-th width="150" align="center">维修内容</uni-th>
							<uni-th width="160" align="center">维修开始时间</uni-th>
							<uni-th width="160" align="center">维修结束时间</uni-th>
							<uni-th width="120" align="center">维修员</uni-th>
							<uni-th operate width="120" align="center">操作</uni-th>
						</uni-tr>
						<!-- 表格数据行 -->
						<uni-tr v-for="(detail,i) in info.repairProgressList" :key="i">
							<uni-td align="center"> {{detail.repairContent}} </uni-td>
							<uni-td align="center"> {{detail.repairStartTime}}</uni-td>
							<uni-td align="center"> {{detail.repairEndTime}}</uni-td>
							<uni-td align="center"> {{detail.repairStaffName}}</uni-td>
							<uni-td  operate align="center">
								<u-button class="uni-button" @click="handleShow(detail,i)" color="#849EB2" :plain="true" :hairline="true" size="mini" type="primary">查看</u-button>
							</uni-td>
						</uni-tr>
					</uni-table>
				</div>
			</div>
		</div>
		
		<div v-if="showDispense('complete') && !dialogShow" class="card_info">
			<div class="card">
				<div class="card_head">
					<div class="card_head_tit2" style="margin-top: 0;">
					   设备维修信息
					</div>
				</div>
				<u--form labelPosition="top" labelWidth="auto" ref="form1" >
					
					<u-form-item label="维修内容" prop="repairContent"  required>
						<u--textarea :count="!!operateInfo.repairContent" maxlength="800"  v-model="operateInfo.repairContent"  class="int"   placeholder="请输入"  />
					</u-form-item>
					<u-form-item label="原因分析" prop="faultAnalysis"  required>
						<u--textarea :count="!!operateInfo.faultAnalysis" maxlength="800"  v-model="operateInfo.faultAnalysis"  class="int"   placeholder="请输入"  />
					</u-form-item>
					
					<u-form-item label="处理方式" prop="solution" required>
						<u-radio-group v-model="operateInfo.solution" activeColor="#849EB2" iconPlacement="left">
						    <u-radio v-for="(item,index) in dict['solution']" :key="index" :customStyle="{marginRight: '16px'}"  :label="item.dname" :name="item.dval"></u-radio>
						</u-radio-group>
					</u-form-item>
					
					<u-form-item label="预防方法" prop="preventionMethod" required >
						<u--textarea :count="!!operateInfo.preventionMethod" maxlength="800"  v-model="operateInfo.preventionMethod"  class="int"   placeholder="请输入"  />
					</u-form-item>
					<u-form-item label="完成照片" prop="completionImg" >
						<u-upload
						    :max-count="3"
						    multiple
						    @after-read="afterRead($event,'completionImg')"
						    :deletable="true"
							:sizeType="['compressed']"
						    :before-delete="(file,detail)=>beforeDelete(file,detail)"
							@delete="beforeDelete($event,'completionImg')"
							:fileList="operateInfo.completionImg"
						>
						</u-upload>
					</u-form-item>
					<u-form-item label="是否需要更换配件" prop="replacePart" required>
						<u-radio-group v-model="operateInfo.replacePart" activeColor="#849EB2" iconPlacement="left">
						    <u-radio v-for="(item,index) in dict['replace_part']" :key="index" :customStyle="{marginRight: '16px'}"  :label="item.dname" :name="item.dval"></u-radio>
						</u-radio-group>
					</u-form-item>
					<template v-if="operateInfo.replacePart=='1'">
						<u-form-item label="配件名称" required prop="partName">
							<u--input  v-model="operateInfo.partName"  class="int"   placeholder="请输入"  />
						</u-form-item>
						<u-form-item label="配件型号" required prop="partModel">
							<u--input  v-model="operateInfo.partModel"  class="int"   placeholder="请输入"  />
						</u-form-item>
						<u-form-item label="配件数量" required prop="partQty">
							<u--input type="number" v-model.number="operateInfo.partQty"  class="int"   placeholder="请输入"  />
						</u-form-item>
						<u-form-item label="完成照片" prop="partImg" >
							<u-upload
								:max-count="2"
								multiple
								@after-read="afterRead($event,'partImg')"
								:deletable="true"
								:sizeType="['compressed']"
								:before-delete="(file,detail)=>beforeDelete(file,detail)"
								@delete="beforeDelete($event,'partImg')"
								:fileList="operateInfo.partImg"
							>
							</u-upload>
						</u-form-item>
					</template>
				</u--form>	
			</div>
		</div>
		</template>
		
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit2" style="margin-top: 0;">
                       故障基本信息
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
<!-- 					<div class="item">
					    <div class="left">岗位信息</div><div class="rigth">{{info.jobInfo}}</div>
					</div> -->
					<div class="item">
					    <div class="left">设备名称</div><div class="rigth">{{info.deviceName}}</div>
					</div>
					<div class="item">
					    <div class="left">发生位置</div><div class="rigth">{{info.location}}</div>
					</div>
					
					<view class="item">
						<view class="left">维修是否接受</view>
						<view class="rigth">{{info.repairAccepted == '1' ? '已接受' : '否' }}</view>
					</view>
					<view class="item" v-if="info.repairAccepted == '1'">
						<view class="left">接受时间</view>
						<view class="rigth">{{info.repairAcceptedTime}}</view>
					</view>
					
					
					<div class="item">
					    <div class="left">故障照片</div>
						<div v-if="info.damageImg" class="rigth">
							<image v-for="(item,index) in info.damageImg.split(';')" :key="index"   mode="widthFix"  :src="imgBase+item" />
						</div>
					</div>
					
                    <div class="item">
                        <div class="left">故障描述</div>
                        <div class="rigth">{{info.faultDesc}}</div>
                    </div>
                </div>
				
                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.createdTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.updateTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>
		
		<div v-if="info.repairHistoryList" class="card_info">
		    <div class="card">
		        <div class="card_head">
		            <div class="card_head_tit2" style="margin-top: 0;">
		               设备维修处理历史 
		            </div>
		        </div>
		        <u-divider></u-divider>
		        <div class="card_main">
					<u-steps :current="info.repairHistoryList.length" direction="column">
						<u-steps-item v-for="(item,index) in info.repairHistoryList" :key="index" :title="`${item.createdStaffName} | ${item.createdTime}` ">
							<template #title>
								<view class="u-text__value u-text__value--content">
									{{item.createdStaffName}} | {{item.createdTime}}
								</view>
								
							</template>
							<template #desc>
								<view class="value--content">  
									<view>{{item.remark}}</view>
								</view>
							</template>
						</u-steps-item>

					</u-steps>
		        </div>
				
			</div>
		</div>
		
		
		<!-- 弹框操作 -->
		<u-modal v-model:show="dialogShow" @confirm="dialogConfirm"  @cancel=" resetDialogShow" confirmColor="#849EB2" :title="operateInfo.dialogTitle" show-cancel-button>
		    <div class="addInfo">
		        <!-- <div class="card"> -->
				<u--form labelPosition="top"  v-if="operateInfo.operateType=='repair'" labelWidth="auto" >
					<!-- <u-form-item label="维修开始时间"  prop="repairStartTime" required>
						<u--input v-model="operateInfo.repairStartTime"  class="int" readonly  placeholder="请选择"  />
						<template #right><u-icon name="calendar" size="22" @click="onDateTimeShow('repairStartTime','datetime')"></u-icon></template>
					</u-form-item>
					<u-form-item label="维修结束时间"  prop="repairEndTime" required>
						<u--input v-model="operateInfo.repairEndTime"  class="int" readonly  placeholder="请选择"  />
						<template #right><u-icon name="calendar" size="22" @click="onDateTimeShow('repairEndTime','datetime')"></u-icon></template>
					</u-form-item> -->
					<!-- <u-form-item label="维修时间"  prop="repairStartTime" required>
						<uni-datetime-picker 
							start-placeholder="开始时间"
							end-placeholder="结束时间"
							type="datetimerange" rangeSeparator="至" :border="false"/>
					</u-form-item> -->	
					
					<u-form-item label="维修开始时间"  prop="repairStartTime" required>
						<uni-datetime-picker
							type="datetime"
							placeholder="开始时间" 
							:border="false"
							v-model="operateInfo.repairStartTime"
						/>
					</u-form-item>
					<u-form-item label="维修结束时间"  prop="repairEndTime" required>
						<uni-datetime-picker
							type="datetime"
							placeholder="结束时间" 
							:border="false"
							v-model="operateInfo.repairEndTime"
						/>
					</u-form-item>
					
		            <u-form-item label="维修内容" prop="repairContent" required>
		            	<u--textarea :count="!!operateInfo.repairContent"   v-model="operateInfo.repairContent"  class="int" maxlength="800"  :placeholder="`请填写`"  />
		            </u-form-item>
					<u-form-item label="维修照片" prop="repairImg" required>
						<u-upload
						    :max-count="3"
						    multiple
						    @after-read="afterRead($event,'repairImg')"
						    :deletable="true"
							:sizeType="['compressed']"
						    :before-delete="(file,detail)=>beforeDelete(file,detail)"
							@delete="beforeDelete($event,'repairImg')"
							:fileList="operateInfo.repairImg"
						>
						</u-upload>
					</u-form-item>
				</u--form>
				<u--form labelPosition="top"  v-if="operateInfo.operateType=='assign'" labelWidth="auto" >
				    <u-form-item label="分配维修员" required prop="repairerStaffId">
				    	<load-selector placeholder="请选择" v-model="operateInfo.repairerStaffId" dataText="name" filterable dataValue="staffId"   load_url="/api/jml/staff/list4Repairer"></load-selector>
				    </u-form-item>
				    
				    <u-form-item label="维修机器状态" required prop="repairerStatus">
				    	<zxz-uni-data-select  placeholder="请选择" v-model="operateInfo.repairerStatus" :localdata="dict['repairer_status']"></zxz-uni-data-select>
				    </u-form-item>
				    <u-form-item label="分配备注" prop="assignRemark"  >
				    	<u--textarea :count="!!operateInfo.assignRemark" maxlength="800"  v-model="operateInfo.assignRemark"  class="int"   placeholder="请输入"  />
				    </u-form-item>
				</u--form>
				<u--form labelPosition="top"  v-if="operateInfo.operateType=='cancel'" labelWidth="auto" >
				    <u-form-item label="取消原因" prop="cancelReason"  >
				    	<u--textarea :count="!!operateInfo.cancelReason" maxlength="800"  v-model="operateInfo.cancelReason"  class="int"   placeholder="请输入"  />
				    </u-form-item>
				</u--form>
				<u--form labelPosition="top"  v-if="operateInfo.operateType=='confirm'" labelWidth="auto" >
				    <u-form-item label="您对本次问题处理结果满意吗?" prop="serviceRating"  >
						<u-rate v-model="operateInfo.serviceRating"  active-color="#FA3534" inactive-color="#b2b2b2" gutter="20"></u-rate>
				    </u-form-item>
				</u--form>
		        <!-- </div> -->
		    </div>
		</u-modal>
		
		<template v-if="!dialogShow">
		<!-- <footer-btn v-if="[deviceRepairStatus.draft,deviceRepairStatus.refuse].includes(info.repairStatus)"
			:cancelBtnShow="false"
			:confirmBtnShow="hasFactoryPermission('deviceRepair','U')"
			confirm_btn_text="编辑" 
			@onconfirm="router.push({name: 'deviceRepairEdit',params: {id:routerQuery.id}})" 
		>
		</footer-btn> -->
		<!-- v-if="info.btnList || info.showRepairAccept == '1'"  :confirmBtnShow="info.showRepairAccept == '1'" -->
        <footer-btn  :cancelBtnShow="false" v-if="info.btnList || info.showRepairAccept == '1'"  :confirmBtnShow="info.showRepairAccept == '1'"  confirm_btn_text="接受" @onconfirm="onAccept(info.id)">
			<div  v-for="(item,index) in info.btnList"  :key="index"
				:style="`background: ${deviceRepairStatusInfo[item.btnValue]?.btnColor};`" 
				@click="hdBtn(item)" :class="item.btnCode" class="confirm_btn btn">
				<u-icon
					v-if="deviceRepairStatusInfo[item.btnValue]?.icon"
					class="btn_icon"
					size="16"
					color="#fff"
					:name="deviceRepairStatusInfo[item.btnValue]?.icon"
				></u-icon>
				<view>{{item.btnName}}</view>
			</div>
		</footer-btn>
		</template>
		
		<!-- 底部弹出 -->
		<u-popup :show="showPopInfo" :round="10" closeable mode="bottom" @close="showPopInfo = false">
		    <div class="pop_info">
		        <div class="pop_info_tit">维修进展明细</div>
		        <div class="card_info">
		            <div class="card">
		                <div class="card_main">
		                    <div class="item">
		                        <div class="left">维修内容</div><div class="rigth">{{popinfo.repairContent}}</div>
		                    </div>
		                    <div class="item">
		                        <div class="left">维修开始时间</div><div class="rigth">{{popinfo.repairStartTime}}</div>
		                    </div>
							<div class="item">
							    <div class="left">维修结束时间</div><div class="rigth">{{popinfo.repairEndTime}}</div>
							</div>
		                    <div class="item">
		                        <div class="left">维修员</div><div class="rigth">{{popinfo.repairStaffName}}</div>
		                    </div>
		                    
		                    <div class="item">
		                        <div class="left">维修照片</div><div class="rigth">
		                            <!-- <van-image v-for="(item,index) in popinfo.files" :key="index"   @click="showImagePreview([imgBase+item])" fit="fill" width="100" :src="imgBase+item" /> -->
		                            <image v-for="(item,index) in popinfo.repairImg" :key="index"   @click="showImagePreview(popinfo.repairImg,index)"  mode="widthFix"  :src="imgBase+item" />
		                        </div>
		                    </div>
		                   <!-- <div class="item">
		                        <div class="left">备注信息</div><div class="rigth">{{popinfo.remark}}</div>
		                    </div>
		                    <div class="item">
		                        <div class="left">创建日期</div><div class="rigth">{{popinfo.created_time}}</div>
		                    </div> -->
		                </div>
		            </div>
		        </div>
		    </div>
		</u-popup>
		
		<u-datetime-picker
			:show="dateTimeShow"
			v-model="currentTime"
			confirmColor="#849EB2"
			closeOnClickOverlay
			@confirm="onDateTimeConfirm"
			@cancel="dateTimeShow = false"
			@close="dateTimeShow = false"
			:mode="dateMode"
		></u-datetime-picker>
		<floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
	import ProgressStatus from '@/components/ProgressStatus.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import {queryDetails,operate,accept}  from  '../../api/deviceRepair'
import {deviceRepairStatusInfo,impactLevel} from '../../constant/index.js'
import LoadSelector from '@/components/LoadSelector.vue';
import { isArray } from 'lodash';
import {upload,dictTree,asyncUpload}  from  '@/api/common'
import {useDateTime}  from  '../../vueUse/datetimeUse'
    const prop =  defineProps(['id'])
    const router = useRouter();
    const util = inject("util");
	const dictStore = useDictStore()
    const routerQuery =  prop;
    const info = ref({
    });
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const init = async() =>{
		await dictStore.getDictBatch(['impact_level','product_line','repairer_status','repair_status_c','solution','replace_part','repair_status']);
        getInfo()
    }
	
	const showDispense = computed(()=>(code)=>{ 
		if(!info.value.btnList || !code) return false
		else{
			return  !!info.value.btnList.find(i=>i.btnValue===code)
		}
	})
	
	//操作用到
	const operateInfo = ref({
		id:routerQuery.id,
		deviceRepairId:routerQuery.id,
	})
	const resetDialogShow = ()=>{
		operateInfo.value = {
			id:routerQuery.id,
		}
		dialogShow.value = false
	}
	const afterRead = async(event,type) =>{
		let files = [].concat(event.file)
		if(!operateInfo.value[type]) operateInfo.value[type] = [];
		let fileListLen =  operateInfo.value[type].length
		files.map((item) => {
			operateInfo.value[type].push({
				...item,
				status: 'uploading',
				message: '上传中'
			})
		})
		for (let i = 0; i < files.length; i++) {
			try {
				const {data} = await asyncUpload(files[i].url)
				let item = operateInfo.value[type][fileListLen];
				operateInfo.value[type].splice(fileListLen, 1, {
				  ...item,
				  status: 'success',
				  message: '',
				  url: data.url,
				});
				fileListLen++;
			} catch (e) {
				console.log(e)
				uni.showToast({
					title: "图片上传失败",
					icon: "none"
				})
			}
		}
	}
	
	const beforeDelete = ({file,index},type) =>{
		uni.showModal({
			title: '提示',
			content: '是否删除该附件？',
			success: function (res) {
				if (res.confirm) {
					operateInfo.value[type].splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
	
	
	let isSubmit = false
	const dialogShow = ref(false)
	const dialogConfirm = ()=>{
		debugger
	 //    if((!approvalInfo.remark || approvalInfo.remark=='')) return uni.showToast({
		// 	title: '备注必须填写',
		// 	duration: 1500,
		// 	icon:'none'
		// });
		submit()
	}
	
	const hdBtn = (data) =>{
		
		if(deviceRepairStatusInfo[data.btnValue]?.dialogShow){
			operateInfo.value.operateType = data.btnCode;
			operateInfo.value.dialogTitle = deviceRepairStatusInfo[data.btnValue]?.dialogTitle;
			dialogShow.value = true
		}else{
			submit(data.btnCode)
		}
	}
	
	const submit =(type)=>{
		
		if(isSubmit) return false
		if(!type && !operateInfo.value.operateType) return;
		let {operateType,dialogTitle,partImg,completionImg,repairImg,...post} = operateInfo.value;
		if(partImg && isArray(partImg)){
			partImg.forEach((imgInfo,index) => {
			    post[`partImg${index+1}`] = imgInfo.url
			});
		}
		if(completionImg && isArray(completionImg)){
			completionImg.forEach((imgInfo,index) => {
			    post[`completionImg${index+1}`] = imgInfo.url
			});
		}
		if(repairImg && isArray(repairImg)){
			repairImg.forEach((imgInfo,index) => {
			    post[`repairImg${index+1}`] = imgInfo.url
			});
		}
		
		isSubmit = true;
		operate(type??operateType,post).then(res=>{
			uni.showModal({
				title: '提示',
				content: res.msg,
				showCancel:false,
				success: function (confirmres) {
					isSubmit = false
					dialogShow.value = false
					operateInfo.value = {
						id:routerQuery.id,
					}
					getInfo()
					// if (confirmres.confirm) {
					// 	if(prop.isComponent)emit('confirm', res)
					// 	else{
					// 		uni.$emit("refresh", {refresh: true}); 
					// 		router.back();
					// 	}
					// } else if (confirmres.cancel) {
					// 	uni.$emit("refresh", {refresh: true}); 
					// 	console.log('用户点击取消');
					// }
				}
			});
		}).catch(e=>{
			isSubmit = false
		})
	}
	
	const showPopInfo = ref(false)
	const popinfo = ref({})
	const {dateTimeShow,field,currentTime,onDateTimeShow,onDateTimeConfirm,dateMode} = useDateTime(operateInfo,'datetime')
	const handleShow = async(detail,index) =>{
	    // let {data} = await queryItemDetails(info.id)
	    popinfo.value = {...detail};
	    if(detail.repairImg){
	        popinfo.value.repairImg = detail.repairImg.split(';')
	    }else{
	        popinfo.value.repairImg = []
	    }
	    showPopInfo.value = true
	}
	const onAccept = (id)=>{
		uni.showModal({
		    title: '提示',
		    content: '是否接受该任务？',
			success: function (unires) {
				if (unires.confirm) {
					accept(id).then((res) => {
					    uni.showModal({
					        title:'提示',
					        showCancel:false,
					        content: res.msg ?? '操作成功',
					    }).then((aa) => {
					        getInfo()
					    })
					}).catch((err) => {
					});
				}
			}
		})
	}
	
	onShow(() => {
		 init()
	})
	onMounted(() => {
	   
	})
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
	@import '@/styles/factory.scss';
	@import '../../style/common.scss';
	.card_info{
		.card{
			.item{
				padding-bottom: 5px;
				.left{
					line-height: 1.2;
				}
				.rigth{
					text-align: right;
				}
			}
		}
	}
	.addInfo{
		padding:0 10px;
		position: relative;
		border-radius: var(--card-border-radius);
		line-height: 1.4;
		width: 100%;
		.card{
			background: none;
			padding: var(--card-padding);
			position: relative;
			border-radius: var(--card-border-radius);
			line-height: 1.4;
			.card_head{
				display: flex;
				align-items: center;
				justify-content: center;
				// border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
				padding: 15px 0 10px 0;
				&_tit{
					display: flex;
					align-items: center;
					font-size: 18px;
					font-weight: 600;
					color: #333333;
					span{
						font-weight: 700;
						font-size: 16px;
						color: #000000;
						margin-right: 5px;
					}
				}
			}
		}
		:deep(){
			.int{
				background: none !important;
				.van-field__value{
					background: #fff;
				}
				// &.van-cell:after{
				//     content: none;
				// }
			}
		}
	}
	
	
</style>