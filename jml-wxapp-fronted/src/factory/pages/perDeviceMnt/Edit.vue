<template>
    <div class="page pd100">
        <div class="card_info">
			<u--form labelPosition="top" labelWidth="auto" ref="form1" >
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">第三方维保</div>
                </div>
					<div class="card_head_tit2">保养</div>
					<view class="u-form-item__body__left__content" style="font-size: 15px;">
						<uni-text class="u-form-item__body__left__content__label">
							<span>项目类型：</span>
							{{ info.itemType }}
						</uni-text>
					</view>
					<u-form-item label="保养设备" required prop="maintenanceEq">
						<u--input v-model="info.maintenanceEq" class="int"   placeholder="请输入"  />
					</u-form-item>
					
					<u-form-item label="保养日期" required  prop="maintenanceDate">
						<u--input v-model="info.maintenanceDate"  class="int" readonly  placeholder="日期"  />
						<template #right><u-icon name="calendar" size="22" @click="onDateTimeShow('maintenanceDate')"></u-icon></template>
					</u-form-item>
					
					<u-form-item label="保养记录照片" prop="maintenanceRecordImg" >
						<u-upload
						    :max-count="5"
						    multiple
						    @after-read="afterRead($event,'maintenanceRecordImg')"
						    :deletable="true"
							:sizeType="['compressed']"
						    :before-delete="(file,detail)=>beforeDelete(file,detail)"
							@delete="beforeDelete($event,'maintenanceRecordImg')"
							:fileList="info.maintenanceRecordImg"
						>
						</u-upload>
					</u-form-item>
					
					<u-form-item label="保养后机械状态"  prop="postMaintenanceStatus">
						<zxz-uni-data-select  placeholder="请选择" v-model="info.postMaintenanceStatus" dataText="dval" dataValue="dname" :localdata="dict['post_maintenance_status']"></zxz-uni-data-select>
					</u-form-item>
					
					<u-form-item label="是否开维修单" prop="isRepairOrderCreated" >
						<u-radio-group v-model="info.isRepairOrderCreated" activeColor="#849EB2" iconPlacement="left">
						    <u-radio :customStyle="{marginRight: '16px'}"  label="是" name="1"></u-radio>
						    <u-radio  label="否" name="0"></u-radio>
						</u-radio-group>
					</u-form-item>
					
					<u-form-item label="分配第三方维修员" required prop="tpStaffId">
						<load-selector v-model="info.tpStaffId" value-key="id"  value-fild="id"  load_url="/api/perDeviceMnt/queryTpServiceProviderList
"></load-selector>
					</u-form-item>
					
					
					<div class="card_head_tit2 mt20">维保</div>
					<u-form-item label="维保意见"  prop="maintenanceOpinion">
						<u--input v-model="info.maintenanceOpinion" class="int"   placeholder="请输入"  />
					</u-form-item>
					
					<u-form-item label="维保意见照片" prop="maintenanceImg" >
						<u-upload
						    :max-count="5"
						    multiple
						    @after-read="afterRead($event,'maintenanceImg')"
						    :deletable="true"
							:sizeType="['compressed']"
						    :before-delete="(file,detail)=>beforeDelete(file,detail)"
							@delete="beforeDelete($event,'maintenanceImg')"
							:fileList="info.maintenanceImg"
						>
						</u-upload>
					</u-form-item>
					
					<u-form-item label="确认签名单照片" prop="confirmSignImg" >
						<u-upload
						    :max-count="5"
						    multiple
						    @after-read="afterRead($event,'confirmSignImg')"
						    :deletable="true"
							:sizeType="['compressed']"
						    :before-delete="(file,detail)=>beforeDelete(file,detail)"
							@delete="beforeDelete($event,'confirmSignImg')"
							:fileList="info.confirmSignImg"
						>
						</u-upload>
					</u-form-item>
					
					<u-form-item label="备注" prop="remark" >
						<u--textarea :count="!!info.remark" maxlength="800"  v-model="info.remark"  class="int"   placeholder="备注"  />
					</u-form-item>
					
				</div>
			</u--form>
        </div>
		<u-datetime-picker
			:show="dateTimeShow"
			v-model="currentTime"
			confirmColor="#849EB2"
			closeOnClickOverlay
			@confirm="onDateTimeConfirm"
			@cancel="dateTimeShow = false"
			@close="dateTimeShow = false"
			:mode="dateMode"
		></u-datetime-picker>
        <footer-btn :cancelBtnShow="false"  confirm_btn_text="保存" @onconfirm="save()"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import LoadSelector from '@/components/LoadSelector.vue';
import Selector from '@/components/Selector.vue';
import dayjs from "dayjs";
import {queryDetails,saveEdit}  from  '../../api/perDeviceMnt'
import {queryListByStaff}  from  '@/api/company'
import {useDateTime}  from  '../../vueUse/datetimeUse'
import {upload,asyncUpload}  from  '@/api/common'
import { isArray, map } from 'lodash';
	const prop =  defineProps({
			id: String,
			isComponent: {
				type: Boolean,
				default: false,
			},
			accountId:{
				type: String,
				default: null,
			}
		})
	const routerQuery = prop;
	const appStore = useAppStore();
	const router = useRouter();
	const { userInfo } = useUserStore();

    // 相关字典
    const dictStore = useDictStore()
    const companyList = ref([])
	const util = inject("util");
    const deleteFild = ['accName','createdStaffName','updateStaffName','salesLeadName','marketingName','areaName'];
    const requiredFild = [
        // {key:'name',message:'请填写机会名称',required: true,},
        // {key:'amount',message:'请填写金额',required: true,},
        // {key:'type',message:'请选择机会类型',required: true,},
    ]
    const save = () => {
        for(let index  in requiredFild){ 
            var item =  requiredFild[index]
            if(item.required && (!info.value[item.key] || info.value[item.key]=='')){
            	return uni.showToast({
            		title: item.message,
            		duration: 1500,
            		icon:'none'
            	});
            }
        };
		if(isArray(info.value.maintenanceRecordImg)) {
		    info.value.maintenanceRecordImg.forEach((item,index) => {
		        info.value[`maintenanceRecordImg${index+1}`] = item.url
		    });
		}
		if(isArray(info.value.maintenanceImg)) {
		    info.value.maintenanceImg.forEach((item,index) => {
		        info.value[`maintenanceImg${index+1}`] = item.url
		    });
		}
		if(isArray(info.value.confirmSignImg)) {
		    info.value.confirmSignImg.forEach((item,index) => {
		        info.value[`confirmSignImg${index+1}`] = item.url
		    });
		}
        // let postData = {}
        // for(var key in info.value){
        //     if(!deleteFild.includes(key))
        //         postData[key] = info.value[key];
        // }
        saveEdit(info.value).then((res)=>{
            uni.showModal({
            	title: '提示',
            	content: info.value.id ?'修改成功':'保存成功',
            	showCancel:false,
            	success: function (confirmres) {
            		if (confirmres.confirm) {
            			if(prop.isComponent)emit('confirm', res)
            			else{
            				uni.$emit("refresh-perDeviceMnt", {refresh: true}); 
            				router.back();
            			}
            		} else if (confirmres.cancel) {
            			console.log('用户点击取消');
            		}
            	}
            });
        })
    }
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
			
			if(res.data.maintenanceRecordImg){
			    info.value.maintenanceRecordImg = res.data.maintenanceRecordImg.split(';').map(i=>{
					return{url:import.meta.env.VUE_APP_BASE_IMG + i}
				})
			}else{
			    info.value.maintenanceRecordImg = []
			}
			if(res.data.maintenanceImg && res.data.maintenanceImg!=''){
			    info.value.maintenanceImg = res.data.maintenanceImg.split(';').map(i=>{
					return{url:import.meta.env.VUE_APP_BASE_IMG + i}
				})
			}else{
			    info.value.maintenanceImg = []
			}
			if(res.data.confirmSignImg && res.data.confirmSignImg!=''){
			    info.value.confirmSignImg = res.data.confirmSignImg.split(';').map(i=>{
					return{url:import.meta.env.VUE_APP_BASE_IMG + i}
				})
			}else{
			    info.value.confirmSignImg = []
			}
        }).catch((err) => {
        });
    }
    const info = ref({
		itemType:'外包保养',
    });
	const {dateTimeShow,field,currentTime,onDateTimeShow,onDateTimeConfirm,dateMode} = useDateTime(info)
	
	const afterRead = async(event,type) =>{
		let files = [].concat(event.file)
		if(!info.value[type]) info.value[type] = [];
		let fileListLen =  info.value[type].length
		files.map((item) => {
			info.value[type].push({
				...item,
				status: 'uploading',
				message: '上传中'
			})
		})
		for (let i = 0; i < files.length; i++) {
			try {
				const {data} = await asyncUpload(files[i].url)
				let item = info.value[type][fileListLen];
				info.value[type].splice(fileListLen, 1, {
				  ...item,
				  status: 'success',
				  message: '',
				  url: data.url,
				});
				fileListLen++;
			} catch (e) {
				console.log(e)
				uni.showToast({
					title: "图片上传失败",
					icon: "none"
				})
			}
		}
	}
	
	const beforeDelete = ({file,index},type) =>{
		uni.showModal({
			title: '提示',
			content: '是否删除该附件？',
			success: function (res) {
				if (res.confirm) {
					info.value[type].splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
	
	
    const init = async() =>{
		await dictStore.getDictBatch(['post_maintenance_status']);
        if(routerQuery.id){
            getInfo()
        }
    }
    onMounted(() => {
        init()
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
	:deep(.card_info){
		.u-form-item__body{
			padding: 15px 0 0 0;
		}
	}
</style>