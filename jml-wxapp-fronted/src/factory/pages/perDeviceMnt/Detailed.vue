<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        {{ info.name }}
						<view> <u-icon name="calendar" :label="`${info.maintenanceDate}`" labelSize="12"></u-icon> </view>
                    </div>
                </div>
            </div>
        </div>
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                       保养
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
					<div class="item">
					    <div class="left">项目类型</div><div class="rigth">{{info.itemType}}</div>
					</div>
					<div class="item">
					    <div class="left">保养设备</div><div class="rigth">{{info.maintenanceEq}}</div>
					</div>
					<div class="item">
					    <div class="left">保养日期</div><div class="rigth">{{info.maintenanceDate}}</div>
					</div>
                    <div class="item">
                        <div class="left">保养记录照片</div>
						<div v-if="info.maintenanceRecordImg" class="rigth">
							<image v-for="(item,index) in info.maintenanceRecordImg.split(';')" :key="index"   mode="widthFix"  :src="imgBase+item" />
						</div>
                    </div>
                    <div class="item">
                        <div class="left">保养后机械状态</div><div class="rigth">{{info.postMaintenanceStatus}}</div>
                    </div>
                    <div class="item">
                        <div class="left">是否开维修单</div><div class="rigth">{{info.isRepairOrderCreated==1?'是':'否'}}</div>
                    </div>
					<div class="item">
					    <div class="left">分配第三方维修员</div><div class="rigth">{{info.tpStaffName}}</div>
					</div>
                   
                </div>
				
				<div class="card_head_tit2 mt15">
				    维保
				</div>
				<u-divider></u-divider>
				<div class="card_main">
				    <div class="item">
				        <div class="left">维保意见</div><div class="rigth">{{ info.maintenanceOpinion }}</div>
				    </div>
				    <div class="item">
				        <div class="left">维保意见照片</div>
						<div v-if="info.maintenanceImg" class="rigth">
							<image v-for="(item,index) in info.maintenanceImg.split(';')" :key="index"   mode="widthFix"  :src="imgBase+item" />
						</div>
				    </div>
					
					<div class="item">
					    <div class="left">确认签名单照片</div>
						<div v-if="info.confirmSignImg" class="rigth">
							<image v-for="(item,index) in info.confirmSignImg.split(';')" :key="index"   mode="widthFix"  :src="imgBase+item" />
						</div>
					</div>
					
					
					<div class="item">
					    <div class="left">备注</div>
					    <div class="rigth">{{info.remark}}</div>
					</div>
				</div>
				
                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.createdTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.updateTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>

        <footer-btn v-if="hasFactoryPermission('perDeviceMnt','U')"  :cancelBtnShow="false" @onconfirm="router.push({name: 'perDeviceMntEdit',params: {id:routerQuery.id}})" confirm_btn_text="编辑"></footer-btn>
		<floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import {queryDetails}  from  '../../api/perDeviceMnt'

    const prop =  defineProps(['id'])
    const router = useRouter();
    const util = inject("util");
	const dictStore = useDictStore()
    const routerQuery =  prop;
    const info = ref({
    });
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const init = async() =>{
        getInfo()
    }

	onShow(() => {
		 init()
	})
	onMounted(() => {
	   
	})
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
	@import '@/styles/factory.scss';
	.card_info{
		.card{
			.item{
				padding-bottom: 5px;
				.left{
					line-height: 1.2;
				}
				.rigth{
					text-align: right;
				}
			}
			
			.head_status{
				background: #999 !important;
			}
		}
	}
	
</style>