<template>
    <div class="page pd100">
		<u-sticky bgColor="#fff">
		    
			<div class="tool">
			    <u-tabs :current="active_tabs"	@change="tabChange" :list="dict['inspection_prod_line']" lineWidth="80" lineColor="#849EB2" keyName="dname"></u-tabs>
			</div>
		</u-sticky>
        <div class="card_info">
			<u--form labelPosition="top" labelWidth="auto" ref="form1" >
				<!-- EB线、肤感线 -->
				<view  v-show="active_tabs==0">
					<div class="card pd10">
						<view class="tabHead">
							<u-form-item label="巡查日期" labelWidth="120" labelPosition="left" prop="ebSkinInspectionDate">
								<!-- <view class="mlauto">{{info.ebSkinInspectionDate}}</view> -->
								
								<u--input inputAlign="right" v-model="info.ebSkinInspectionDate" readonly  placeholder="年 /月/日">	</u--input>
								<template #right><u-icon name="calendar" size="22" @click="onDateTimeShow('ebSkinInspectionDate','date')"></u-icon></template>
							</u-form-item>
							<view class="no-border">
							<u-form-item label="巡查时间" labelWidth="120" labelPosition="left"  prop="ebSkinInspectionStartTime">
								<view style="display: flex; width: 100%;">
									<view class="start int-time" style="flex: 1;">
										<u--input inputAlign="left" v-model="info.ebSkinInspectionStartTime"  class="int" readonly  placeholder="--:--"  >
											<template #suffix>
												<u-icon name="clock" size="20" @click="onRangeTimeShow('ebSkinInspectionStartTime','ebSkinInspectionEndTime')"></u-icon>
											</template>
										</u--input>
									</view>
									<u-icon name="minus"  size="22"></u-icon>
									
									<view class="end int-time" style="flex: 1;">
										<u--input inputAlign="right" v-model="info.ebSkinInspectionEndTime"  class="int" readonly  placeholder="--:--"  >
											<template #suffix>
												<u-icon name="clock" size="20" @click="onRangeTimeShow('ebSkinInspectionStartTime','ebSkinInspectionEndTime')"></u-icon>
											</template>
										</u--input>
									</view>
									
								</view>
							</u-form-item>
							
							<u-form-item label="巡查人" labelWidth="120" labelPosition="left" prop="inspectorStaffId">
								<view class="mlauto">{{info.inspectorStaffName}}</view>
							</u-form-item>
							</view>
							
							<u-form-item label="现场确认人" labelWidth="120" labelPosition="left" required prop="ebSkinConfirmStaffId">
								<zxz-uni-data-select filterable  placeholder="请选择" v-model="info.ebSkinConfirmStaffId" dataText="name" dataValue="id" :localdata="companyList"></zxz-uni-data-select>
								<!-- <load-selector placeholder="请选择" :clearable="false" v-model="info.ebSkinConfirmStaffId" load_url="/api/jml/staff/list4FactoryRoleStaff"></load-selector> -->
							</u-form-item>
							
						</view>
					</div>
					<!-- <view class="total-content" >
						<u--text size="14" bold color="#A1A4A9" :prefixIcon="`${baseURL}/assets/fac/d-null.png`" iconStyle="font-size: 14px;color:#A1A4A9; margin-right: 5upx;" text="10项"></u--text>
						<u--text size="14" bold color="#74D126" :prefixIcon="`${baseURL}/assets/fac/d-unchecked.png`" iconStyle="font-size: 14px;color:#74D126; margin-right: 5upx;" text="10项"></u--text>
						<u--text size="14" bold color="#E97056" :prefixIcon="`${baseURL}/assets/fac/d-checked.png`" iconStyle="font-size: 14px;color:#A1A4A9; margin-right: 5upx;" text="10项"></u--text>
					</view> -->
					<div class="card">
						<u-form-item label="检查空压机是否开启" required prop="ebSkinCompCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.ebSkinCompCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'ebSkinCompCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'ebSkinCompCheckImg')"
											:fileList="info.ebSkinCompCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.ebSkinCompCheckSol" maxlength="500" v-model="info.ebSkinCompCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="每台设备点检表是否完成《检查完成情况》 " required prop="ebSkinEqInspectDone">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.ebSkinEqInspectDone" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'ebSkinEqInspectDoneImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'ebSkinEqInspectDoneImg')"
											:fileList="info.ebSkinEqInspectDoneImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.ebSkinEqInspectDoneSol" maxlength="500" v-model="info.ebSkinEqInspectDoneSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="每台设备日保养表是否完成《检查完成情况》 " required prop="ebSkinDailyMaintDone">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.ebSkinDailyMaintDone" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'ebSkinDailyMaintDoneImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'ebSkinDailyMaintDoneImg')"
											:fileList="info.ebSkinDailyMaintDoneImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.ebSkinDailyMaintDoneSol" maxlength="500" v-model="info.ebSkinDailyMaintDoneSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查空气压力是否达标<0.7MPa> " required prop="ebSkinAirPressCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.ebSkinAirPressCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'ebSkinAirPressCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'ebSkinAirPressCheckImg')"
											:fileList="info.ebSkinAirPressCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.ebSkinAirPressCheckSol" maxlength="500" v-model="info.ebSkinAirPressCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查皮带位置是否跑偏《标记处》" required prop="ebSkinBeltPosCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.ebSkinBeltPosCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'ebSkinBeltPosCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'ebSkinBeltPosCheckImg')"
											:fileList="info.ebSkinBeltPosCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.ebSkinBeltPosCheckSol" maxlength="500" v-model="info.ebSkinBeltPosCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查覆膜机刀切刀是否完好" required prop="ebSkinBladeCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.ebSkinBladeCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'ebSkinBladeCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'ebSkinBladeCheckImg')"
											:fileList="info.ebSkinBladeCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.ebSkinBladeCheckSol" maxlength="500" v-model="info.ebSkinBladeCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查涂胶机侧面是否漏胶" required prop="ebSkinGlueLeakCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.ebSkinGlueLeakCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'ebSkinGlueLeakCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'ebSkinGlueLeakCheckImg')"
											:fileList="info.ebSkinGlueLeakCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.ebSkinGlueLeakCheckSol" maxlength="500" v-model="info.ebSkinGlueLeakCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查所有涂布机平辊是否完好" required prop="ebSkinCoaterCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.ebSkinCoaterCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'ebSkinCoaterCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'ebSkinCoaterCheckImg')"
											:fileList="info.ebSkinCoaterCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.ebSkinCoaterCheckSol" maxlength="500" v-model="info.ebSkinCoaterCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查上下料龙门吸盘是否完好" required prop="ebSkinLoaderCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.ebSkinLoaderCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'ebSkinLoaderCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'ebSkinLoaderCheckImg')"
											:fileList="info.ebSkinLoaderCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.ebSkinLoaderCheckSol" maxlength="500" v-model="info.ebSkinLoaderCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="三灯固化电控箱空调是否运行" required prop="ebSkinCuringBoxCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.ebSkinCuringBoxCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'ebSkinCuringBoxCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'ebSkinCuringBoxCheckImg')"
											:fileList="info.ebSkinCuringBoxCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.ebSkinCuringBoxCheckSol" maxlength="500" v-model="info.ebSkinCuringBoxCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查传送带是否清洁" required prop="ebSkinConvCleanliness">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.ebSkinConvCleanliness" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'ebSkinConvCleanlinessImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'ebSkinConvCleanlinessImg')"
											:fileList="info.ebSkinConvCleanlinessImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.ebSkinConvCleanlinessSol" maxlength="500" v-model="info.ebSkinConvCleanlinessSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
										
					</div>
				</view >
				
				<!-- 压贴线 -->
				<view  v-show="active_tabs==1">
					<div class="card pd10">
						<view class="tabHead">
							<u-form-item label="巡查日期" labelWidth="120" labelPosition="left" prop="pressInspectionDate">
								<!-- <view class="mlauto">{{info.ebSkinInspectionDate}}</view> -->
								
								<u--input inputAlign="right" v-model="info.pressInspectionDate" readonly  placeholder="年 /月/日">	</u--input>
								<template #right><u-icon name="calendar" size="22" @click="onDateTimeShow('pressInspectionDate','date')"></u-icon></template>
							</u-form-item>
							<view class="no-border">
							<u-form-item label="巡查时间" labelWidth="120" labelPosition="left"  prop="pressInspectionStartTime">
								<view style="display: flex; width: 100%;">
									<view class="start int-time" style="flex: 1;">
										<u--input inputAlign="left" v-model="info.pressInspectionStartTime"  class="int" readonly  placeholder="--:--"  >
											<template #suffix>
												<u-icon name="clock" size="20" @click="onRangeTimeShow('pressInspectionStartTime','pressInspectionEndTime')"></u-icon>
											</template>
										</u--input>
									</view>
									<u-icon name="minus"  size="22"></u-icon>
									
									<view class="end int-time" style="flex: 1;">
										<u--input inputAlign="right" v-model="info.pressInspectionEndTime"  class="int" readonly  placeholder="--:--"  >
											<template #suffix>
												<u-icon name="clock" size="20" @click="onRangeTimeShow('pressInspectionStartTime','pressInspectionEndTime')"></u-icon>
											</template>
										</u--input>
									</view>
									
								</view>
							</u-form-item>
							<u-form-item label="巡查人" labelWidth="120" labelPosition="left" prop="inspectorStaffId">
								<view class="mlauto">{{info.inspectorStaffName}}</view>
							</u-form-item>
							</view>
							
							<u-form-item label="现场确认人" labelWidth="120" labelPosition="left" required prop="pressConfirmStaffId">
								<!-- <load-selector placeholder="请选择" :clearable="false" v-model="info.pressConfirmStaffId" load_url="/api/jml/staff/list4FactoryRoleStaff"></load-selector> -->
								<zxz-uni-data-select filterable   placeholder="请选择" v-model="info.pressConfirmStaffId" dataText="name" dataValue="id" :localdata="companyList"></zxz-uni-data-select>
							</u-form-item>
							
						</view>
					</div>
					<!-- <view class="total-content" >
						<u--text size="14" bold color="#A1A4A9" prefixIcon="checkmark-circle-fill" iconStyle="font-size: 14px;color:#A1A4A9; margin-right: 5upx;" text="10项"></u--text>
						<u--text size="14" bold color="#74D126" prefixIcon="checkmark-circle-fill" iconStyle="font-size: 14px;color:#74D126; margin-right: 5upx;" text="10项"></u--text>
						<u--text size="14" bold color="#E97056" prefixIcon="checkmark-circle-fill" iconStyle="font-size: 14px;color:#A1A4A9; margin-right: 5upx;" text="10项"></u--text>
					</view> -->
					<div class="card">
						<u-form-item label="每台设备点检表是否完成《检查完成情况》" required prop="pressEqInspectionDone">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.pressEqInspectionDone" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'pressEqInspectionDoneImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'pressEqInspectionDoneImg')"
											:fileList="info.pressEqInspectionDoneImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.pressEqInspectionDoneSol" maxlength="500" v-model="info.pressEqInspectionDoneSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="每台设备日保养表是否完成《检查完成情况》" required prop="pressDailyMaintenanceDone">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.pressDailyMaintenanceDone" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'pressDailyMaintenanceDoneImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'pressDailyMaintenanceDoneImg')"
											:fileList="info.pressDailyMaintenanceDoneImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.pressDailyMaintenanceDoneSol" maxlength="500" v-model="info.pressDailyMaintenanceDoneSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查空气压力是否达标《0.7MPa》" required prop="pressAirPressureCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.pressAirPressureCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'pressAirPressureCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'pressAirPressureCheckImg')"
											:fileList="info.pressAirPressureCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.pressAirPressureCheckSol" maxlength="500" v-model="info.pressAirPressureCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查铺纸房湿度是否在范围《65%》" required prop="pressToolCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.pressToolCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'pressToolCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'pressToolCheckImg')"
											:fileList="info.pressToolCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.pressToolCheckSol" maxlength="500" v-model="info.pressToolCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查液压站是否漏油" required prop="pressSensorCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.pressSensorCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'pressSensorCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'pressSensorCheckImg')"
											:fileList="info.pressSensorCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.pressSensorCheckSol" maxlength="500" v-model="info.pressSensorCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查下料电箱房间空调是否打开" required prop="pressLocatorCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.pressLocatorCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'pressLocatorCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'pressLocatorCheckImg')"
											:fileList="info.pressLocatorCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.pressLocatorCheckSol" maxlength="500" v-model="info.pressLocatorCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查压机油缸是否漏油《看接油盘》" required prop="pressConveyorCleanliness">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.pressConveyorCleanliness" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'pressConveyorCleanlinessImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'pressConveyorCleanlinessImg')"
											:fileList="info.pressConveyorCleanlinessImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.pressConveyorCleanlinessSol" maxlength="500" v-model="info.pressConveyorCleanlinessSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查所有毛刷机是否开启" required prop="pressBrushMachineCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.pressBrushMachineCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'pressBrushMachineCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'pressBrushMachineCheckImg')"
											:fileList="info.pressBrushMachineCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.pressBrushMachineCheckSol" maxlength="500" v-model="info.pressBrushMachineCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查上下料龙门吸盘是否完好" required prop="pressBrushLoaderCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.pressBrushLoaderCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'pressBrushLoaderCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'pressBrushLoaderCheckImg')"
											:fileList="info.pressBrushLoaderCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.pressBrushLoaderCheckSol" maxlength="500" v-model="info.pressBrushLoaderCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
					</div>
				</view >
				
				<!-- 倒角线 -->
				<view  v-show="active_tabs==2">
					<div class="card pd10">
						<view class="tabHead">
							<u-form-item label="巡查日期" labelWidth="120" labelPosition="left" prop="chamferInspectionDate">
								<!-- <view class="mlauto">{{info.ebSkinInspectionDate}}</view> -->
								
								<u--input inputAlign="right" v-model="info.chamferInspectionDate" readonly  placeholder="年 /月/日">	</u--input>
								<template #right><u-icon name="calendar" size="22" @click="onDateTimeShow('chamferInspectionDate','date')"></u-icon></template>
							</u-form-item>
							<view class="no-border">
							<u-form-item label="巡查时间" labelWidth="120" labelPosition="left"  prop="chamferInspectionStartTime">
								<view style="display: flex; width: 100%;">
									<view class="start int-time" style="flex: 1;">
										<u--input inputAlign="left" v-model="info.chamferInspectionStartTime"  class="int" readonly  placeholder="--:--"  >
											<template #suffix>
												<u-icon name="clock" size="20" @click="onRangeTimeShow('chamferInspectionStartTime','chamferInspectionEndTime')"></u-icon>
											</template>
										</u--input>
									</view>
									<u-icon name="minus"  size="22"></u-icon>
									
									<view class="end int-time" style="flex: 1;">
										<u--input inputAlign="right" v-model="info.chamferInspectionEndTime"  class="int" readonly  placeholder="--:--"  >
											<template #suffix>
												<u-icon name="clock" size="20" @click="onRangeTimeShow('chamferInspectionStartTime','chamferInspectionEndTime')"></u-icon>
											</template>
										</u--input>
									</view>
									
								</view>
							</u-form-item>
							<u-form-item label="巡查人" labelWidth="120" labelPosition="left" prop="inspectorStaffId">
								<view class="mlauto">{{info.inspectorStaffName}}</view>
							</u-form-item>
							</view>
							
							<u-form-item label="现场确认人" labelWidth="120" labelPosition="left" required prop="chamferConfirmStaffId">
								<!-- <load-selector placeholder="请选择" :clearable="false" v-model="info.chamferConfirmStaffId" load_url="/api/jml/staff/list4FactoryRoleStaff"></load-selector> -->
								<zxz-uni-data-select filterable  placeholder="请选择" v-model="info.chamferConfirmStaffId" dataText="name" dataValue="id" :localdata="companyList"></zxz-uni-data-select>
							</u-form-item>
							
						</view>
					</div>
					<!-- <view class="total-content" >
						<u--text size="14" bold color="#A1A4A9" prefixIcon="checkmark-circle-fill" iconStyle="font-size: 14px;color:#A1A4A9; margin-right: 5upx;" text="10项"></u--text>
						<u--text size="14" bold color="#74D126" prefixIcon="checkmark-circle-fill" iconStyle="font-size: 14px;color:#74D126; margin-right: 5upx;" text="10项"></u--text>
						<u--text size="14" bold color="#E97056" prefixIcon="checkmark-circle-fill" iconStyle="font-size: 14px;color:#A1A4A9; margin-right: 5upx;" text="10项"></u--text>
					</view> -->
					<div class="card">
						<u-form-item label="每台设备点检表是否完成《检查完成情况》 " required prop="chamferEqInspectionDone">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.chamferEqInspectionDone" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'chamferEqInspectionDoneImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'chamferEqInspectionDoneImg')"
											:fileList="info.chamferEqInspectionDoneImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.chamferEqInspectionDoneSol" maxlength="500" v-model="info.chamferEqInspectionDoneSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="每台设备日保养表是否完成《检查完成情况》 " required prop="chamferDailyMaintenanceDone">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.chamferDailyMaintenanceDone" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'chamferDailyMaintenanceDoneImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'chamferDailyMaintenanceDoneImg')"
											:fileList="info.chamferDailyMaintenanceDoneImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.chamferDailyMaintenanceDoneSol" maxlength="500" v-model="info.chamferDailyMaintenanceDoneSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查空气压力是否达标《7Pa》" required prop="chamferAirPressureCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.chamferAirPressureCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'chamferAirPressureCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'chamferAirPressureCheckImg')"
											:fileList="info.chamferAirPressureCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.chamferAirPressureCheckSol" maxlength="500" v-model="info.chamferAirPressureCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查现场刀具是否完好 " required prop="chamferToolCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.chamferToolCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'chamferToolCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'chamferToolCheckImg')"
											:fileList="info.chamferToolCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.chamferToolCheckSol" maxlength="500" v-model="info.chamferToolCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查各个传感器位置是否正确" required prop="chamferSensorCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.chamferSensorCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'chamferSensorCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'chamferSensorCheckImg')"
											:fileList="info.chamferSensorCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.chamferSensorCheckSol" maxlength="500" v-model="info.chamferSensorCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查上料分板器定位杆是否松动" required prop="chamferLocatorCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.chamferLocatorCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'chamferLocatorCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'chamferLocatorCheckImg')"
											:fileList="info.chamferLocatorCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.chamferLocatorCheckSol" maxlength="500" v-model="info.chamferLocatorCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查传送带是否清洁 " required prop="chamferConveyorCleanliness">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.chamferConveyorCleanliness" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'chamferConveyorCleanlinessImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'chamferConveyorCleanlinessImg')"
											:fileList="info.chamferConveyorCleanlinessImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.chamferConveyorCleanlinessSol" maxlength="500" v-model="info.chamferConveyorCleanlinessSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查所有毛刷机是否开启" required prop="chamferBrushMachineCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.chamferBrushMachineCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'chamferBrushMachineCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'chamferBrushMachineCheckImg')"
											:fileList="info.chamferBrushMachineCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.chamferBrushMachineCheckSol" maxlength="500" v-model="info.chamferBrushMachineCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查上下料龙门吸盘是否完好" required prop="chamferLoaderCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.chamferLoaderCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'chamferLoaderCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'chamferLoaderCheckImg')"
											:fileList="info.chamferLoaderCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.chamferLoaderCheckSol" maxlength="500" v-model="info.chamferLoaderCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
					</div>
				</view >
				
                <!-- 打印线 -->
				<view  v-show="active_tabs==3">
					<div class="card pd10">
						<view class="tabHead">
							<u-form-item label="巡查日期" labelWidth="120" labelPosition="left" prop="printLineInspectionDate">
								<!-- <view class="mlauto">{{info.ebSkinInspectionDate}}</view> -->
								
								<u--input inputAlign="right" v-model="info.printLineInspectionDate" readonly  placeholder="年 /月/日">	</u--input>
								<template #right><u-icon name="calendar" size="22" @click="onDateTimeShow('printLineInspectionDate','date')"></u-icon></template>
							</u-form-item>
							<view class="no-border">
							<u-form-item label="巡查时间" labelWidth="120" labelPosition="left"  prop="printLineInspectionStartTime">
								<view style="display: flex; width: 100%;">
									<view class="start int-time" style="flex: 1;">
										<u--input inputAlign="left" v-model="info.printLineInspectionStartTime"  class="int" readonly  placeholder="--:--"  >
											<template #suffix>
												<u-icon name="clock" size="20" @click="onRangeTimeShow('printLineInspectionStartTime','printLineInspectionEndTime')"></u-icon>
											</template>
										</u--input>
									</view>
									<u-icon name="minus"  size="22"></u-icon>
									
									<view class="end int-time" style="flex: 1;">
										<u--input inputAlign="right" v-model="info.printLineInspectionEndTime"  class="int" readonly  placeholder="--:--"  >
											<template #suffix>
												<u-icon name="clock" size="20" @click="onRangeTimeShow('printLineInspectionStartTime','printLineInspectionEndTime')"></u-icon>
											</template>
										</u--input>
									</view>
									
								</view>
							</u-form-item>
							<u-form-item label="巡查人" labelWidth="120" labelPosition="left" prop="inspectorStaffId">
								<view class="mlauto">{{info.inspectorStaffName}}</view>
							</u-form-item>
							</view>
							
							<u-form-item label="现场确认人" labelWidth="120" labelPosition="left" required prop="printLineConfirmStaffId">
								<!-- <load-selector placeholder="请选择" :clearable="false" v-model="info.chamferConfirmStaffId" load_url="/api/jml/staff/list4FactoryRoleStaff"></load-selector> -->
								<zxz-uni-data-select filterable  placeholder="请选择" v-model="info.printLineConfirmStaffId" dataText="name" dataValue="id" :localdata="companyList"></zxz-uni-data-select>
							</u-form-item>
							
						</view>
					</div>
					<!-- <view class="total-content" >
						<u--text size="14" bold color="#A1A4A9" prefixIcon="checkmark-circle-fill" iconStyle="font-size: 14px;color:#A1A4A9; margin-right: 5upx;" text="10项"></u--text>
						<u--text size="14" bold color="#74D126" prefixIcon="checkmark-circle-fill" iconStyle="font-size: 14px;color:#74D126; margin-right: 5upx;" text="10项"></u--text>
						<u--text size="14" bold color="#E97056" prefixIcon="checkmark-circle-fill" iconStyle="font-size: 14px;color:#A1A4A9; margin-right: 5upx;" text="10项"></u--text>
					</view> -->
					<div class="card">
						<u-form-item label="打印线巡查空压机是否开启" required prop="printLineCompCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.printLineCompCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'printLineCompCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'printLineCompCheckImg')"
											:fileList="info.printLineCompCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.printLineCompCheckSol" maxlength="500" v-model="info.printLineCompCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="每台设备点检表是否完成《检查完成情况》 " required prop="printLineEqInspectDone">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.printLineEqInspectDone" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'printLineEqInspectDoneImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'printLineEqInspectDoneImg')"
											:fileList="info.printLineEqInspectDoneImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.printLineEqInspectDoneSol" maxlength="500" v-model="info.printLineEqInspectDoneSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>

                        <u-form-item label="每台设备日保养表是否完成《检查完成情况》" required prop="printLineDailyMaintDone">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.printLineDailyMaintDone" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'printLineDailyMaintDoneImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'printLineDailyMaintDoneImg')"
											:fileList="info.printLineDailyMaintDoneImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.printLineDailyMaintDoneSol" maxlength="500" v-model="info.printLineDailyMaintDoneSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查空气压力是否达标《7Pa》" required prop="printLineAirPressCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.printLineAirPressCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'printLineAirPressCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'printLineAirPressCheckImg')"
											:fileList="info.printLineAirPressCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.printLineAirPressCheckSol" maxlength="500" v-model="info.printLineAirPressCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查砂光机皮带传送带位置是否跑偏《标记处》 " required prop="printLineBeltPosCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.printLineBeltPosCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'printLineBeltPosCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'printLineBeltPosCheckImg')"
											:fileList="info.printLineBeltPosCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.printLineBeltPosCheckSol" maxlength="500" v-model="info.printLineBeltPosCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查辊涂机油泵是否漏油" required prop="printLineGlueLeakCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.printLineGlueLeakCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'printLineGlueLeakCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'printLineGlueLeakCheckImg')"
											:fileList="info.printLineGlueLeakCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.printLineGlueLeakCheckSol" maxlength="500" v-model="info.printLineGlueLeakCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查除尘废气是否开启" required prop="printLineDustCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.printLineDustCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'printLineDustCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'printLineDustCheckImg')"
											:fileList="info.printLineDustCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.printLineDustCheckSol" maxlength="500" v-model="info.printLineDustCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查所有涂布机平辊是否完好" required prop="printLineRollerCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.printLineRollerCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'printLineRollerCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'printLineRollerCheckImg')"
											:fileList="info.printLineRollerCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.printLineRollerCheckSol" maxlength="500" v-model="info.printLineRollerCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="打印房间温度是否达标" required prop="printLineTempCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.printLineTempCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'printLineTempCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'printLineTempCheckImg')"
											:fileList="info.printLineTempCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.printLineTempCheckSol" maxlength="500" v-model="info.printLineTempCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
						
						<u-form-item label="巡查上下料龙门吸盘是否完好" required prop="printLineHopperCheck">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.printLineHopperCheck" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'printLineHopperCheckImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'printLineHopperCheckImg')"
											:fileList="info.printLineHopperCheckImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.printLineHopperCheckSol" maxlength="500" v-model="info.printLineHopperCheckSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>

                        <u-form-item label="巡查传送带是否清洁" required prop="printLineConveyorClean">
							<view class="section">
								<view class="section-item">
									<u-radio-group v-model="info.printLineConveyorClean" activeColor="#849EB2" iconPlacement="left">
										<u-radio :customStyle="{marginRight: '16px'}"  label="完成" name="1"></u-radio>
										<u-radio  label="未完成" name="0"></u-radio>
									</u-radio-group>
								</view>
								<view class="section-item flex">
									<view>
										<u-upload
											:max-count="1"
											@after-read="afterRead($event,'printLineConveyorCleanImg')"
											:deletable="true"
											:sizeType="['compressed']"
											:before-delete="(file,detail)=>beforeDelete(file,detail)"
											@delete="beforeDelete($event,'printLineConveyorCleanImg')"
											:fileList="info.printLineConveyorCleanImg"
										>
										</u-upload>
									</view>
									<u--textarea :count="!!info.printLineConveyorCleanSol" maxlength="500" v-model="info.printLineConveyorCleanSol"  placeholder="请输入"  />
								</view>
							</view>
						</u-form-item>
					</div>
				</view >
			</u--form>
        </div>
		<u-datetime-picker
			:show="dateTimeShow"
			v-model="currentTime"
			confirmColor="#849EB2"
			closeOnClickOverlay
			@confirm="onDateTimeConfirm"
			@cancel="dateTimeShow = false"
			@close="dateTimeShow = false"
			:mode="dateMode"
		></u-datetime-picker>
		
		<hbxw-time-range-picker 
			v-model:visible="rangeTimeShow"
			@cancel="rangeTimeShow= false"
			isShowType
			:isShowSecond='false'
			:value="rangeTime" 
			delimiter="point"
			@sure="onRangeTimeConfirm"
		/>
		<u-modal :show="submitPop"  title="确认是否提交" >
			<view class="modal-content">
				<view class="flex item-text">
					<!-- <view class="left">巡检生产线</view> -->
					<view class="tag_list">
						<view v-for="(dic,index) in dict['inspection_prod_line']" :key="index" class="tag-item">
							<u-tag size="mini" :plain="!info.inspectionProdLine || (info.inspectionProdLine && !info.inspectionProdLine.includes(dic.dname))"  :text="dic.dname"></u-tag>
						</view>
					</view>
				</view>
				<template v-if="info.inspectionProdLine && info.inspectionProdLine.includes('EB线、肤感线')">
					<!-- EB线、肤感线 -->
					<view v-if="info.ebSkinInspectionDate" class="flex item-text">
						<view class="left">
							(EB线、肤感线)巡检日期/时间
						</view>
						{{info.ebSkinInspectionDate}} {{info.ebSkinInspectionStartTime}} - {{info.ebSkinInspectionEndTime}}
					</view>
					<view v-if="info.ebSkinConfirmStaffId" class="flex item-text">
						<view class="left">(EB线、肤感线)现场确认人</view>
						<view class="rigth">
							<zxz-uni-data-select disabled placeholder="请选择" v-model="info.ebSkinConfirmStaffId" dataText="name" dataValue="id" :localdata="companyList"></zxz-uni-data-select>
						</view>
					</view>
				</template>
				<template v-if="info.inspectionProdLine && info.inspectionProdLine.includes('压贴线')">
				<!-- 压贴线 -->
				<view v-if="info.pressInspectionDate" class="flex item-text">
					<view class="left">
						(压贴线)巡检日期/时间
					</view>
					{{info.pressInspectionDate}} {{info.pressInspectionStartTime}} - {{info.pressInspectionEndTime}}
				</view>
				<view v-if="info.pressConfirmStaffId" class="flex item-text">
					<view class="left">(压贴线)现场确认人</view>
					<view class="rigth">
						<zxz-uni-data-select disabled placeholder="请选择" v-model="info.pressConfirmStaffId" dataText="name" dataValue="id" :localdata="companyList"></zxz-uni-data-select>
					</view>
					
				</view>
				</template>
				<template v-if="info.inspectionProdLine && info.inspectionProdLine.includes('倒角线')">
				<!-- 倒角线 -->
				<view v-if="info.chamferInspectionDate" class="flex item-text">
					<view class="left">
						(倒角线)巡检日期/时间
					</view>
					{{info.chamferInspectionDate}} {{info.chamferInspectionStartTime}} - {{info.chamferInspectionEndTime}}
				</view>
				<view v-if="info.chamferConfirmStaffId" class="flex item-text">
					<view class="left">(倒角线)现场确认人</view>
					<view class="rigth">
						<zxz-uni-data-select disabled placeholder="请选择" v-model="info.chamferConfirmStaffId" dataText="name" dataValue="id" :localdata="companyList"></zxz-uni-data-select>
					</view>	
				</view>
				</template>
                <template v-if="info.inspectionProdLine && info.inspectionProdLine.includes('打印线')">
				<!-- 打印线 -->
				<view v-if="info.printLineInspectionDate" class="flex item-text">
					<view class="left">
						(打印线)巡检日期/时间
					</view>
					{{info.printLineInspectionDate}} {{info.printLineInspectionStartTime}} - {{info.printLineInspectionStartTime}}
				</view>
				<view v-if="info.printLineConfirmStaffId" class="flex item-text">
					<view class="left">(打印线)现场确认人</view>
					<view class="rigth">
						<zxz-uni-data-select disabled placeholder="请选择" v-model="info.printLineConfirmStaffId" dataText="name" dataValue="id" :localdata="companyList"></zxz-uni-data-select>
					</view>	
				</view>
				</template>
			</view>
			<template #confirmButton>
				<view class="modal_footer flex">
					<div  @click="submitPop = false" class="cancel_btn modal_footer_btn">取消</div>
					<div  @click="save()" class="draft_btn modal_footer_btn">保存</div>
                    <div  @click="save('待确认')" class="confirm_btn modal_footer_btn">提交</div>
				</view>
			</template>
		</u-modal>
		<footer-btn v-if="!info.id || info?.isEdit=='1'" :cancelBtnShow="false" :confirmBtnShow="false">
			<div v-if="active_tabs>0"  @click="tabChange({index:active_tabs-1})" class="prev_btn btn">上一页</div>
			<div v-if="active_tabs<dict['inspection_prod_line'].length-1" @click="tabChange({index:active_tabs+1})" class="next_btn btn">下一页</div>
			<div v-if="active_tabs==dict['inspection_prod_line'].length-1" @click="onSubmit" class="next_btn btn">提交</div>
		</footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import LoadSelector from '@/components/LoadSelector.vue';
import Selector from '@/components/Selector.vue';
import dayjs from "dayjs";
import {queryDetails,saveEdit}  from  '../../api/dailyInspection'
import {queryListByStaff}  from  '@/api/company'
import {useDateTime}  from  '../../vueUse/datetimeUse'
import {useRangeTime}  from  '../../vueUse/rangeTimeUse'
import { useRoute } from 'uni-mini-router';
import { forEach, isArray } from 'lodash';
import {upload,dictTree,asyncUpload}  from  '@/api/common'
import {dailyInspectionImgFild} from '../../constant/index.js'
import {getRecordData}  from  '@/api/common'
	const prop =  defineProps({
			id: String,
			isComponent: {
				type: Boolean,
				default: false,
			},
			deviceRepairId:{
				type: String,
				default: null,
			}
		})
	const routerQuery = prop;
	const appStore = useAppStore();
	const router = useRouter();
	const route = useRoute();
	const { userInfo,factoryMenuObj } = useUserStore();
    // 相关字典
    const dictStore = useDictStore()
    const companyList = ref([])
	const util = inject("util");
    const deleteFild = ['accName','createdStaffName','updateStaffName','salesLeadName','marketingName','areaName'];
    const requiredFild = [
        // {key:'name',message:'请填写机会名称',required: true,},
        // {key:'amount',message:'请填写金额',required: true,},
        // {key:'type',message:'请选择机会类型',required: true,},
    ]

    const save = (type) => {

        for(let index  in requiredFild){ 
            var item =  requiredFild[index]
            if(item.required && (!info.value[item.key] || info.value[item.key]=='')){
            	return uni.showToast({
            		title: item.message,
            		duration: 1500,
            		icon:'none'
            	});
            }
        };
        let postData = {...info.value}
		if(type)postData.inspectionStatus = type

		for(let item of dailyInspectionImgFild){ 
			if(info.value[item] && isArray(info.value[item])) {
			    postData[item] = info.value[item].map(i=>i.url).join(';')
			}
		}
        
        // for(var key in info.value){
        //     if(!deleteFild.includes(key))
        //         postData[key] = info.value[key];
        // }
        saveEdit(postData).then((res)=>{
            uni.showModal({
            	title: '提示',
            	content: info.value.id ?'修改成功':'保存成功',
            	showCancel:false,
            	success: function (confirmres) {
            		if (confirmres.confirm) {
            			if(prop.isComponent)emit('confirm', res)
            			else{
							// uni.$off('refresh-dailyInspection');
            				uni.$emit("refresh-dailyInspection", {refresh: true}); 
            				router.back();
            			}
            		} else if (confirmres.cancel) {
            			console.log('用户点击取消');
            		}
            	}
            });
        })
    }
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
			for(let item of dailyInspectionImgFild){ 
                // debugger
				if(isArray(info.value[item])) {
				    info.value[item] = info.value[item].map(i=>i.url).join(';')
				}
				if(res.data[item] && res.data[item]!=''){
				    info.value[item] = res.data[item].split(';').map(i=>{
						return{url:import.meta.env.VUE_APP_BASE_IMG + i}
					})
				}else{
				    info.value[item] = []
				}
			}
            
        }).catch((err) => {
        });
        
    }
    const info = ref({
		inspectorStaffName: userInfo.name,
		inspectionProdLine:''
    });
    const init = async() =>{
		await dictStore.getDictBatch(['inspection_prod_line','product_line']);
		companyList.value = (await getRecordData('/api/jml/staff/list4FactoryRoleStaff')).data
        if(routerQuery.id){
            getInfo()
        }
    }
	const {dateTimeShow,field,currentTime,onDateTimeShow,onDateTimeConfirm,dateMode} = useDateTime(info)
	const {rangeTime,rangeTimeShow,onRangeTimeShow,onRangeTimeConfirm}  = useRangeTime(info)
	
	const afterRead = async(event,type) =>{
		let files = [].concat(event.file)
		if(!info.value[type]) info.value[type] = [];
		let fileListLen =  info.value[type].length
		files.map((item) => {
			info.value[type].push({
				...item,
				status: 'uploading',
				message: '上传中'
			})
		})
		for (let i = 0; i < files.length; i++) {
			try {
				const {data} = await asyncUpload(files[i].url)
				let item = info.value[type][fileListLen];
				info.value[type].splice(fileListLen, 1, {
				  ...item,
				  status: 'success',
				  message: '',
				  url: data.url,
				});
				fileListLen++;
			} catch (e) {
				console.log(e)
				uni.showToast({
					title: "图片上传失败",
					icon: "none"
				})
			}
		}
	}
	
	const beforeDelete = ({file,index},type) =>{
		uni.showModal({
			title: '提示',
			content: '是否删除该附件？',
			success: function (res) {
				if (res.confirm) {
					info.value[type].splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
	
	const active_tabs = ref(0)
	const tabChange = ({index,...item})=>{
		active_tabs.value = index
	}
	
	const submitPop = ref(false)
	
	const onSubmit =  async() =>{
		let dataTree = await dictStore.getDictTree('inspection_prod_line')
		let prodLine = [];
		dataTree.forEach((i, index) => {
			let fieldTrue = i.children.some(j=>info.value[j.dval])
			if(fieldTrue) prodLine.push(i.dval)
		});
		info.value.inspectionProdLine = prodLine.join(';')
		submitPop.value = true
	}
    onMounted(() => {
        init()
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
	@import '../../style/common.scss';
	:deep(.tool){
	    .u-tabs__wrapper__nav__item{
	    		flex: 1;
	    }
	}
	.footer .footer_btn .btn.prev_btn{
		background: none;
		color: #849EB2;
	}
	.footer .footer_btn .btn.next_btn{
		background: #849EB2;
		color: #fff;
	}
	.modal_footer{
		display: flex;
		.modal_footer_btn{
			border: 1px solid rgba(137,158,175,1);
			flex: 1;
			margin: 0 8px;
			text-align:center;
			border-radius: 6px;
			padding: 6px 0px;
			&.cancel_btn{
				color: rgba(137,158,175,1);
			}
			&.confirm_btn{
				background-color: #4085CB;
				color: #fff;
			}
            &.draft_btn{
                background-color: rgba(137,158,175,1);
				color: #fff;
			}
		}
	}
	.modal-content{
		padding: 0 15px 30px 15px;
		color: #666;
		text-align: left;
		width: 100%;
		.item-text{
			display: flex;
			align-items: center;
			margin: 10px 0;
			.left{
				margin-right: 10px;
				width: 80px;
			}
			.rigth{
				flex: 1;
			}
		}
	}
	:deep(.tag_list){
		display: flex;
		.tag-item{
			margin-left: 5px;
		}
		.u-tag--primary{
			background-color: #93AABB;
			border-color: #93AABB;
		}
		.u-tag--primary--plain{
			border-color: #93AABB;
			.u-tag__text--primary--plain{
				color: #93AABB;
			}
		}
	}
</style>