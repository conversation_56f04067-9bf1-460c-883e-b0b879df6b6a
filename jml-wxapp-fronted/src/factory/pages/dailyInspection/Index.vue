<template>
    <view class="page">
        <view class="head">
            <view class="search">
                <u-search
                    v-model="searchQuery.multiFiled"
                    placeholder="记录编号"
                    shape="square"
                    @change="util.debounce(onSearch)"
                	@custom="util.debounce(onSearch)"
                >
                </u-search>
            </view>
        </view>
        <view class="card_list">
            <view class="card_tool">
                <view class="tool">
                    <view @click="reset" v-if="showReset" style="position: absolute; left: 0;  display: flex; align-items: center;">
                        <u-icon size="16" name="close-circle-fill" color="#849EB2" />
                        <span class="" style=" margin-left: 5px; font-weight: normal;">清除筛选</span>
                    </view>

					<!-- SU显示员工筛选选项员工获取调用： -->
					<toolsPicker
						v-if="hasFactoryPermission('dailyInspection','SU')"
						v-model="searchQuery.createdStaffId"
						 title="员工"
						 dataValue="staffId"
						 dataText="staffName"
						load_url="/api/jml/staff/list4FactorySubStaffWithoutSelf"
						@confirm="util.debounce(onSearch)"
					></toolsPicker>
					
                    <view class="item">
                    <uni-datetime-picker ref="calendarShow" color="#849EB2"
                    	type="daterange"
                    	:end="maxDate"
                    	rangeSeparator="至"
                    	v-model="datetimerange"
                    	@change=onCalendarConfirm
                    >
                    	<u-icon size="6" labelSize="12" labelPos="left" :label="searchQuery.startDate ? `${searchQuery.startDate} - ${searchQuery.endDate}` : '日期'" name="/static/icon/sel_icon.png" />
                    </uni-datetime-picker>	
                    </view>
                </view>
            </view>
            <view class="swiper-item" style="height: calc(100% - 30px )">
				<scroll-view
					scroll-y enable-flex refresher-enabled	
					@scrolltolower="getDataPage()" 
					@refresherrefresh="getFresh"
					:refresher-triggered="refresherTriggered"
					class="scrollHeight"
					style="height: 100%;">
            		<uni-swipe-action>
            		<div v-for="(item,index) in data.list" :key="index" class="van-swipe-cell">
            			<uni-swipe-action-item >
							<view class="card"  @click="handDetail(item)">
								<view class="card_head">
									<view class="card_head_tit">
										{{ item.name }}
									</view>
                                    <div v-if="item.inspectionStatus" class="head_status">
										<u-tag size="mini" :bgColor="dailyInspection[item.inspectionStatus].bgColor" :borderColor="dailyInspection[item.inspectionStatus].bgColor" :text="item.inspectionStatus"></u-tag>
										<!-- <image  class="van-icon__image" width="40"  mode="widthFix" src="/static/icon/mileage_incomplete.png" /> -->
									</div>
								</view>
								<u-divider></u-divider>
								<view class="card_main">
									<!-- <view class="item">
										<view class="left">影响程度</view>
										<view class="rigth">{{item.impactLevel}}</view>
									</view> -->
									<view v-if="item.checkAll" class="total-content" >
										<u--text size="14" bold color="#A1A4A9" :prefixIcon="`${baseURL}/assets/fac/d-null.png`" iconStyle="font-size: 14px;color:#A1A4A9; margin-right: 5upx;" 
										:text="item.checkAll.totalNulls+'项'"></u--text>
										<u--text size="14" bold color="#74D126"  :prefixIcon="`${baseURL}/assets/fac/d-checked.png`" iconStyle="font-size: 14px;color:#A1A4A9; margin-right: 5upx;" 
										:text="item.checkAll.totalChecked+'项'"></u--text>
										<u--text size="14" bold color="#E97056" :prefixIcon="`${baseURL}/assets/fac/d-unchecked.png`" iconStyle="font-size: 14px;color:#74D126; margin-right: 5upx;"
										:text="item.checkAll.totalUnchecked+'项'"></u--text>
									</view>
									<view class="item">
										<!-- <view class="left"></view> -->
										<div  class="head_status">
											<view v-for="(dic,index) in dict['inspection_prod_line']" :key="index" class="tag-item">
												<u-tag size="mini" :plain="!item.inspectionProdLine || (item.inspectionProdLine && !item.inspectionProdLine.includes(dic.dname))" :text="dic.dname"></u-tag>
											</view>
										</div>
									</view>
									<view class="item">
										<view class="left">巡查人</view>
										<view class="rigth">{{item.inspectorStaffName}}</view>
									</view>
									<template v-if="item.inspectionProdLine && item.inspectionProdLine.includes('EB线、肤感线')">
										<view class="item">
											<view class="left">EB线、肤感线巡查时间</view>
											<view class="rigth">{{item.ebSkinInspectionDate}} {{item.ebSkinInspectionStartTime}} - {{item.ebSkinInspectionEndTime}}</view>
										</view>
                                        <view class="item">
											<view class="left">EB线、肤感线确认人</view>
											<view class="rigth">
                                                {{item.ebSkinConfirmStaffName}}
                                                <view class="mlauto">
                                                    <u--text v-if="item.ebSkinConfirmStatus=='1'" text="已确认" 
                                                        size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
                                                        iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
                                                    <u--text v-if="item.ebSkinConfirmStatus=='0'" text="未确认"
                                                        size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
                                                        iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
                                                </view>
                                            </view>
										</view>
									</template>
									<template v-if="item.inspectionProdLine && item.inspectionProdLine.includes('压贴线')">
										<view class="item">
											<view class="left">压贴线线巡查时间</view>
											<view class="rigth">{{item.pressInspectionDate}} {{item.pressInspectionStartTime}} - {{item.pressInspectionStartTime}}</view>
										</view>
                                        <view class="item">
											<view class="left">压贴线确认人</view>
											<view class="rigth">
                                                {{item.pressConfirmStaffName}}
                                                <view class="mlauto">
                                                    <u--text v-if="item.pressConfirmStatus=='1'" text="已确认" 
                                                        size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
                                                        iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
                                                    <u--text v-if="item.pressConfirmStatus=='0'" text="未确认"
                                                        size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
                                                        iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
                                                </view>
                                            </view>
										</view>
									</template>
									<template v-if="item.inspectionProdLine && item.inspectionProdLine.includes('倒角线')">
                                        <view class="item">
                                            <view class="left">倒角线巡查时间</view>
                                            <view class="rigth">{{item.chamferInspectionDate}} {{item.chamferInspectionStartTime}} - {{item.chamferInspectionEndTime}}</view>
                                        </view>
                                        <view class="item">
											<view class="left">倒角线确认人</view>
											<view class="rigth">
                                                {{item.chamferConfirmStaffName}}
                                                <view class="mlauto">
                                                    <u--text v-if="item.chamferConfirmStatus=='1'" text="已确认" 
                                                        size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
                                                        iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
                                                    <u--text v-if="item.chamferConfirmStatus=='0'" text="未确认"
                                                        size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
                                                        iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
                                                </view>
                                            </view>
										</view>
									</template>
                                    <template v-if="item.inspectionProdLine && item.inspectionProdLine.includes('打印线')">
                                        <view class="item">
                                            <view class="left">打印线巡查时间</view>
                                            <view class="rigth">{{item.printLineInspectionDate}} {{item.printLineInspectionStartTime}} - {{item.printLineInspectionEndTime}}</view>
                                        </view>
                                        <view class="item">
											<view class="left">打印线确认人</view>
											<view class="rigth">
                                                {{item.printLineConfirmStaffName}}
                                                <view class="mlauto">
                                                    <u--text v-if="item.printLineConfirmStatus=='1'" text="已确认" 
                                                        size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
                                                        iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
                                                    <u--text v-if="item.printLineConfirmStatus=='0'" text="未确认"
                                                        size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
                                                        iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
                                                </view>
                                            </view>
										</view>
									</template>
									<view class="card_footer">
										<view class="item">
											<view class="left">创建时间</view>
											<view class="rigth">{{item.createdTime}} <span class="link">查看 <u-icon name="arrow-right" size="12" /></span> </view>
										</view>
										<view class="item">
											<view class="left">创建员工</view>
											<view class="rigth">{{item.createdStaffName}}</view>
										</view>
									</view>
								</view>
								<u-divider></u-divider>
							</view>
						</uni-swipe-action-item>
					</div>
					</uni-swipe-action>
					<view class="empty_box" v-if="!data.loading && (!data.list ||  data.list.length<=0)">
						<u-empty v-if="!data.list || data.list.length==0" text="未找到信息" :icon="`${config.static}images/empty/data.png`" ></u-empty>
					</view>
               </scroll-view>
           </view>
        </view>

		<!-- <u-calendar v-model:show="calendarShow" color="#849EB2"
			monthNum = "13"
			showLunar
			:minDate="minDate"
			:maxDate="maxDate"
			confirm-disabled-text="请选择结束时间" mode="range"  round="10" 
			@confirm="onCalendarConfirm"
			closeOnClickOverlay
			@close="calendarShow=false" 
		/> -->
		
        <floating-window>
           <view
                v-if="hasFactoryPermission('dailyInspection','C')"
				@click="router.push({name:'dailyInspectionEdit'})"
                class="item add_btn"
            >
               <u-icon size="20"  name="/static/icon/add.png" />
            </view>
        </floating-window>
    </view>
</template>
<script setup>
	import FloatingWindow from '@/components/FloatingWindow.vue';
	import toolsPicker from '../../components/toolsPicker.vue';
	import ProgressStatus from '@/components/ProgressStatus.vue';
	import {queryPageData}  from  '../../api/dailyInspection'
	import {queryUserList}  from  '@/api/common'
    import {dailyInspection} from '../../constant/index.js'
	import dayjs from "dayjs";
	import {useDateTime}  from  '../../vueUse/datetimeUse'
    const router = useRouter();
	const util = inject("util");
    const {data,finishedText} = useDataList()
    const dictStore = useDictStore()
    const onSearch = () => {
        initEmpty()
    }
    const routerQuery =  defineProps({});
	var pages = getCurrentPages();
	//获取当前页面
	var currentPage = pages[pages.length - 1];
	const urlLink = ref(currentPage.route)
	console.log(currentPage, '打印当前页路由')
	
    const searchQuery = reactive({
        multiFiled:'',
        startDate: routerQuery.startDate || '',
        endDate: routerQuery.endDate || '',
        createdStaffId:'',
        pageNo:data.pageNo,
        pageSize:data.pageSize,
		prodLineOrDept:'',
		repairerStaffId:'',
    })
	console.log(searchQuery)
	// 下拉刷新
	const refresherTriggered = ref(false);
	const getFresh=()=> {
		console.log('refresh');
		refresherTriggered.value = 'restore'
		initEmpty()
	}
    const getDataPage = (pageNo) =>{
		// debugger
    	if (data.finished || data.loading)return;
        searchQuery.pageNo = pageNo || data.pageNo;
		data.loading = true;
		if(searchQuery.pageNo<=1) data.initEmpty()
        queryPageData(searchQuery).then((res) => {
			if(refresherTriggered.value !== false) refresherTriggered.value = false
            data.list = [...data.list, ...res.data.list]
			data.totalRow = res.data.totalRow
			data.pageNo = data.pageNo + 1 ;
			data.loading = false;
			data.finished = res.data.lastPage;
        }).catch((err) => {
            data.finished = true;
        });
    }

    const deleteRecord = (id)=>{
		uni.showModal({
		    title: '提示',
		    content: '是否删除该记录？',
			success: function (unires) {
				if (unires.confirm) {
					deleteReport(id).then((res) => {
					    uni.showModal({
					        title:'提示',
					        showCancel:false,
					        content: '操作成功',
					    }).then((aa) => {
					        initEmpty()
					    })
					}).catch((err) => {
					});
				}
			}
		})
    }

    const init = async() =>{
		data.finished && (data.finished = false)
        getDataPage()
    }
    const initEmpty =() =>{
        data.initEmpty()
		getDataPage(1)
    }
	onShow(()=>{
		
	})
    onMounted(async() => {
		await dictStore.getDictBatch(['inspection_prod_line']);
		init()
		uni.$once("refresh-dailyInspection", (data) => {
			initEmpty()
		})
    })
    //点击明细
    const handDetail =(item)=>{
        router.push({name: 'dailyInspectionInfo',params: {id: item.id}})
    }

    /**
     *  搜索相关 开始
     */
    const calendarShow = ref(false)
    const datetimerange = ref([])
    const {minDate,maxDate} = useDateTime()
    const onCalendarConfirm = (values) => {
    	const start= values[0],end = values[values.length - 1];
        calendarShow.value = false;
    	searchQuery.startDate = start;
    	searchQuery.endDate = end;
        // searchQuery.startDate = `${dayjs(start).format('YYYY-MM-DD')}`;
        // searchQuery.endDate = `${dayjs(end).format('YYYY-MM-DD')}`;
        initEmpty()
    };
    
    // 是否显示清除图标
    const showReset = computed(()=>{
			return (searchQuery.startDate ||
			searchQuery.endDate  ||
			searchQuery.createdStaffId ||
			searchQuery.prodLineOrDept ||
			searchQuery.repairerStaffId 
		)
    });
    //清空搜索条件
    const reset = () =>{
        searchQuery.startDate = ''
        searchQuery.endDate = ''
        searchQuery.createdStaffId= ''
		searchQuery.prodLineOrDept= ''
		searchQuery.repairerStaffId= ''
        initEmpty()
    }
	onUnload(()=>{
		console.log('onUnload')
		uni.$off('refresh-dailyInspection');
	})
</script>
<style lang="scss" scoped>
    @import '@/styles/list.scss';
    .time{
		margin-left: auto; color: #999;
	}
    .page{
		.search{
			margin-left: 10upx;
		}
		.card_list .card .item .left{
			width: 150px;
		}
        .addInfo{
			padding: 10px;
			position: relative;
			border-radius: var(--card-border-radius);
			line-height: 1.4;
			width: 100%;
			.u-icon{
				padding: 10px 15px;
			}
			.card{
				background: none;
				padding: var(--card-padding);
				position: relative;
				border-radius: var(--card-border-radius);
				line-height: 1.4;
				.card_head{
					display: flex;
					align-items: center;
					justify-content: center;
					// border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
					padding: 15px 0 10px 0;
					&_tit{
						display: flex;
						align-items: center;
						font-size: 18px;
						font-weight: 600;
						color: #333333;
						span{
							font-weight: 700;
							font-size: 16px;
							color: #000000;
							margin-right: 5px;
						}
					}
				}
			}
			
		}

	}
	:deep(.head_status){
		display: flex;
		.tag-item{
			margin-right: 5px;
		}
		.u-tag--primary{
			background-color: #93AABB;
			border-color: #93AABB;
		}
		.u-tag--primary--plain{
			border-color: #93AABB;
			.u-tag__text--primary--plain{
				color: #93AABB;
			}
		}
	}
	.total-content{
		display: flex;
	}
    .mlauto{
        margin-left: auto;
    }
</style>