<template>
    <div class="page pd100">

		
        <div class="card_info">
            <div class="card">
				<div class="card_head">
					<div class="card_head_tit2" style="margin-top: 0;">
                        {{ info.name }}
                    </div>
                    <div v-if="info.inspectionStatus" class="head_status">
                        <u-tag size="mini" :bgColor="dailyInspection[info.inspectionStatus].bgColor" :borderColor="dailyInspection[info.inspectionStatus].bgColor" :text="info.inspectionStatus"></u-tag>
                    </div>
                </div>
				
            </div>
        </div>
		
		<u-sticky bgColor="#fff">  
			<div class="tool">
			    <u-tabs :current="active_tabs"	@change="tabChange" :list="dict['inspection_prod_line']" lineWidth="80" lineColor="#849EB2" keyName="dname"></u-tabs>
			</div>
		</u-sticky>
		
        <div class="card_info">
			<!-- EB线、肤感线 -->
            <div class="card"  v-show="active_tabs==0">
                <div class="card_head">
                    <div class="card_head_tit2" style="margin-top: 0;">
                       巡检信息
                    </div>
					<view v-if="info.checkEbSkin" class="total-content" >
						<u--text size="14" bold color="#A1A4A9" :prefixIcon="`${baseURL}/assets/fac/d-null.png`" iconStyle="font-size: 14px;color:#A1A4A9; margin-right: 5upx;" 
						:text="info.checkEbSkin.totalNulls+'项'"></u--text>
						<u--text size="14" bold color="#74D126"  :prefixIcon="`${baseURL}/assets/fac/d-checked.png`" iconStyle="font-size: 14px;color:#A1A4A9; margin-right: 5upx;" 
						:text="info.checkEbSkin.totalChecked+'项'"></u--text>
						<u--text size="14" bold color="#E97056" :prefixIcon="`${baseURL}/assets/fac/d-unchecked.png`" iconStyle="font-size: 14px;color:#74D126; margin-right: 5upx;"
						:text="info.checkEbSkin.totalUnchecked+'项'"></u--text>
					</view>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
					<div class="item">
					    <div class="left">巡查日期</div><div class="rigth">{{info.ebSkinInspectionDate}}</div>
					</div>
					<div class="item">
					    <div class="left">巡查时间</div><div class="rigth">{{info.ebSkinInspectionStartTime}} - {{info.ebSkinInspectionEndTime}}</div>
					</div>
					<div class="item">
					    <div class="left">巡查人</div><div class="rigth">{{info.inspectorStaffName}}</div>
					</div>
					<div class="item">
						<div class="left">现场确认人</div>
                        <div class="rigth">
                            {{info.ebSkinInspectorStaffName}}
                            <view class="mlauto">
                                <u--text v-if="info.ebSkinConfirmStatus=='1'" text="已确认" 
                                    size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
                                    iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
                                <u--text v-if="info.ebSkinConfirmStatus=='0'" text="未确认"
                                    size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
                                    iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
                            </view>
                        </div>
					</div>
                    <div class="item" v-if="info.ebSkinConfirmTime">
						<div class="left">确认时间</div><div class="rigth">{{info.ebSkinConfirmTime}}</div>
					</div>
                </div>
				<div class="card_head">
				    <div class="card_head_tit2" style="margin-top: 0;">
				       故障基本信息
				    </div>
				</div>
				<view class="checkList">
					<view class="checkList-item">
						<view class="tit flex">
							检查空压机是否开启
							<view class="mlauto">
								<u--text v-if="info.ebSkinCompCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.ebSkinCompCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;" ></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.ebSkinCompCheckImg"  @click="showImagePreview([imgBase+info.ebSkinCompCheckImg])"  mode="widthFix"  :src="imgBase+info.ebSkinCompCheckImg" />
								</view>
								<view>{{info.ebSkinCompCheckSol}}</view>
							</view>
						</view>
					</view>
					<view class="checkList-item">
						<view class="tit flex">
							每台设备点检表是否完成《检查完成情况》 
							<view class="mlauto">
								<u--text v-if="info.ebSkinEqInspectDone=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.ebSkinEqInspectDone=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;" ></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.ebSkinEqInspectDoneImg"  @click="showImagePreview([imgBase+info.ebSkinEqInspectDoneImg])"  mode="widthFix"  :src="imgBase+info.ebSkinEqInspectDoneImg" />
								</view>
								<view>{{info.ebSkinEqInspectDoneSol}}</view>
							</view>
						</view>
					</view>
					
					<view class="checkList-item">
						<view class="tit flex">
							每台设备日保养表是否完成《检查完成情况》 
							<view class="mlauto">
								<u--text v-if="info.ebSkinDailyMaintDone=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.ebSkinDailyMaintDone=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;" ></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.ebSkinDailyMaintDoneImg"  @click="showImagePreview([imgBase+info.ebSkinDailyMaintDoneImg])"  mode="widthFix"  :src="imgBase+info.ebSkinDailyMaintDoneImg" />
								</view>
								<view>{{info.ebSkinDailyMaintDoneSol}}</view>
							</view>
						</view>
					</view>
					
					<view class="checkList-item">
						<view class="tit flex">
							巡查空气压力是否达标《0.7MPa》
							<view class="mlauto">
								<u--text v-if="info.ebSkinAirPressCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.ebSkinAirPressCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.ebSkinAirPressCheckImg"  @click="showImagePreview([imgBase+info.ebSkinAirPressCheckImg])"  mode="widthFix"  :src="imgBase+info.ebSkinAirPressCheckImg" />
								</view>
								<view>{{info.ebSkinAirPressCheckSol}}</view>
							</view>
						</view>
					</view>

					<view class="checkList-item">
						<view class="tit flex">
							巡查皮带位置是否跑偏《标记处》
							<view class="mlauto">
								<u--text v-if="info.ebSkinBeltPosCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.ebSkinBeltPosCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.ebSkinBeltPosCheckImg"  @click="showImagePreview([imgBase+info.ebSkinBeltPosCheckImg])"  mode="widthFix"  :src="imgBase+info.ebSkinBeltPosCheckImg" />
								</view>
								<view>{{info.ebSkinBeltPosCheckSol}}</view>
							</view>
						</view>
					</view>

					<view class="checkList-item">
						<view class="tit flex">
							巡查覆膜机刀切刀是否完好
							<view class="mlauto">
								<u--text v-if="info.ebSkinBladeCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.ebSkinBladeCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.ebSkinBladeCheckImg"  @click="showImagePreview([imgBase+info.ebSkinBladeCheckImg])"  mode="widthFix"  :src="imgBase+info.ebSkinBladeCheckImg" />
								</view>
								<view>{{info.ebSkinBladeCheckSol}}</view>
							</view>
						</view>
					</view>

					<view class="checkList-item">
						<view class="tit flex">
							巡查涂胶机侧面是否漏胶
							<view class="mlauto">
								<u--text v-if="info.ebSkinGlueLeakCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.ebSkinGlueLeakCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.ebSkinGlueLeakCheckImg"  @click="showImagePreview([imgBase+info.ebSkinGlueLeakCheckImg])"  mode="widthFix"  :src="imgBase+info.ebSkinGlueLeakCheckImg" />
								</view>
								<view>{{info.ebSkinGlueLeakCheckSol}}</view>
							</view>
						</view>
					</view>

					<view class="checkList-item">
						<view class="tit flex">
							巡查所有涂布机平辊是否完好
							<view class="mlauto">
								<u--text v-if="info.ebSkinCoaterCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.ebSkinCoaterCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.ebSkinCoaterCheckImg"  @click="showImagePreview([imgBase+info.ebSkinCoaterCheckImg])"  mode="widthFix"  :src="imgBase+info.ebSkinCoaterCheckImg" />
								</view>
								<view>{{info.ebSkinCoaterCheckSol}}</view>
							</view>
						</view>
					</view>

					<view class="checkList-item">
						<view class="tit flex">
							巡查上下料龙门吸盘是否完好
							<view class="mlauto">
								<u--text v-if="info.ebSkinLoaderCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.ebSkinLoaderCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.ebSkinLoaderCheckImg"  @click="showImagePreview([imgBase+info.ebSkinLoaderCheckImg])"  mode="widthFix"  :src="imgBase+info.ebSkinLoaderCheckImg" />
								</view>
								<view>{{info.ebSkinLoaderCheckSol}}</view>
							</view>
						</view>
					</view>

					<view class="checkList-item">
						<view class="tit flex">
							三灯固化电控箱空调是否运行
							<view class="mlauto">
								<u--text v-if="info.ebSkinCuringBoxCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.ebSkinCuringBoxCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.ebSkinCuringBoxCheckImg"  @click="showImagePreview([imgBase+info.ebSkinCuringBoxCheckImg])"  mode="widthFix"  :src="imgBase+info.ebSkinCuringBoxCheckImg" />
								</view>
								<view>{{info.ebSkinCuringBoxCheckSol}}</view>
							</view>
						</view>
					</view>

					<view class="checkList-item">
						<view class="tit flex">
							巡查传送带是否清洁
							<view class="mlauto">
								<u--text v-if="info.ebSkinConvCleanliness=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.ebSkinConvCleanliness=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.ebSkinConvCleanlinessImg"  @click="showImagePreview([imgBase+info.ebSkinConvCleanlinessImg])"  mode="widthFix"  :src="imgBase+info.ebSkinConvCleanlinessImg" />
								</view>
								<view>{{info.ebSkinConvCleanlinessSol}}</view>
							</view>
						</view>
					</view>

				</view>
			</div>
			
			<!-- 压贴线 -->
			<div class="card"  v-show="active_tabs==1">
			    <div class="card_head">
			        <div class="card_head_tit2" style="margin-top: 0;">
			           巡检信息
			        </div>
					<view v-if="info.checkPress" class="total-content" >
						<u--text size="14" bold color="#A1A4A9" :prefixIcon="`${baseURL}/assets/fac/d-null.png`" iconStyle="font-size: 14px;color:#A1A4A9; margin-right: 5upx;" 
						:text="info.checkPress.totalNulls+'项'"></u--text>
						<u--text size="14" bold color="#74D126"  :prefixIcon="`${baseURL}/assets/fac/d-checked.png`" iconStyle="font-size: 14px;color:#A1A4A9; margin-right: 5upx;" 
						:text="info.checkPress.totalChecked+'项'"></u--text>
						<u--text size="14" bold color="#E97056" :prefixIcon="`${baseURL}/assets/fac/d-unchecked.png`" iconStyle="font-size: 14px;color:#74D126; margin-right: 5upx;"
						:text="info.checkPress.totalUnchecked+'项'"></u--text>
					</view>
			    </div>
			    <u-divider></u-divider>
			    <div class="card_main">
					<div class="item">
					    <div class="left">巡查日期</div><div class="rigth">{{info.pressInspectionDate}}</div>
					</div>
					<div class="item">
					    <div class="left">巡查时间</div><div class="rigth">{{info.pressInspectionStartTime}} - {{info.pressInspectionStartTime}}</div>
					</div>
					<div class="item">
					    <div class="left">巡查人</div><div class="rigth">{{info.inspectorStaffName}}</div>
					</div>
					<div class="item">
						<div class="left">现场确认人</div>
                        <div class="rigth">
                            {{info.pressConfirmStaffName}}
                            <view class="mlauto">
                                <u--text v-if="info.pressConfirmStatus=='1'" text="已确认" 
                                    size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
                                    iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
                                <u--text v-if="info.pressConfirmStatus=='0'" text="未确认"
                                    size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
                                    iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
                            </view>
                        </div>
					</div>
                    <div class="item" v-if="info.pressConfirmTime">
						<div class="left">确认时间</div><div class="rigth">{{info.pressConfirmTime}}</div>
					</div>
			    </div>
				<div class="card_head">
				    <div class="card_head_tit2" style="margin-top: 0;">
				       故障基本信息
				    </div>
				</div>
				<view class="checkList">
					<view class="checkList-item">
						<view class="tit flex">
							每台设备点检表是否完成《检查完成情况》 
							<view class="mlauto">
								<u--text v-if="info.pressEqInspectionDone=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.pressEqInspectionDone=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;" ></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.pressEqInspectionDoneImg"  @click="showImagePreview([imgBase+info.pressEqInspectionDoneImg])"  mode="widthFix"  :src="imgBase+info.pressEqInspectionDoneImg" />
								</view>
                                <view>{{info.pressEqInspectionDoneSol}}</view>
							</view>
						</view>
					</view>
					
					<view class="checkList-item">
						<view class="tit flex">
							每台设备日保养表是否完成《检查完成情况》 
							<view class="mlauto">
								<u--text v-if="info.pressDailyMaintenanceDone=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.pressDailyMaintenanceDone=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;" ></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.pressDailyMaintenanceDoneImg"  @click="showImagePreview([imgBase+info.pressDailyMaintenanceDoneImg])"  mode="widthFix"  :src="imgBase+info.pressDailyMaintenanceDoneImg" />
								</view>
								<view>{{info.pressDailyMaintenanceDoneSol}}</view>
							</view>
						</view>
					</view>
					
					<view class="checkList-item">
						<view class="tit flex">
							巡查空气压力是否达标《0.7MPa》
							<view class="mlauto">
								<u--text v-if="info.pressAirPressureCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.pressAirPressureCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.pressAirPressureCheckImg"  @click="showImagePreview([imgBase+info.pressAirPressureCheckImg])"  mode="widthFix"  :src="imgBase+info.pressAirPressureCheckImg" />
								</view>
								<view>{{info.pressAirPressureCheckSol}}</view>
							</view>
						</view>
					</view>
			
					<view class="checkList-item">
						<view class="tit flex">
							巡查铺纸房湿度是否在范围《65%》
							<view class="mlauto">
								<u--text v-if="info.pressToolCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.pressToolCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.pressToolCheckImg"  @click="showImagePreview([imgBase+info.pressToolCheckImg])"  mode="widthFix"  :src="imgBase+info.pressToolCheckImg" />
								</view>
								<view>{{info.pressToolCheckSol}}</view>
							</view>
						</view>
					</view>
			
					<view class="checkList-item">
						<view class="tit flex">
							巡查液压站是否漏油
							<view class="mlauto">
								<u--text v-if="info.pressSensorCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.pressSensorCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.pressSensorCheckImg"  @click="showImagePreview([imgBase+info.pressSensorCheckImg])"  mode="widthFix"  :src="imgBase+info.pressSensorCheckImg" />
								</view>
								<view>{{info.pressSensorCheckSol}}</view>
							</view>
						</view>
					</view>
			
					<view class="checkList-item">
						<view class="tit flex">
							巡查下料电箱房间空调是否打开
							<view class="mlauto">
								<u--text v-if="info.pressLocatorCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.pressLocatorCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.pressLocatorCheckImg"  @click="showImagePreview([imgBase+info.pressLocatorCheckImg])"  mode="widthFix"  :src="imgBase+info.pressLocatorCheckImg" />
								</view>
								<view>{{info.pressLocatorCheckSol}}</view>
							</view>
						</view>
					</view>
			
					<view class="checkList-item">
						<view class="tit flex">
							巡查压机油缸是否漏油《看接油盘》
							<view class="mlauto">
								<u--text v-if="info.pressConveyorCleanliness=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.pressConveyorCleanliness=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.pressConveyorCleanlinessImg"  @click="showImagePreview([imgBase+info.pressConveyorCleanlinessImg])"  mode="widthFix"  :src="imgBase+info.pressConveyorCleanlinessImg" />
								</view>
								<view>{{info.pressConveyorCleanlinessSol}}</view>
							</view>
						</view>
					</view>
			
					<view class="checkList-item">
						<view class="tit flex">
							巡查所有毛刷机是否开启
							<view class="mlauto">
								<u--text v-if="info.pressBrushMachineCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.pressBrushMachineCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.pressBrushMachineCheckImg"  @click="showImagePreview([imgBase+info.pressBrushMachineCheckImg])"  mode="widthFix"  :src="imgBase+info.pressBrushMachineCheckImg" />
								</view>
								<view>{{info.pressBrushMachineCheckSol}}</view>
							</view>
						</view>
					</view>
			
					<view class="checkList-item">
						<view class="tit flex">
							巡查上下料龙门吸盘是否完好
							<view class="mlauto">
								<u--text v-if="info.pressBrushLoaderCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.pressBrushLoaderCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.pressBrushLoaderCheckImg"  @click="showImagePreview([imgBase+info.pressBrushLoaderCheckImg])"  mode="widthFix"  :src="imgBase+info.pressBrushLoaderCheckImg" />
								</view>
								<view>{{info.pressBrushLoaderCheckSol}}</view>
							</view>
						</view>
					</view>
			
				</view>
			</div>
			
			<!-- 倒角线 -->
			<div class="card"  v-show="active_tabs==2">
			    <div class="card_head">
			        <div class="card_head_tit2" style="margin-top: 0;">
			           巡检信息
			        </div>
					<view v-if="info.checkChamfer" class="total-content" >
						<u--text size="14" bold color="#A1A4A9" :prefixIcon="`${baseURL}/assets/fac/d-null.png`" iconStyle="font-size: 14px;color:#A1A4A9; margin-right: 5upx;" 
						:text="info.checkChamfer.totalNulls+'项'"></u--text>
						<u--text size="14" bold color="#74D126"  :prefixIcon="`${baseURL}/assets/fac/d-checked.png`" iconStyle="font-size: 14px;color:#A1A4A9; margin-right: 5upx;" 
						:text="info.checkChamfer.totalChecked+'项'"></u--text>
						<u--text size="14" bold color="#E97056" :prefixIcon="`${baseURL}/assets/fac/d-unchecked.png`" iconStyle="font-size: 14px;color:#74D126; margin-right: 5upx;"
						:text="info.checkChamfer.totalUnchecked+'项'"></u--text>
					</view>
			    </div>
			    <u-divider></u-divider>
			    <div class="card_main">
					<div class="item">
					    <div class="left">巡查日期</div><div class="rigth">{{info.chamferInspectionDate}}</div>
					</div>
					<div class="item">
					    <div class="left">巡查时间</div><div class="rigth">{{info.chamferInspectionStartTime}} - {{info.chamferInspectionEndTime}}</div>
					</div>
					<div class="item">
					    <div class="left">巡查人</div><div class="rigth">{{info.inspectorStaffName}}</div>
					</div>
					<div class="item">
						<div class="left">现场确认人</div>
                        <div class="rigth">
                            {{info.chamferConfirmStaffName}}
                            <view class="mlauto">
                                <u--text v-if="info.chamferConfirmStatus=='1'" text="已确认" 
                                    size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
                                    iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
                                <u--text v-if="info.chamferConfirmStatus=='0'" text="未确认"
                                    size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
                                    iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
                            </view>
                        </div>
					</div>
                    <div class="item" v-if="info.chamferConfirmTime">
						<div class="left">确认时间</div><div class="rigth">{{info.chamferConfirmTime}}</div>
					</div>
			    </div>
				<div class="card_head">
				    <div class="card_head_tit2" style="margin-top: 0;">
				       故障基本信息
				    </div>
				</div>
				<view class="checkList">
					
					<view class="checkList-item">
						<view class="tit flex">
							每台设备点检表是否完成《检查完成情况》 
							<view class="mlauto">
								<u--text v-if="info.chamferEqInspectionDone=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.chamferEqInspectionDone=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;" ></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.chamferEqInspectionDoneImg"  @click="showImagePreview([imgBase+info.chamferEqInspectionDoneImg])"  mode="widthFix"  :src="imgBase+info.chamferEqInspectionDoneImg" />
								</view>
								<view>{{info.chamferEqInspectionDoneSol}}</view>
							</view>
						</view>
					</view>
					
					<view class="checkList-item">
						<view class="tit flex">
							每台设备日保养表是否完成《检查完成情况》 
							<view class="mlauto">
								<u--text v-if="info.chamferDailyMaintenanceDone=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.chamferDailyMaintenanceDone=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;" ></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.chamferDailyMaintenanceDoneImg"  @click="showImagePreview([imgBase+info.chamferDailyMaintenanceDoneImg])"  mode="widthFix"  :src="imgBase+info.chamferDailyMaintenanceDoneImg" />
								</view>
								<view>{{info.chamferDailyMaintenanceDoneSol}}</view>
							</view>
						</view>
					</view>
					
					<view class="checkList-item">
						<view class="tit flex">
							巡查空气压力是否达标《7Pa》
							<view class="mlauto">
								<u--text v-if="info.chamferAirPressureCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.chamferAirPressureCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.chamferAirPressureCheckImg"  @click="showImagePreview([imgBase+info.chamferAirPressureCheckImg])"  mode="widthFix"  :src="imgBase+info.chamferAirPressureCheckImg" />
								</view>
								<view>{{info.chamferAirPressureCheckSol}}</view>
							</view>
						</view>
					</view>
			
					<view class="checkList-item">
						<view class="tit flex">
							巡查现场刀具是否完好
							<view class="mlauto">
								<u--text v-if="info.chamferToolCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.chamferToolCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.chamferToolCheckImg"  @click="showImagePreview([imgBase+info.chamferToolCheckImg])"  mode="widthFix"  :src="imgBase+info.chamferToolCheckImg" />
								</view>
								<view>{{info.chamferToolCheckSol}}</view>
							</view>
						</view>
					</view>
			
					<view class="checkList-item">
						<view class="tit flex">
							巡查各个传感器位置是否正确
							<view class="mlauto">
								<u--text v-if="info.chamferSensorCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.chamferSensorCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.chamferSensorCheckImg"  @click="showImagePreview([imgBase+info.chamferSensorCheckImg])"  mode="widthFix"  :src="imgBase+info.chamferSensorCheckImg" />
								</view>
								<view>{{info.chamferSensorCheckSol}}</view>
							</view>
						</view>
					</view>
			
					<view class="checkList-item">
						<view class="tit flex">
							巡查上料分板器定位杆是否松动
							<view class="mlauto">
								<u--text v-if="info.chamferLocatorCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.chamferLocatorCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.chamferLocatorCheckImg"  @click="showImagePreview([imgBase+info.chamferLocatorCheckImg])"  mode="widthFix"  :src="imgBase+info.chamferLocatorCheckImg" />
								</view>
								<view>{{info.chamferLocatorCheckSol}}</view>
							</view>
						</view>
					</view>
			
					<view class="checkList-item">
						<view class="tit flex">
							巡查传送带是否清洁
							<view class="mlauto">
								<u--text v-if="info.chamferConveyorCleanliness=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.chamferConveyorCleanliness=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.chamferConveyorCleanlinessImg"  @click="showImagePreview([imgBase+info.chamferConveyorCleanlinessImg])"  mode="widthFix"  :src="imgBase+info.chamferConveyorCleanlinessImg" />
								</view>
								<view>{{info.chamferConveyorCleanlinessSol}}</view>
							</view>
						</view>
					</view>
			
					<view class="checkList-item">
						<view class="tit flex">
							巡查所有毛刷机是否开启 
							<view class="mlauto">
								<u--text v-if="info.chamferBrushMachineCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.chamferBrushMachineCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.chamferBrushMachineCheckImg"  @click="showImagePreview([imgBase+info.chamferBrushMachineCheckImg])"  mode="widthFix"  :src="imgBase+info.chamferBrushMachineCheckImg" />
								</view>
								<view>{{info.chamferBrushMachineCheckSol}}</view>
							</view>
						</view>
					</view>
			
					<view class="checkList-item">
						<view class="tit flex">
							巡查上下料龙门吸盘是否完好
							<view class="mlauto">
								<u--text v-if="info.chamferLoaderCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.chamferLoaderCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.chamferLoaderCheckImg"  @click="showImagePreview([imgBase+info.chamferLoaderCheckImg])"  mode="widthFix"  :src="imgBase+info.chamferLoaderCheckImg" />
								</view>
								<view>{{info.chamferLoaderCheckSol}}</view>
							</view>
						</view>
					</view>
			
				</view>
			</div>
			

            <!-- 打印线 -->
			<div class="card"  v-show="active_tabs==3">
			    <div class="card_head">
			        <div class="card_head_tit2" style="margin-top: 0;">
			           巡检信息
			        </div>
					<view v-if="info.checkPrintLine" class="total-content" >
						<u--text size="14" bold color="#A1A4A9" :prefixIcon="`${baseURL}/assets/fac/d-null.png`" iconStyle="font-size: 14px;color:#A1A4A9; margin-right: 5upx;" 
						:text="info.checkPrintLine.totalNulls+'项'"></u--text>
						<u--text size="14" bold color="#74D126"  :prefixIcon="`${baseURL}/assets/fac/d-checked.png`" iconStyle="font-size: 14px;color:#A1A4A9; margin-right: 5upx;" 
						:text="info.checkPrintLine.totalChecked+'项'"></u--text>
						<u--text size="14" bold color="#E97056" :prefixIcon="`${baseURL}/assets/fac/d-unchecked.png`" iconStyle="font-size: 14px;color:#74D126; margin-right: 5upx;"
						:text="info.checkPrintLine.totalUnchecked+'项'"></u--text>
					</view>
			    </div>
			    <u-divider></u-divider>
			    <div class="card_main">
					<div class="item">
					    <div class="left">巡查日期</div><div class="rigth">{{info.printLineInspectionDate}}</div>
					</div>
					<div class="item">
					    <div class="left">巡查时间</div><div class="rigth">{{info.printLineInspectionStartTime}} - {{info.printLineInspectionEndTime}}</div>
					</div>
					<div class="item">
					    <div class="left">巡查人</div>
                        <div class="rigth">{{info.inspectorStaffName}}</div>
					</div>
					<div class="item">
						<div class="left">现场确认人</div>
                        <div class="rigth">
                            {{info.printLineConfirmStaffName}}
                            <view class="mlauto">
								<u--text v-if="info.printLineConfirmStatus=='1'" text="已确认" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.printLineConfirmStatus=='0'" text="未确认"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;" ></u--text>
							</view>
                        </div>
					</div>
                    <div class="item" v-if="info.printLienConfirmTime">
						<div class="left">确认时间</div><div class="rigth">{{info.printLienConfirmTime}}</div>
					</div>
			    </div>
				<div class="card_head">
				    <div class="card_head_tit2" style="margin-top: 0;">
				       故障基本信息
				    </div>
				</div>
				<view class="checkList">
					<view class="checkList-item">
						<view class="tit flex">
							空压机是否开启
							<view class="mlauto">
								<u--text v-if="info.printLineCompCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.printLineCompCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;" ></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.printLineCompCheckImg"  @click="showImagePreview([imgBase+info.printLineCompCheckImg])"  mode="widthFix"  :src="imgBase+info.printLineCompCheckImg" />
								</view>
								<view>{{info.printLineCompCheckSol}}</view>
							</view>
						</view>
					</view>
					
					<view class="checkList-item">
						<view class="tit flex">
							每台设备点检表是否完成《检查完成情况》 
							<view class="mlauto">
								<u--text v-if="info.printLineEqInspectDone=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.printLineEqInspectDone=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;" ></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.printLineEqInspectDoneImg"  @click="showImagePreview([imgBase+info.printLineEqInspectDoneImg])"  mode="widthFix"  :src="imgBase+info.printLineEqInspectDoneImg" />
								</view>
								<view>{{info.printLineEqInspectDoneSol}}</view>
							</view>
						</view>
					</view>
					
					<view class="checkList-item">
						<view class="tit flex">
							每台设备日保养表是否完成《检查完成情况》 
							<view class="mlauto">
								<u--text v-if="info.printLineDailyMaintDone=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.printLineDailyMaintDone=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.printLineDailyMaintDoneImg"  @click="showImagePreview([imgBase+info.printLineDailyMaintDoneImg])"  mode="widthFix"  :src="imgBase+info.printLineDailyMaintDoneImg" />
								</view>
								<view>{{info.printLineDailyMaintDoneSol}}</view>
							</view>
						</view>
					</view>
			
					<view class="checkList-item">
						<view class="tit flex">
							空气压力是否达标《7Pa》
							<view class="mlauto">
								<u--text v-if="info.printLineAirPressCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.printLineAirPressCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.printLineAirPressCheckImg"  @click="showImagePreview([imgBase+info.printLineAirPressCheckImg])"  mode="widthFix"  :src="imgBase+info.printLineAirPressCheckImg" />
								</view>
								<view>{{info.printLineAirPressCheckSol}}</view>
							</view>
						</view>
					</view>
			
					<view class="checkList-item">
						<view class="tit flex">
							砂光机皮带传送带位置是否跑偏《标记处》
							<view class="mlauto">
								<u--text v-if="info.printLineBeltPosCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.printLineBeltPosCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.printLineBeltPosCheckImg"  @click="showImagePreview([imgBase+info.printLineBeltPosCheckImg])"  mode="widthFix"  :src="imgBase+info.printLineBeltPosCheckImg" />
								</view>
								<view>{{info.printLineBeltPosCheckSol}}</view>
							</view>
						</view>
					</view>
			
					<view class="checkList-item">
						<view class="tit flex">
							辊涂机油泵是否漏油
							<view class="mlauto">
								<u--text v-if="info.printLineGlueLeakCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.printLineGlueLeakCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.printLineGlueLeakCheckImg"  @click="showImagePreview([imgBase+info.printLineGlueLeakCheckImg])"  mode="widthFix"  :src="imgBase+info.printLineGlueLeakCheckImg" />
								</view>
								<view>{{info.printLineGlueLeakCheckSol}}</view>
							</view>
						</view>
					</view>
			
					<view class="checkList-item">
						<view class="tit flex">
							除尘废气是否开启
							<view class="mlauto">
								<u--text v-if="info.printLineDustCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.printLineDustCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.printLineDustCheckImg"  @click="showImagePreview([imgBase+info.printLineDustCheckImg])"  mode="widthFix"  :src="imgBase+info.printLineDustCheckImg" />
								</view>
								<view>{{info.printLineDustCheckSol}}</view>
							</view>
						</view>
					</view>
			
					<view class="checkList-item">
						<view class="tit flex">
							所有涂布机平辊是否完好 
							<view class="mlauto">
								<u--text v-if="info.printLineRollerCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.printLineRollerCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.printLineRollerCheckImg"  @click="showImagePreview([imgBase+info.printLineRollerCheckImg])"  mode="widthFix"  :src="imgBase+info.printLineRollerCheckImg" />
								</view>
								<view>{{info.printLineRollerCheckSol}}</view>
							</view>
						</view>
					</view>
			
					<view class="checkList-item">
						<view class="tit flex">
							打印房间温度是否达标《高于24度低于30度》 
							<view class="mlauto">
								<u--text v-if="info.printLineTempCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.printLineTempCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.printLineTempCheckImg"  @click="showImagePreview([imgBase+info.printLineTempCheckImg])"  mode="widthFix"  :src="imgBase+info.printLineTempCheckImg" />
								</view>
								<view>{{info.printLineTempCheckSol}}</view>
							</view>
						</view>
					</view>
                    
                    <view class="checkList-item">
						<view class="tit flex">
							上下料龙门吸盘是否完好 
							<view class="mlauto">
								<u--text v-if="info.printLineHopperCheck=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.printLineHopperCheck=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.printLineHopperCheckImg"  @click="showImagePreview([imgBase+info.printLineHopperCheckImg])"  mode="widthFix"  :src="imgBase+info.printLineHopperCheckImg" />
								</view>
								<view>{{info.printLineHopperCheckSol}}</view>
							</view>
						</view>
					</view>

                    <view class="checkList-item">
						<view class="tit flex">
							传送带是否清洁
							<view class="mlauto">
								<u--text v-if="info.printLineConveyorClean=='1'" text="已完成" 
									size="14" bold color="#4181D3" prefixIcon="checkmark-circle-fill" 
									iconStyle="font-size: 14px;color:#4181D3; margin-right: 5upx;"></u--text>
								<u--text v-if="info.printLineConveyorClean=='0'" text="未完成"
									size="14" bold color="#F57272" prefixIcon="close-circle-fill" 
									iconStyle="font-size: 14px;color:#F57272;margin-right: 5upx;"></u--text>
							</view>
						</view>
						<view class="section">
							<view class="section-item flex">
								<view class="section-item-img">
									<image v-if="info.printLineConveyorCleanImg"  @click="showImagePreview([imgBase+info.printLineConveyorCleanImg])"  mode="widthFix"  :src="imgBase+info.printLineConveyorCleanImg" />
								</view>
								<view>{{info.printLineConveyorCleanSol}}</view>
							</view>
						</view>
					</view>
				</view>
			</div>

			<view class="card">
				<div class="card_head">
				    
					<div class="card_head_tit2" style="margin-top: 0;">
					   系统信息
					</div>
				</div>
				<u-divider></u-divider>
				<div class="card_main">
				    <div class="item">
				        <div class="left">创建时间</div><div class="rigth">{{ info.createdTime }}</div>
				    </div>
				    <div class="item">
				        <div class="left">创建员工</div><div class="rigth">{{ info.createdStaffName }}</div>
				    </div>
				    <div class="item">
				        <div class="left">修改时间</div><div class="rigth">{{ info.updateTime }}</div>
				    </div>
				    <div class="item">
				        <div class="left">修改员工</div><div class="rigth">{{ info.updateStaffName }}</div>
				    </div>
				</div>
			</view>
        </div>
		<footer-btn v-if="hasFactoryPermission('dailyInspection','U') && info?.isEdit=='1'"  :cancelBtnShow="false" @onconfirm="router.push({name: 'dailyInspectionEdit',params: {id:routerQuery.id}})" confirm_btn_text="编辑"></footer-btn>
        <template v-else-if="info.inspectionStatus &&  (active_tabs || active_tabs==0)">
            <footer-btn  :cancelBtnShow="false" :confirm_btn_text=" active_tabs==3 ? '上一页' : '下一页'  " 
                :confirmBtnShow="active_tabs<4"
                @onconfirm="tabChange( active_tabs==3 ? {index:active_tabs-1} :{index:active_tabs+1} )">
                <div
                    v-if="info.inspectionStatus=='待确认' && info[dailyInspectionTabs[active_tabs]?.btnCode] == '1' "
                    :style="`background: 849EB2;`"
                    @click="hdBtn(dailyInspectionTabs[active_tabs]?.type)"  class="confirm_btn btn">
                    <u-icon
                        class="btn_icon"
                        size="16"
                        color="#fff"
                        name="checkmark-circle-fill"
                    ></u-icon>
                    <view>确认</view>
                </div>
            </footer-btn>
        </template>
		<floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
	import ProgressStatus from '@/components/ProgressStatus.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import {queryDetails,confirm}  from  '../../api/dailyInspection'
import LoadSelector from '@/components/LoadSelector.vue';
import { isArray } from 'lodash';
import {upload,dictTree,asyncUpload}  from  '@/api/common'
    import {dailyInspectionTabs,dailyInspection} from '../../constant/index.js'
    const prop =  defineProps(['id'])
    const router = useRouter();
    const util = inject("util");
	const dictStore = useDictStore()
    const routerQuery =  prop;
    const info = ref({
    });
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const init = async() =>{
		await dictStore.getDictBatch(['impact_level','product_line','repairer_status','repair_status_c','solution','replace_part']);
        getInfo()
    }


	let isSubmit = false
	const hdBtn = (type) =>{
        if(isSubmit) return false
		if(!type) return;
        isSubmit = true;
        let postData = {
            id: routerQuery.id,
            type: type
        }
		confirm(postData).then(res=>{
			uni.showModal({
				title: '提示',
				content: res.msg,
				showCancel:false,
				success: function (confirmres) {
					isSubmit = false
                    uni.$emit("refresh-dailyInspection", {refresh: true}); 
					getInfo()
				}
			});
		}).catch(e=>{
			isSubmit = false
		})
	}

	const active_tabs = ref(0)
	const tabChange = ({index,...item})=>{
		active_tabs.value = index
	}
	onShow(() => {
		 init()
	})
	onMounted(() => {
	   
	})
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
	@import '@/styles/factory.scss';
	@import '../../style/common.scss';
	.card_info{
		.card{
			.item{
				padding-bottom: 5px;
				.left{
					line-height: 1.2;
				}
				.rigth{
					text-align: right;
                    display: inline-flex;
				}
			}
		}
	}
	.addInfo{
		padding:0 10px;
		position: relative;
		border-radius: var(--card-border-radius);
		line-height: 1.4;
		width: 100%;
		.card{
			background: none;
			padding: var(--card-padding);
			position: relative;
			border-radius: var(--card-border-radius);
			line-height: 1.4;
			.card_head{
				display: flex;
				align-items: center;
				justify-content: center;
				// border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
				padding: 15px 0 10px 0;
				&_tit{
					display: flex;
					align-items: center;
					font-size: 18px;
					font-weight: 600;
					color: #333333;
					span{
						font-weight: 700;
						font-size: 16px;
						color: #000000;
						margin-right: 5px;
					}
				}
			}
		}
		:deep(){
			.int{
				background: none !important;
				.van-field__value{
					background: #fff;
				}
				// &.van-cell:after{
				//     content: none;
				// }
			}
		}
	}
	:deep(.tool){
	    .u-tabs__wrapper__nav__item{
	    		flex: 1;
	    }
	}
	:deep(.tag_list){
		display: flex;
		.tag-item{
			margin-left: 5px;
		}
		.u-tag--primary{
			background-color: #93AABB;
			border-color: #93AABB;
		}
		.u-tag--primary--plain{
			border-color: #93AABB;
			.u-tag__text--primary--plain{
				color: #93AABB;
			}
		}
	}
	.checkList{
		color: #303133;
		font-size: 14px;
		.checkList-item{
			padding-bottom: 10px;
		}
		.section{
			box-shadow: 0 1px 4px rgba(0, 0, 0, 0.0509803922);
			border-radius: 4px;
			opacity: 1;
			border: 1px solid #E2E6F6;
			padding: 4px 8px;
			margin-top: 5px;
		}
		.section-item-img{
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			width: 80px;
			height: 80px;
			background-color: rgb(244, 245, 247);
			border-radius: 2px;
			margin: 0 8px 8px 0;
			box-sizing: border-box;
			image{
				max-width: 100%; max-height: 100%
			}
		}
	}
    .mlauto{
        margin-left: auto;
    }
</style>