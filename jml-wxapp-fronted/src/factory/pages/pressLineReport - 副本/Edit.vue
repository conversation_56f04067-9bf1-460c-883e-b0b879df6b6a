<template>
    <div class="page pd100">
        <div class="card_info">
			<u--form labelPosition="top" labelWidth="auto" ref="form1" >
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">{{info.pressLineType}}</div>
                </div>
				<u-form-item label="日期"  prop="date">
					<u--input v-model="info.date"  class="int" readonly  placeholder="日期"  />
					<template #right><u-icon name="calendar" size="22" @click="onDateTimeShow('date')"></u-icon></template>
				</u-form-item>
				<u-form-item label="工单号" prop="workNo" required>
					<scan-code-input  v-model="info.workNo"  />
					<!-- <u--input  v-model="info.workNo"  class="int"   placeholder="请输入"  /> -->
				</u-form-item>
				<u-form-item v-if="['纸张使用情况'].includes(info.pressLineType)" label="纸张型号" required prop="paperModel">
					<u--input  v-model="info.paperModel"  class="int"   placeholder="请输入"  />
				</u-form-item>
				<u-form-item v-if="['基材使用情况'].includes(info.pressLineType)" label="基材尺寸" required prop="substrateSize">
					<u--input  v-model="info.substrateSize"  class="int"   placeholder="请输入"  />
				</u-form-item>
				<view v-if="['纸张使用情况','基材使用情况'].includes(info.pressLineType)">

					<u-form-item label="厂家" required prop="manufacturer">
						<u--input v-model="info.manufacturer"  class="int"   placeholder="请输入"  />
					</u-form-item>
					<u-form-item label="是否检验" prop="isInspected" required>
						<u-radio-group v-model="info.isInspected" activeColor="#849EB2" iconPlacement="left">
						    <u-radio :customStyle="{marginRight: '16px'}"  label="是" name="1"></u-radio>
						    <u-radio  label="否" name="0"></u-radio>
						</u-radio-group>
					</u-form-item>
					
					<template v-if="info.pressLineType=='纸张使用情况'">
						<u-form-item label="领出（张）" prop="issuedQtySheet" required>
							<u--input type="number" v-model.number="info.issuedQtySheet"  class="int"   placeholder="请输入"  />
						</u-form-item>
						<u-form-item label="压贴（张）" required prop="laminateQtySheet">
							<u--input type="number" v-model.number="info.laminateQtySheet"  class="int"   placeholder="请输入"  />
						</u-form-item>
						<u-form-item label="消耗（张）" required prop="consumptionQtySheet">
							<u--input type="number" v-model.number="info.consumptionQtySheet"  class="int"   placeholder="请输入"  />
						</u-form-item>
						<u-form-item label="结余（张）" required prop="remainingQtySheet">
							<u--input type="number" v-model.number="info.remainingQtySheet"  class="int"   placeholder="请输入"  />
						</u-form-item>
					</template>
					<template v-if="info.pressLineType=='基材使用情况'">
						<u-form-item label="领出（片）" prop="issuedQtyPiece" required>
							<u--input type="number" v-model.number="info.issuedQtyPiece"  class="int"   placeholder="请输入"  />
						</u-form-item>
						<u-form-item label="压贴（片）" required prop="laminateQtyPiece">
							<u--input type="number" v-model.number="info.laminateQtyPiece"  class="int"   placeholder="请输入"  />
						</u-form-item>
						<u-form-item label="消耗（片）" required prop="consumptionQtyPiece">
							<u--input type="number" v-model.number="info.consumptionQtyPiece"  class="int"   placeholder="请输入"  />
						</u-form-item>
						<u-form-item label="结余（片）" required prop="remainingQtyPiece">
							<u--input type="number" v-model.number="info.remainingQtyPiece"  class="int"   placeholder="请输入"  />
						</u-form-item>
					</template>
				</view>
				<view v-if="['生产情况'].includes(info.pressLineType)">
					<u-form-item label="产品型号" required prop="productModel">
						<u--input v-model="info.productModel"  class="int"   placeholder="请输入"  />
					</u-form-item>
					<u-form-item label="规格" required prop="spec">
						<u--input v-model="info.spec"  class="int"   placeholder="请输入"  />
					</u-form-item>
					<u-form-item label="A级" required prop="gradeA">
						<u--input type="number" v-model.number="info.gradeA"  class="int"   placeholder="请输入"  />
					</u-form-item>
					<u-form-item label="B级" required prop="gradeB">
						<u--input type="number" v-model.number="info.gradeB"  class="int"   placeholder="请输入"  />
					</u-form-item>
					<u-form-item label="C级" required prop="gradeC">
						<u--input type="number" v-model.number="info.gradeC"  class="int"   placeholder="请输入"  />
					</u-form-item>
					<u-form-item label="其他" required prop="other">
						<u--input type="number" v-model.number="info.other"  class="int"   placeholder="请输入"  />
					</u-form-item>
					<u-form-item label="合计（张）" required prop="totalQtySheet">
						<u--input type="number" v-model.number="info.totalQtySheet"  readonly placeholder="自动计算"  />
					</u-form-item>
				</view>
					
					
				<u-form-item label="其他情况" prop="otherCondition" >
					<u--textarea :count="!!info.otherCondition" maxlength="200"  v-model="info.otherCondition"  class="int"   placeholder="请填写"  />
				</u-form-item>
				<u-form-item label="备注" prop="remark" >
					<u--textarea :count="!!info.remark" maxlength="800"  v-model="info.remark"  class="int"   placeholder="备注"  />
				</u-form-item>
					
			</div>
			</u--form>
        </div>
		<u-datetime-picker
			:show="dateTimeShow"
			v-model="currentTime"
			confirmColor="#849EB2"
			closeOnClickOverlay
			@confirm="onDateTimeConfirm"
			@cancel="dateTimeShow = false"
			@close="dateTimeShow = false"
			:mode="dateMode"
		></u-datetime-picker>
		
        <footer-btn :cancelBtnShow="false"  confirm_btn_text="保存" @onconfirm="save()"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import LoadSelector from '@/components/LoadSelector.vue';
import Selector from '@/components/Selector.vue';
import scanCodeInput from '../../components/scanCodeInput.vue';
import dayjs from "dayjs";
import {queryDetails,saveEdit}  from  '../../api/pressLineReport'
import {queryListByStaff}  from  '@/api/company'
import {useDateTime}  from  '../../vueUse/datetimeUse'
	const prop =  defineProps({
			id: String,
			isComponent: {
				type: Boolean,
				default: false,
			},
			pressLineType:{
				type: String,
				default: null,
			}
		})
	const routerQuery = prop;
	const appStore = useAppStore();
	const router = useRouter();
	const { userInfo } = useUserStore();
	
    // 相关字典
    const dictStore = useDictStore()
    const companyList = ref([])
	const util = inject("util");
    const deleteFild = ['accName','createdStaffName','updateStaffName','salesLeadName','marketingName','areaName'];
    const requiredFild = [
        // {key:'name',message:'请填写机会名称',required: true,},
        // {key:'amount',message:'请填写金额',required: true,},
        // {key:'type',message:'请选择机会类型',required: true,},
    ]
    const save = () => {
        for(let index  in requiredFild){ 
            var item =  requiredFild[index]
            if(item.required && (!info.value[item.key] || info.value[item.key]=='')){
            	return uni.showToast({
            		title: item.message,
            		duration: 1500,
            		icon:'none'
            	});
            }
        };
        // let postData = {}
        // for(var key in info.value){
        //     if(!deleteFild.includes(key))
        //         postData[key] = info.value[key];
        // }
        saveEdit(info.value).then((res)=>{
            uni.showModal({
            	title: '提示',
            	content: info.value.id ?'修改成功':'保存成功',
            	showCancel:false,
            	success: function (confirmres) {
            		if (confirmres.confirm) {
            			if(prop.isComponent)emit('confirm', res)
            			else{
            				uni.$emit("refresh-pressLineReport", {refresh: true}); 
            				router.back();
            			}
            		} else if (confirmres.cancel) {
            			console.log('用户点击取消');
            		}
            	}
            });
        })
    }
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const info = ref({
		pressLineType:prop.pressLineType?.trim(),
		totalQtySheet:null
    });
	// debugger
	// console.log( ['纸张使用情况','基材使用情况'].includes(info.value.pressLineType))
	// console.log( ['纸张使用情况','基材使用情况'].includes('基材使用情况'))
	const {dateTimeShow,field,currentTime,onDateTimeShow,onDateTimeConfirm,dateMode} = useDateTime(info)
	
	watch(()=>[info.value.gradeA,info.value.gradeB,info.value.gradeC,info.value.other],([newA, newB,newC,newO], olds)=>{
		// debugger
		info.value.totalQtySheet = ((newA??0) + (newB??0) + ( newC??0) + (newO??0))
	},{immediate:false,deep:false})
	// if(info.value?.pressLineType=='生产情况'){
		
	// 	// info.value.totalQtySheet = computed(() => {
	// 	// 	debugger
	// 	// 	return (info.value?.gradeA??0) + (info.value?.gradeB??0) + (info.value?.gradeC??0) + (info.value?.other??0)
	// 	// })
	// }
	
    const init = async() =>{
        if(routerQuery.id){
            getInfo()
        }
    }
	
    onMounted(() => {
        init()
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
	:deep(.card_info){
		.u-form-item__body{
			padding: 15px 0 0 0;
		}
	}
</style>