<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        {{ info.name }}
                    </div>
					<view class="head_status bg">{{ info.pressLineType }}</view>
					<view> <u-icon name="calendar" :label="info.date" labelSize="12"></u-icon> </view>
                </div>
				
            </div>
        </div>
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                       基本信息
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
					<div class="item">
					    <div class="left">工单号</div><div class="rigth">{{info.workNo}}</div>
					</div>
					<div v-if="['纸张使用情况'].includes(info.pressLineType)" class="item">
					    <div class="left">纸张型号</div><div class="rigth">{{info.paperModel}}</div>
					</div>
					<div v-if="['基材使用情况'].includes(info.pressLineType)" class="item">
					    <div class="left">基材尺寸</div><div class="rigth">{{info.substrateSize}}</div>
					</div>
					<template v-if="['纸张使用情况','基材使用情况'].includes(info.pressLineType)">
					<div class="item">
					    <div class="left">厂家</div><div class="rigth">{{info.manufacturer}}</div>
					</div>
                    <div class="item">
                        <div class="left">是否检验</div><div class="rigth">{{info.isInspected==1?'是':'否'}}</div>
                    </div>
					<template v-if="info.pressLineType=='纸张使用情况'">
						<div class="item">
							<div class="left">领出（张）</div><div class="rigth">{{info.issuedQtySheet}}</div>
						</div>
						<div class="item">
							<div class="left">压贴（张）</div><div class="rigth">{{info.laminateQtySheet}}</div>
						</div>
						<div class="item">
							<div class="left">消耗（张）</div><div class="rigth">{{info.consumptionQtySheet}}</div>
						</div>
						<div class="item">
							<div class="left">结余（张）</div><div class="rigth">{{info.remainingQtySheet}}</div>
						</div>
					</template>
					<template v-if="info.pressLineType=='基材使用情况'">
						<div class="item">
							<div class="left">领出（片）</div><div class="rigth">{{info.issuedQtyPiece}}</div>
						</div>
						<div class="item">
							<div class="left">压贴（片）</div><div class="rigth">{{info.laminateQtyPiece}}</div>
						</div>
						<div class="item">
							<div class="left">消耗（片）</div><div class="rigth">{{info.consumptionQtyPiece}}</div>
						</div>
						<div class="item">
							<div class="left">结余（片）</div><div class="rigth">{{info.remainingQtyPiece}}</div>
						</div>
					</template>
				</template>
				<template v-if="['生产情况'].includes(info.pressLineType)">
                    <div class="item">
                        <div class="left">产品型号</div>
                        <div class="rigth">{{info.productModel}}</div>
                    </div>
                    <div class="item">
                        <div class="left">规格</div><div class="rigth">{{info.spec}}</div>
                    </div>
                    <div class="item">
                        <div class="left">A级</div>
                        <div class="rigth">{{info.gradeA}}</div>
                    </div>
					<div class="item">
					    <div class="left">B级</div><div class="rigth">{{info.gradeB}}</div>
					</div>
					<div class="item">
					    <div class="left">C级</div><div class="rigth">{{info.gradeC}}</div>
					</div>
					<div class="item">
					    <div class="left">其他</div><div class="rigth">{{info.other}}</div>
					</div>
                    <div class="item">
                        <div class="left">合计（张）</div>
                        <div class="rigth">{{info.totalQtySheet}}</div>
                    </div>
                    
				</template>
				<div class="item">
				    <div class="left">其他情况</div><div class="rigth">{{info.otherCondition}}</div>
				</div>
				<div class="item">
				    <div class="left">备注</div>
				    <div class="rigth">{{info.remark}}</div>
				</div>
                    
                </div>

                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.createdTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.updateTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>

        <footer-btn v-if="hasFactoryPermission('pressLineReport','U')"  :cancelBtnShow="false" @onconfirm="router.push({name: 'pressLineReportEdit',params: {id:routerQuery.id}})" confirm_btn_text="编辑"></footer-btn>
		<floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import {queryDetails}  from  '../../api/pressLineReport'

    const prop =  defineProps(['id'])
    const router = useRouter();
    const util = inject("util");
	const dictStore = useDictStore()
    const routerQuery =  prop;
    const info = ref({
    });
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const init = async() =>{
        getInfo()
    }

	onShow(() => {
		uni.$once("refresh", (data) => {
			init()
		})
	})
    onMounted(() => {
        init()
    })
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
	@import '@/styles/factory.scss';
</style>