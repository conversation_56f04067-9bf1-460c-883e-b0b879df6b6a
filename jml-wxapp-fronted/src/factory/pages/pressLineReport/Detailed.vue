<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        {{ info.name }}
                    </div>
					<view class="head_status bg">{{ info.pressLineType }}</view>
					<view> <u-icon name="calendar" :label="`${info.date}  ${info.startTime} ~ ${info.endTime}`" labelSize="12"></u-icon> </view>
                </div>
				
            </div>
        </div>
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                       基本信息
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
					<div class="item">
					    <div class="left">工单号</div><div class="rigth">{{info.workNo}}</div>
					</div>
					<!-- <div class="item">
					    <div class="left">型号</div><div class="rigth">{{info.model}}</div>
					</div> -->
					<div class="item">
					    <div class="left">产能</div><div class="rigth">{{info.capacity}}</div>
					</div>
                    <div class="item">
                        <div class="left">良品数</div><div class="rigth">{{info.goodQty}}</div>
                    </div>
                    <div class="item">
                        <div class="left">不良品数</div><div class="rigth">{{info.defectiveQty}}</div>
                    </div>
                    <div class="item">
                        <div class="left">纸张耗用</div><div class="rigth">{{info.paperConsumption}}</div>
                    </div>
                    <div class="item">
                        <div class="left">基材耗用</div><div class="rigth">{{info.substrateConsumption}}</div>
                    </div>
                    <div class="item">
                        <div class="left">备注</div>
                        <div class="rigth">{{info.remark}}</div>
                    </div>
                </div>
			 </div>
		</div>

		<div class="card_info">
			<div class="card">
				<div class="card_head">
					<div class="card_head_tit2" style="margin-top: 0;">
					   压贴线-日报表信息
					</div>
				</div>

				<div class="detail_list">
					<uni-table border stripe emptyText="暂无更多数据">
						<!-- 表头行 -->
						<uni-tr>
							<uni-th width="80" align="center">型号</uni-th>
                            <uni-th width="80" align="center">产能</uni-th>
                            <uni-th width="120" align="center">良品数量</uni-th>
                            <uni-th width="120" align="center">不良品数量</uni-th>
                            <!-- <uni-th width="120" align="center">不良说明</uni-th> -->
                            <uni-th width="120" align="center">纸张耗用</uni-th>
                            <uni-th width="120" align="center">基材耗用</uni-th>
                            <uni-th width="80" operate align="center">操作</uni-th>
						</uni-tr>
						<!-- 表格数据行 -->
						<uni-tr v-for="(detail,i) in info.pressLineDailyReportDetailList" :key="i">
							<uni-td align="center"> {{detail.model}} </uni-td>
                            <uni-td align="center"> {{detail.capacity}} </uni-td>
                            <uni-td align="center"> {{detail.goodQty}}</uni-td>
                            <uni-td align="center"> {{detail.defectiveQty}}</uni-td>
                            <!-- <uni-td align="center"> {{detail.defectiveDesc}}</uni-td> -->
                            <uni-td align="center"> {{detail.paperConsumption}}</uni-td>
                            <uni-td align="center"> {{detail.substrateConsumption}}</uni-td>
                            <uni-td  operate align="center">
                                <view class="uni-group">
                                    <u-button class="uni-button" @click="handleShow(detail,i)" color="#849EB2" :plain="true" :hairline="true" size="mini" type="primary">查看</u-button>
                                </view>
                            </uni-td>
						</uni-tr>
					</uni-table>
				</div>
			</div>
		</div>
		<div class="card_info">
			<div class="card">
				<div class="card_head">
					<div class="card_head_tit2" style="margin-top: 0;">
					   系统信息
					</div>
				</div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.createdTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.updateTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>
		
		<!-- 底部弹出 -->
		<u-popup :show="showPopInfo" :round="10" closeable mode="bottom" @close="showPopInfo = false">
		    <div class="pop_info">
		        <div class="pop_info_tit">压贴线详情</div>
		        <div class="card_info">
		            <div class="card">
		                <div class="card_main">
							<div class="item">
							    <div class="left">型号</div><div class="rigth">{{popinfo.model}}</div>
							</div>
		                    <div class="item">
		                        <div class="left">产能</div><div class="rigth">{{popinfo.capacity}}</div>
		                    </div>
                            
							<div class="item">
							    <div class="left">良品数量</div><div class="rigth">{{popinfo.goodQty}}</div>
							</div>
		                    <div class="item">
		                        <div class="left">不良品数量</div><div class="rigth">{{popinfo.defectiveQty}}</div>
		                    </div>
		                    <!-- <div class="item">
		                        <div class="left">不良说明</div><div class="rigth">{{popinfo.defectiveDesc}}</div>
		                    </div> -->
		                    <div class="item">
                                <div class="left">纸张耗用</div><div class="rigth">{{popinfo.paperConsumption}}</div>
                            </div>
                            <div class="item">
                                <div class="left">基材耗用</div><div class="rigth">{{popinfo.substrateConsumption}}</div>
                            </div>
		                    <div class="item">
		                        <div class="left">创建日期</div><div class="rigth">{{popinfo.createdTime}}</div>
		                    </div>
                             <div class="item">
		                        <div class="left">备注</div><div class="rigth">{{popinfo.remark}}</div>
		                    </div>
		                </div>
		            </div>
		        </div>
		    </div>
		</u-popup>

        <footer-btn v-if="hasFactoryPermission('pressLineReport','U')"  :cancelBtnShow="false" @onconfirm="router.push({name: 'pressLineReportEdit',params: {id:routerQuery.id}})" confirm_btn_text="编辑"></footer-btn>
		<floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import {queryDetails}  from  '../../api/pressLineReport'

    const prop =  defineProps(['id'])
    const router = useRouter();
    const util = inject("util");
	const dictStore = useDictStore()
    const routerQuery =  prop;
    const info = ref({
    });
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const init = async() =>{
        getInfo()
    }
    const showPopInfo = ref(false)
	const popinfo = ref({})
	const handleShow = async(info,index) =>{
	    popinfo.value = info;
	    showPopInfo.value = true
	}
	onShow(() => {
		 init()
	})
	onMounted(() => {
	   
	})
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
	@import '@/styles/factory.scss';
</style>