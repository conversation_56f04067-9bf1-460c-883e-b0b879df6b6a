<template>
    <view class="page">
        <view class="head">
			<!-- <view class="tool flex">
				<toolsPicker
					v-model="searchQuery.pressLineType"
					 title="类型"
					 dataValue="dval"
					 dataText="dname"
					dictKey="press_line_type"
					@confirm="util.debounce(onSearch)"
				></toolsPicker>
			</view> -->
            <view class="search">
                <u-search
                    v-model="searchQuery.multiFiled"
                    placeholder="工单号"
                    shape="square"
                    @change="util.debounce(onSearch)"
                	@custom="util.debounce(onSearch)"
                >
                </u-search>
            </view>
        </view>
        <view class="card_list">
            <view class="card_tool">
                <view class="tool">
                    <view @click="reset" v-if="showReset" style="position: absolute; left: 0;  display: flex; align-items: center;">
                        <u-icon size="16" name="close-circle-fill" color="#849EB2" />
                        <span class="" style=" margin-left: 5px; font-weight: normal;">清除筛选</span>
                    </view>
					<!-- SU显示员工筛选选项员工获取调用： -->
					<toolsPicker
						v-if="hasFactoryPermission('pressLineReport','SU')"
						v-model="searchQuery.createdStaffId"
						 title="员工"
						 dataValue="staffId"
						 dataText="staffName"
						load_url="/api/jml/staff/list4FactorySubStaffWithoutSelf"
						@confirm="util.debounce(onSearch)"
					></toolsPicker>
                    <view class="item">
                    <uni-datetime-picker ref="calendarShow" color="#849EB2"
                    	type="daterange"
                    	:end="maxDate"
                    	rangeSeparator="至"
                    	v-model="datetimerange"
                    	@change=onCalendarConfirm
                    >
                    	<u-icon size="6" labelSize="12" labelPos="left" :label="searchQuery.startDate ? `${searchQuery.startDate} - ${searchQuery.endDate}` : '日期'" name="/static/icon/sel_icon.png" />
                    </uni-datetime-picker>	
                    </view>
                </view>
            </view>
             <view class="swiper-item" style="height: calc(100% - 30px )">
				<scroll-view
					scroll-y enable-flex refresher-enabled	
					@scrolltolower="getDataPage()" 
					@refresherrefresh="getFresh"
					:refresher-triggered="refresherTriggered"
					class="scrollHeight"
					style="height: 100%;">
					<uni-swipe-action>
            		<div v-for="(item,index) in data.list" :key="index" class="van-swipe-cell">
            			<uni-swipe-action-item >
							<view class="card"  @click="handDetail(item)">
								<view class="card_head">
									<view class="card_head_tit">
										{{ item.name }}
									</view>
									<!-- <view class="head_status bg">{{ item.pressLineType }}</view> -->
								</view>
								<u-divider></u-divider>
								<view class="card_main">
								
									<view  class="item">
										<view class="left">日期</view>
										<view class="rigth">
											<view class="rigth">{{item.date}}    {{item.startTime}} ~ {{item.endTime}}</view>
										</view>
									</view>
									<view  class="item">
										<view class="left">工单号</view>
										<view class="rigth">
											<view class="rigth">{{item.workNo}}</view>
										</view>
                                    </view>
									<view class="card_footer">
										<view class="item">
											<view class="left">创建时间</view>
											<view class="rigth">{{item.createdTime}} <span class="link">查看 <u-icon name="arrow-right" size="12" /></span> </view>
										</view>
										<view class="item">
											<view class="left">创建员工</view>
											<view class="rigth">{{item.createdStaffName}}</view>
										</view>
									</view>
								</view>
								<u-divider></u-divider>
                            </view>
						</uni-swipe-action-item>
					</div>
					</uni-swipe-action>
					<view class="empty_box" v-if="!data.loading && (!data.list ||  data.list.length<=0)">
						<u-empty v-if="!data.list || data.list.length==0" text="未找到信息" :icon="`${config.static}images/empty/data.png`" ></u-empty>
					</view>
               </scroll-view>
           </view>
        </view>

		<!-- <u-calendar v-model:show="calendarShow" color="#849EB2"
			monthNum = "13"
			showLunar
			:minDate="minDate"
			:maxDate="maxDate"
			confirm-disabled-text="请选择结束时间" mode="range"  round="10" 
			@confirm="onCalendarConfirm"
			closeOnClickOverlay
			@close="calendarShow=false" 
		/> -->

		
		<!-- 添加类型 -->
		<u-modal v-model:show="addDialogShow" :before-close="beforeClose" @confirm="addConfirm" @close="addDialogShow = false" @cancel="addDialogShow = false" title="选择类型" show-cancel-button confirmColor="#849EB2" confirm-button-text="提交">
		    <view class="addInfo">
		        <view class="card">
		            <u-radio-group v-model="addType" activeColor="#849EB2" iconPlacement="right" placement="column">
						<template v-for="item in dict['press_line_type']" :key="item.dval">
		                <u-radio :customStyle="{padding:'5px 10px'}"   :label="item.dname" :name="item.dval" />
						</template>
		            </u-radio-group>
		        </view>
		    </view>
		</u-modal>

        <floating-window>
            <view
                v-if="hasFactoryPermission('pressLineReport','C')"
                @click="router.push({name:'pressLineReportEdit'})"
                class="item add_btn"
            >
               <u-icon size="20"  name="/static/icon/add.png" />
            </view>
        </floating-window>
    </view>
</template>
<script setup>
	import FloatingWindow from '@/components/FloatingWindow.vue';
	import toolsPicker from '../../components/toolsPicker.vue';
	import {queryPageData}  from  '../../api/pressLineReport'
	import {queryUserList}  from  '@/api/common'
	import dayjs from "dayjs";
	import userPicker from '../../components/userPicker';
	import {useDateTime}  from  '../../vueUse/datetimeUse'
    const router = useRouter();
	const util = inject("util");
    const {data,finishedText} = useDataList()
    const dictStore = useDictStore()
    const onSearch = () => {
        initEmpty()
    }
    const routerQuery =  defineProps({});
	var pages = getCurrentPages();
	//获取当前页面
	var currentPage = pages[pages.length - 1];
	const urlLink = ref(currentPage.route)
	console.log(currentPage, '打印当前页路由')
	
    const searchQuery = reactive({
        multiFiled:'',
        startDate: routerQuery.startDate || '',
        endDate: routerQuery.endDate || '',
        createdStaffId:'',
        pageNo:data.pageNo,
        pageSize:data.pageSize,
		pressLineType:''
    })
	console.log(searchQuery)
	// 下拉刷新
	const refresherTriggered = ref(false);
	const getFresh=()=> {
		console.log('refresh');
		refresherTriggered.value = 'restore'
		initEmpty()
	}
	// const onPulling=(e)=> {
	// 	console.log('onPulling');
	// 	if (e.detail.deltaY < 0) return
	// }
	
    const getDataPage = (pageNo) =>{
    	if (data.finished || data.loading)return;
        searchQuery.pageNo = pageNo || data.pageNo;
		data.loading = true;
		if(searchQuery.pageNo<=1) data.initEmpty()
        queryPageData(searchQuery).then((res) => {
			if(refresherTriggered.value !== false) refresherTriggered.value = false
            data.list = [...data.list, ...res.data.list]
			data.totalRow = res.data.totalRow
			data.pageNo = data.pageNo + 1 ;
			data.loading = false;
			data.finished = res.data.lastPage;
        }).catch((err) => {
            data.finished = true;
        });
    }

    const deleteRecord = (id)=>{
		uni.showModal({
		    title: '提示',
		    content: '是否删除该记录？',
			success: function (unires) {
				if (unires.confirm) {
					deleteReport(id).then((res) => {
					    uni.showModal({
					        title:'提示',
					        showCancel:false,
					        content: '操作成功',
					    }).then((aa) => {
					        initEmpty()
					    })
					}).catch((err) => {
					});
				}
			}
		})
    }
    const init = async() =>{
		data.finished && (data.finished = false)
        getDataPage()
    }
    const initEmpty =() =>{
        data.initEmpty()
		getDataPage(1)
    }
	
	// 添加相关
	const addDialogShow = ref(false)
	const addType = ref(null)
	const showAddPop = async()=>{
	    !dictStore.hasDict('press_line_type') && await dictStore.getDictBatch(['press_line_type']);
	    addDialogShow.value = true;
	}
	const addConfirm = ()=>{
	    if(!addType.value)  return uni.showToast({
			title: '请选择类型',
			duration: 1500,
			icon:'none'
		});
		addDialogShow.value = false;
	    router.push({name: 'pressLineReportEdit',params: {pressLineType:addType.value}})
	}
	const beforeClose = (action) =>{
	    return action !== 'confirm' || (addType.value)
	}
	
	onShow(()=>{
		
	})
    onMounted(() => {
		init()
		uni.$once("refresh-pressLineReport", (data) => {
			initEmpty()
		})
    })
    //点击明细
    const handDetail =(item)=>{
        router.push({name: 'pressLineReportInfo',params: {id: item.id}})
    }

    /**
     *  搜索相关 开始
     */
    const calendarShow = ref(false)
    const datetimerange = ref([])
    const {minDate,maxDate} = useDateTime()
    const onCalendarConfirm = (values) => {
    	const start= values[0],end = values[values.length - 1];
        calendarShow.value = false;
    	searchQuery.startDate = start;
    	searchQuery.endDate = end;
        // searchQuery.startDate = `${dayjs(start).format('YYYY-MM-DD')}`;
        // searchQuery.endDate = `${dayjs(end).format('YYYY-MM-DD')}`;
        initEmpty()
    };
    
    // 是否显示清除图标
    const showReset = computed(()=>{
        return (searchQuery.startDate != '' ||
        searchQuery.endDate != '' ||
        searchQuery.createdStaffId != '' ||
		searchQuery.pressLineType != '')
    });
    //清空搜索条件
    const reset = () =>{
        searchQuery.startDate = ''
        searchQuery.endDate = ''
        searchQuery.createdStaffId= ''
		searchQuery.pressLineType= ''
        initEmpty()
    }
    
</script>
<style lang="scss" scoped>
    @import '@/styles/list.scss';
    @import '@/styles/factory.scss';
    .page{
		.search{
			margin-left: 10upx;
		}
        .addInfo{
			padding: 10px;
			position: relative;
			border-radius: var(--card-border-radius);
			line-height: 1.4;
			width: 100%;
			.u-icon{
				padding: 10px 15px;
			}
			.card{
				background: none;
				padding: var(--card-padding);
				position: relative;
				border-radius: var(--card-border-radius);
				line-height: 1.4;
				.card_head{
					display: flex;
					align-items: center;
					justify-content: center;
					// border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
					padding: 15px 0 10px 0;
					&_tit{
						display: flex;
						align-items: center;
						font-size: 18px;
						font-weight: 600;
						color: #333333;
						span{
							font-weight: 700;
							font-size: 16px;
							color: #000000;
							margin-right: 5px;
						}
					}
				}
			}
			
		}

	}
</style>