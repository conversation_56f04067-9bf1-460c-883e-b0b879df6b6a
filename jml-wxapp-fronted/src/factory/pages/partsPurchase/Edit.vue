<template>
    <div class="page pd100">
        <div class="card_info">
			<view class="tabInfo"> {{ info.jobType }} - {{ info.prodLineOrDept }} </view>
			<u--form labelPosition="top" labelWidth="auto" ref="form1" >
				<div v-if="info.deviceRepairId" class="card">
					<div class="card_head">
						<div class="card_head_tit3">
							设备维修单号 
							
							<span @click="router.push({name: 'deviceRepairInfo',params: {id:info.deviceRepairId}})" class="link">
							    {{info.deviceRepairName}}
							</span>
						</div>
					</div>
				</div>
				<div class="card mt10 pd10">
					<div class="card_head">
						<div class="card_head_tit2">申请零配件采购</div>
					</div>
					
					<div class="detail_list">
						<uni-table border stripe emptyText="暂无更多数据">
							<!-- 表头行 -->
							<uni-tr>
								<uni-th width="80" align="center">编号</uni-th>
								<uni-th width="120" align="center">名称</uni-th>
								<uni-th width="120" align="center">数量</uni-th>
								<uni-th width="120" align="center">单位</uni-th>
								<uni-th width="80" operate align="center">操作</uni-th>
							</uni-tr>
							<!-- 表格数据行 -->
							<uni-tr v-for="(detail,i) in info.partsPurchaseDetailsList" :key="i">
								<uni-td align="center"> {{i+1}} </uni-td>
								<uni-td align="center"> {{detail.name}}</uni-td>
								<uni-td align="center"> {{detail.qty}}</uni-td>
								<uni-td align="center"> {{detail.unit}}</uni-td>
								<uni-td  operate align="center">
									<view class="uni-group">
										<u-button class="uni-button" @click="handleShow(detail,i)" color="#849EB2" :plain="true" :hairline="true" size="mini" type="primary">查看</u-button>
										<div>
										    <u-button class="uni-button"  @click="deleteRow()" :plain="true" :hairline="true" size="mini" type="primary">删除</u-button>
										</div>
									</view>
								</uni-td>
							</uni-tr>
						</uni-table>
					    <div @click="handleShow({partImg:[]},info.partsPurchaseDetailsList.length)"   class="detail_add">
							<u-icon name="plus" color="#849EB2" size="12"></u-icon>
							添加零件
						</div>
					</div>
					
				</div>
				
				<div class="card mt10 pd20">
					<div class="card_head">
						<div class="card_head_tit2">零配件采购申请基本信息</div>
					</div>
					
					<!-- <u-form-item label="损坏情况及故障描述" required prop="damageDesc">
						<zxz-uni-data-select  placeholder="请选择" v-model="info.damageDesc" :localdata="dict['product_line']"></zxz-uni-data-select>
					</u-form-item> -->
					<u-form-item label="备注" prop="remark" >
						<u--textarea :count="!!info.remark" maxlength="800"  v-model="info.remark"  class="int"   placeholder="备注"  />
					</u-form-item>
					
				</div>
			</u--form>
        </div>
		
		<u-popup :show="showAddPop" :round="10" closeable mode="bottom" @close="showAddPop = false">
			<div class="addInfo_pop">
				<div class="card">
					<div class="card_head">
						<div class="card_head_tit">添加零配件</div>
					</div>
					<scroll-view style="height: 450px; max-height: 70vh;" scroll-y>
						<div class="card_from">
							<u--form labelPosition="top" labelWidth="auto" 
								ref="popform" 
								errorType="toast" 
								:model="popInfo" 
								:rules="{
									qty:[{ required: true, message: '请输入数量'}],
									unit:[{ required: true, message: '请输入单位'}],
									damageDesc:[{ required: true, message: '请输入损坏情况及故障描述'}],
									handlingMethod:[{ required: true, message: '请选择处理方式'}],
								}"
							>
								<u-form-item label="零配件名称" prop="name"  >
									<u--input  v-model="popInfo.name"   placeholder="请输入"  />
								</u-form-item>
								
								<u-form-item label="数量" prop="qty" required> 
									<u--input type="number" v-model.number="popInfo.qty"  placeholder="请输入"  />
								</u-form-item>
								<u-form-item label="单位" prop="unit" required >
									<u--input  v-model="popInfo.unit"   placeholder="请输入"  />
								</u-form-item>
								<u-form-item label="建议供应商名称" prop="supplier"  >
									<u--input  v-model="popInfo.supplier"   placeholder="请输入"  />
								</u-form-item>
								<u-form-item label="损坏情况及故障描述" prop="damageDesc" required>
									<!-- <zxz-uni-data-select  placeholder="请选择" v-model="popInfo.damageDesc" :localdata="dict['product_line']"></zxz-uni-data-select> -->
									<u--textarea :count="!!popInfo.damageDesc" maxlength="150"  v-model="popInfo.damageDesc"  class="int"   placeholder="请输入"  />
								</u-form-item>
								<u-form-item label="处理方式" prop="handlingMethod" required>
									<zxz-uni-data-select  placeholder="请选择" v-model="popInfo.handlingMethod" :localdata="dict['handling_method']"></zxz-uni-data-select>
								</u-form-item>
								<u-form-item label="零件照片" prop="partImg" >
									<u-upload
										:max-count="2"
										multiple
										@after-read="afterRead($event,'partImg')"
										:deletable="true"
										:sizeType="['compressed']"
										:before-delete="(file,detail)=>beforeDelete(file,detail)"
										@delete="beforeDelete($event,'partImg')"
										:fileList="popInfo.partImg"
									>
									</u-upload>
								</u-form-item>
								
								<u-form-item label="备注" prop="remark" >
									<u--textarea :count="!!popInfo.remark" maxlength="150"  v-model="popInfo.remark"  class="int"   placeholder="备注"  />
								</u-form-item>
							</u--form>
						</div>
					</scroll-view>
				</div>
				<footer-btn @onconfirm="handconfirm" @oncancel="showAddPop = false"  confirm_btn_text="确定"></footer-btn>
		    </div>
		</u-popup>
		
        <footer-btn v-if="[partsPurchaseStatus.draft,partsPurchaseStatus.refuse,null].includes(info.status) || !info.id" :cancelBtnShow="false"  confirm_btn_text="提交"  @onconfirm="save(partsPurchaseStatus.submit)">
			<div @click="save(partsPurchaseStatus.draft)" class="draft_btn btn">保存草稿</div>
		</footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import LoadSelector from '@/components/LoadSelector.vue';
import Selector from '@/components/Selector.vue';
import dayjs from "dayjs";
import {queryDetails,saveEdit}  from  '../../api/partsPurchase'
import {queryListByStaff}  from  '@/api/company'
import {useDateTime}  from  '../../vueUse/datetimeUse'
import { useRoute } from 'uni-mini-router';
import { isArray } from 'lodash';
import {upload,dictTree,asyncUpload}  from  '@/api/common'
import {partsPurchaseStatus} from '../../constant/index.js'
	const prop =  defineProps({
			id: String,
			isComponent: {
				type: Boolean,
				default: false,
			},
			deviceRepairId:{
				type: String,
				default: null,
			}
		})
	const routerQuery = prop;
	const appStore = useAppStore();
	const router = useRouter();
	const route = useRoute();
	const { userInfo,factoryMenuObj } = useUserStore();
    // 相关字典
    const dictStore = useDictStore()
    const companyList = ref([])
	const util = inject("util");
    const deleteFild = ['accName','createdStaffName','updateStaffName','salesLeadName','marketingName','areaName'];
    const requiredFild = [
        // {key:'name',message:'请填写机会名称',required: true,},
        // {key:'amount',message:'请填写金额',required: true,},
        // {key:'type',message:'请选择机会类型',required: true,},
    ]
    const save = (type) => {
        for(let index  in requiredFild){ 
            var item =  requiredFild[index]
            if(item.required && (!info.value[item.key] || info.value[item.key]=='')){
            	return uni.showToast({
            		title: item.message,
            		duration: 1500,
            		icon:'none'
            	});
            }
        };
		if(type)info.value.status = type
		info.value.partsPurchaseDetailsList = info.value.partsPurchaseDetailsList.map(item=>{
		    let {partImg,...detail} = item
		    if(isArray(partImg)) {
		        partImg.forEach((imgInfo,index) => {
		            detail[`partImg${index+1}`] = imgInfo.url
		        });
		    }
		    return detail
		})
        // let postData = {}
        // for(var key in info.value){
        //     if(!deleteFild.includes(key))
        //         postData[key] = info.value[key];
        // }
        saveEdit(info.value).then((res)=>{
            uni.showModal({
            	title: '提示',
            	content: res.msg,
            	showCancel:false,
            	success: function (confirmres) {
            		if (confirmres.confirm) {
            			if(prop.isComponent)emit('confirm', res)
            			else{
            				uni.$emit("refresh-partsPurchase", {refresh: true}); 
            				router.back();
            			}
            		} else if (confirmres.cancel) {
            			console.log('用户点击取消');
            		}
            	}
            });
        })
    }
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const info = ref({
		jobType: factoryMenuObj?.partsPurchase.jobType,
		prodLineOrDept:factoryMenuObj?.partsPurchase.prodLineOrDept,
		deviceRepairId:prop.deviceRepairId,
		deviceRepairName:prop.deviceRepairName,
		partsPurchaseDetailsList:[],
    });
    const init = async() =>{
		await dictStore.getDictBatch(['replace_reason','product_line','handling_method']);
        if(routerQuery.id){
            getInfo()
        }
    }
	const showAddPop = ref(false)
	const popInfo = ref({});
	const deleteRow = (index) => {
		uni.showModal({
			title: '提示',
			content: '确认删除？',
			success: function (res) {
				if (res.confirm) {
					info.value?.partsPurchaseDetailsList.splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
	let showindex = null;
	const popform = ref()
	const handconfirm = async () =>{
	    // debugger
		try {
			await popform.value?.validate();
			console.log('校验通过');
		} catch (err) {
			console.error('校验失败', err);
			return
		}
	    if(showindex || showindex>=0){
	        info.value.partsPurchaseDetailsList[showindex] = popInfo.value;
	    }else{
	        info.value.partsPurchaseDetailsList.push(popInfo.value)
	    }
	    showAddPop.value = false
	    popInfo.value = {};
	}
	const handleShow = (data,index) =>{
	    popInfo.value = data;
	    if(data.partImg){
	        if(!Array.isArray(data.partImg))popInfo.value.partImg = data.partImg.split(';').map(i=>{
				return{url:import.meta.env.VUE_APP_BASE_IMG + i}
			})
	    }else{
	        popInfo.value.partImg = []
	    }
	    showindex = index
	    showAddPop.value = true
	}
	const afterRead = async(event,type) =>{
		let files = [].concat(event.file)
		if(!popInfo.value[type]) popInfo.value[type] = [];
		let fileListLen =  popInfo.value[type]?popInfo.value[type].length:0
		files.map((item) => {
			popInfo.value[type].push({
				...item,
				status: 'uploading',
				message: '上传中'
			})
		})
		for (let i = 0; i < files.length; i++) {
			try {
				const {data} = await asyncUpload(files[i].url)
				let item = popInfo.value[type][fileListLen];
				popInfo.value[type].splice(fileListLen, 1, {
				  ...item,
				  status: 'success',
				  message: '',
				  url: data.url,
				});
				fileListLen++;
			} catch (e) {
				console.log(e)
				uni.showToast({
					title: "图片上传失败",
					icon: "none"
				})
			}
		}
	}
	
	const beforeDelete = ({file,index},type) =>{
		uni.showModal({
			title: '提示',
			content: '是否删除该附件？',
			success: function (res) {
				if (res.confirm) {
					popInfo.value[type].splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
    onMounted(() => {
        init()
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
	@import '../../style/common.scss';
</style>