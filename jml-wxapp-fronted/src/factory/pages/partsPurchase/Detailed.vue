<template>
    <div class="page pd100">
        <div class="card_info">
			<view class="tabInfo"> {{ info.jobType }} - {{ info.prodLineOrDept }} </view>
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        {{ info.name }}
						<!-- <view> <u-icon name="calendar" :label="`${info.replaceDate} ${info.replaceStartTime}/${info.replaceEndTime}`" labelSize="12"></u-icon> </view> -->
                    </div>
					<div v-if="info.status" class="head_status">
						<u-tag size="mini" :bgColor="partsPurchaseStatusInfo[info.status]?.color" :borderColor="partsPurchaseStatusInfo[info.status]?.color" :text="info.approvalStatus"></u-tag>
						<!-- <image  class="van-icon__image" width="40"  mode="widthFix" src="/static/icon/mileage_incomplete.png" /> -->
					</div>
					<u-divider></u-divider>
					<div class="card_head_tit3 mt10 align-items_center">
						<view style="margin-right: 10px;">设备维修单 : </view>
						<u--text  @click="router.push({name: 'deviceRepairInfo',params: {id:info.deviceRepairId}})" :text="info.deviceRepairName">
						</u--text>
					</div>
                </div>
				
            </div>
        </div>
		
		<div class="card_info">
			<div class="card">
				<div class="card_head">
					<div class="card_head_tit2" style="margin-top: 0;">
					   申请零配件采购
					</div>
				</div>
				
				<div class="detail_list">
					<uni-table border stripe emptyText="暂无更多数据">
						<!-- 表头行 -->
						<uni-tr>
							<uni-th width="50" align="center">编号</uni-th>
							<uni-th width="120" align="center">零件名称</uni-th>
							<uni-th width="120" align="center">数量</uni-th>
							<uni-th width="120" align="center">单位</uni-th>
							<uni-th width="80" operate align="center">操作</uni-th>
						</uni-tr>
						<!-- 表格数据行 -->
						<uni-tr v-for="(detail,i) in info.partsPurchaseDetailsList" :key="i">
							<uni-td align="center"> {{i+1}} </uni-td>
							<uni-td align="center"> {{detail.name}}</uni-td>
							<uni-td align="center"> {{detail.qty}}</uni-td>
							<uni-td align="center"> {{detail.unit}}</uni-td>
							<uni-td  operate align="center">
								<view class="uni-group">
									<u-button class="uni-button" @click="handleShow(detail,i)" color="#849EB2" :plain="true" :hairline="true" size="mini" type="primary">查看</u-button>
								</view>
							</uni-td>
						</uni-tr>
					</uni-table>
				</div>
			</div>
		</div>
		
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit2" style="margin-top: 0;">
                       零配件采购申请基本信息
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
					<!-- <div class="item">
					    <div class="left">损坏情况及故障描述</div><div class="rigth">{{info.damageDesc}}</div>
					</div> -->
					
                    <div class="item">
                        <div class="left">备注</div>
                        <div class="rigth">{{info.remark}}</div>
                    </div>
                </div>
				
                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.createdTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.updateTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>
		
		<div v-if="info.historyApprovalList" class="card_info">
		    <div class="card">
		        <div class="card_head">
		            <div class="card_head_tit2" style="margin-top: 0;">
		               审批历史
		            </div>
		        </div>
		        <u-divider></u-divider>
		        <div  class="card_main">
					<u-steps :current="info.historyApprovalList.length" direction="column">
						<u-steps-item v-for="(item,index) in info.historyApprovalList" :key="index" >
							<template #title>
								<view class="u-text__value u-text__value--content">
									{{item.approvalStatus}} | {{item.createdStaffName}} 
								</view>
								
							</template>
							<template #desc>
								<view class="value--content">  
									<text>{{item.createdTime}}</text>
									<view>{{item.remark}}</view>
								</view>
							</template>
						</u-steps-item>
					</u-steps>
		        </div>
				
			</div>
		</div>
		
		
		<!-- 审批 -->
		<u-modal v-model:show="dialogShow" @confirm="dialogConfirm"  @close="dialogShow = false" @cancel="dialogShow = false" confirmColor="#849EB2" title="审批操作" show-cancel-button>
		    <div class="addInfo">
		        <!-- <div class="card"> -->
					<u--form labelPosition="top" labelWidth="auto" >
		            <u-form-item  prop="remark">
		            	<u--textarea :count="!!approvalInfo.remark"   v-model="approvalInfo.remark"  class="int" maxlength="800"  :placeholder="`${approvalInfo.type}审批备注`"  />
		            </u-form-item>
					</u--form>
		        <!-- </div> -->
		    </div>
		</u-modal>
		
		<!-- 底部弹出 -->
		<u-popup :show="showPopInfo" :round="10" closeable mode="bottom" @close="showPopInfo = false">
		    <div class="pop_info">
		        <div class="pop_info_tit">零配件详情</div>
		        <div class="card_info">
		            <div class="card">
		                <div class="card_main">
		                    <div class="item">
		                        <div class="left">零配件名称</div><div class="rigth">{{popinfo.name}}</div>
		                    </div>
		                    <div class="item">
		                        <div class="left">数量</div><div class="rigth">{{popinfo.qty}}</div>
		                    </div>
		                    <div class="item">
		                        <div class="left">单位</div><div class="rigth">{{popinfo.unit}}</div>
		                    </div>
		                    <div class="item">
		                        <div class="left">建议供应商名称</div><div class="rigth">{{popinfo.supplier}}</div>
		                    </div>
		                    <div class="item">
		                        <div class="left">损坏情况及故障描述</div><div class="rigth">{{popinfo.damageDesc}}</div>
		                    </div>
		                    <div class="item">
		                        <div class="left">处理方式</div><div class="rigth">{{popinfo.handlingMethod}}</div>
		                    </div>
		                    <div class="item">
		                        <div class="left">零件照片</div>
								<div v-if="popinfo.partImg && popinfo.partImg.length>0" class="rigth section-item-img">
		                            <image v-for="(item,index) in popinfo.partImg" :key="index" class="image"  @click="showImagePreview(popinfo.partImg,index)"  mode="widthFix"  :src="imgBase+item" />
		                        </div>
		                    </div>
		                    <div class="item">
		                        <div class="left">备注信息</div><div class="rigth">{{popinfo.remark}}</div>
		                    </div>
		                    <div class="item">
		                        <div class="left">创建日期</div><div class="rigth">{{popinfo.createdTime}}</div>
		                    </div>
		                </div>
		            </div>
		        </div>
		    </div>
		</u-popup>
		
		
		<template v-if="!dialogShow">
		<footer-btn v-if="[partsPurchaseStatus.draft,partsPurchaseStatus.refuse,null].includes(info.status)"
			:cancelBtnShow="false"
			:confirmBtnShow="hasFactoryPermission('partsPurchase','U')"
			confirm_btn_text="编辑" 
			@onconfirm="router.push({name: 'partsPurchaseEdit',params: {id:routerQuery.id}})" 
		>
		</footer-btn>
		
        <footer-btn v-else-if="info.btnList && info.approvalStatus!='待提交'"  :cancelBtnShow="false" :confirmBtnShow="false">
			<div  v-for="(item,index) in info.btnList"  :key="index"
				:style="`background: ${partsPurchaseStatusInfo[item.btnCode]?.btnColor};`" 
				@click="hdBtn(item)" :class="item.btnCode" class="confirm_btn btn ">
				<u-icon
					v-if="partsPurchaseStatusInfo[item.btnCode]?.icon"
					class="btn_icon"
				  size="16"
				  color="#fff"
				  :name="partsPurchaseStatusInfo[item.btnCode]?.icon"
				></u-icon>
				<view>{{item.btnName}}</view>
			</div>
		</footer-btn>
		</template>
		<floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import {queryDetails,approval}  from  '../../api/partsPurchase'
import {partsPurchaseStatusInfo,partsPurchaseStatus} from '../../constant/index.js'

    const prop =  defineProps(['id'])
    const router = useRouter();
    const util = inject("util");
	const dictStore = useDictStore()
    const routerQuery =  prop;
    const info = ref({
    });
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const init = async() =>{
        getInfo()
    }
	
	//审批用到
	const approvalInfo = reactive({
		id:routerQuery.id,
		remark:'',
		status:null,
		type:null,
	})
	let isSubmit = false
	const dialogShow = ref(false)
	const dialogConfirm = ()=>{
		if(isSubmit) return false
	   
	    if((!approvalInfo.remark || approvalInfo.remark=='')) return uni.showToast({
			title: '备注必须填写',
			duration: 1500,
			icon:'none'
		});
		submit()
	}
	
	const hdBtn = (data) =>{
		if(data.btnCode==partsPurchaseStatus.submit){
			submit(data.btnCode)
		}else{
			approvalInfo.status = data.btnCode;
			approvalInfo.type = data.btnName;
			dialogShow.value = true
		}
	}
	
	const submit =(status)=>{
		let post = {...approvalInfo}
		if(status) post.status = status
		debugger
		approval(post).then(res=>{
			uni.showModal({
				title: '提示',
				content: res.msg,
				showCancel:false,
				success: function (confirmres) {
					init()
					if (confirmres.confirm) {
						if(prop.isComponent)emit('confirm', res)
						else{
							uni.$emit("refresh-partsPurchase", {refresh: true}); 
							router.back();
						}
					} else if (confirmres.cancel) {
						console.log('用户点击取消');
					}
				}
			});
		}).catch(e=>{
			isSubmit = false
		})
	}
	
	const showPopInfo = ref(false)
	const popinfo = ref({})
	const handleShow = async(info,index) =>{
	    popinfo.value = info;
	    if(info.partImg && !Array.isArray(info.partImg)){
	        popinfo.value.partImg = info.partImg.split(';')
	    }else{
	        popinfo.value.partImg = []
	    }
	    showPopInfo.value = true
	}
	
	onShow(() => {
		 init()
	})
	onMounted(() => {
	   
	})
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
	@import '@/styles/factory.scss';
	@import '../../style/common.scss';
	.card_info{
		.card{
			.item{
				padding-bottom: 5px;
				.left{
					line-height: 1.2;
				}
				.rigth{
					text-align: right;
				}
			}
		}
	}
	.addInfo{
		padding:0 10px;
		position: relative;
		border-radius: var(--card-border-radius);
		line-height: 1.4;
		width: 100%;
		.card{
			background: none;
			padding: var(--card-padding);
			position: relative;
			border-radius: var(--card-border-radius);
			line-height: 1.4;
			.card_head{
				display: flex;
				align-items: center;
				justify-content: center;
				// border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
				padding: 15px 0 10px 0;
				&_tit{
					display: flex;
					align-items: center;
					font-size: 18px;
					font-weight: 600;
					color: #333333;
					span{
						font-weight: 700;
						font-size: 16px;
						color: #000000;
						margin-right: 5px;
					}
				}
			}
		}
		:deep(){
			.int{
				background: none !important;
				.van-field__value{
					background: #fff;
				}
				// &.van-cell:after{
				//     content: none;
				// }
			}
		}
	}
	
	.pop_info{
		.card_info{
			max-height: calc(90vh - 100px);
			overflow-y: scroll;
		}
		.section-item-img{
			image,.image{
				max-width: 80px;
				max-width: 80px;
			}
		}
	}
</style>