<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                       {{ info.name }}
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
					<div class="item">
					    <div class="left">上一次天然气示数</div><div class="rigth">{{info.beforeReading}}</div>
					</div>
					<div class="item">
					    <div class="left">今日天然气示数</div><div class="rigth">{{info.dailyReading}}</div>
					</div>
                    <div class="item">
                        <div class="left">今日示数照片</div>
						<div v-if="info.naturalGasImg" class="rigth">
							<image v-for="(item,index) in info.naturalGasImg.split(';')" :key="index"   mode="widthFix"  :src="imgBase+item" />
						</div>
                    </div>

                    <div class="item">
                        <div class="left">上一次耗用量</div><div class="rigth">{{info.beforeUsage}} （单位：m³）</div>
                    </div>
					<div class="item">
					    <div class="left">备注</div>
					    <div class="rigth">{{info.remark}}</div>
					</div>
                </div>

                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.createdTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.updateTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>

        <footer-btn v-if="info.isEdit=='1' && hasFactoryPermission('naturalGasLog','U')"  :cancelBtnShow="false" @onconfirm="router.push({name: 'naturalGasLogEdit',params: {id:routerQuery.id}})" confirm_btn_text="编辑"></footer-btn>
		<floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import {queryDetails}  from  '../../api/naturalGasLog'

    const prop =  defineProps(['id'])
    const router = useRouter();
    const util = inject("util");
	const dictStore = useDictStore()
    const routerQuery =  prop;
    const info = ref({
    });
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const init = async() =>{
        getInfo()
    }

	onShow(() => {
		 init()
	})
	onMounted(() => {
	   
	})
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
	@import '@/styles/factory.scss';
	.card_info{
		.card{
			.item{
				padding-bottom: 5px;
				.left{
					line-height: 1.2;
                    width: 150px;
				}
				.rigth{
					text-align: right;
				}
			}
			
			.head_status{
				background: #999 !important;
			}
		}
	}
	
</style>