<template>
    <div class="page pd100">
        <div class="card_info">
			<u--form labelPosition="top" labelWidth="auto" ref="form1" >
            <div class="card">
                <!-- <div class="card_head">
                    <div class="card_head_tit">氮气登记表</div>
                </div> -->
					<view  class="u-form-item__body__left__content" style="font-size: 15px; padding-top: 10px;">
						<uni-text class="u-form-item__body__left__content__label">
							<span>上一次天然气示数：</span>
							{{ info.beforeReading }}
							
						</uni-text>
					</view>
					<u-form-item label="今日天然气示数" required prop="dailyReading">
						<u--input v-model.number="info.dailyReading" type="number" class="int"   placeholder="请输入"  />
					</u-form-item>
					<u-form-item label="今日天然气示数照片" required prop="naturalGasImg" >
						<u-upload
						    :max-count="1"
						    @after-read="afterRead($event,'naturalGasImg')"
						    :deletable="true"
							:sizeType="['compressed']"
						    :before-delete="(file,detail)=>beforeDelete(file,detail)"
							@delete="beforeDelete($event,'naturalGasImg')"
							:fileList="info.naturalGasImg"
						>
						</u-upload>
					</u-form-item>

						<view class="u-form-item__body__left__content" style="font-size: 15px; margin-top: 10px;">
							<uni-text class="u-form-item__body__left__content__label">
								<span>上一次天然气耗用量（单位：m³）:</span>
								{{ info.beforeUsage }}
							</uni-text>
						</view>
					<!-- </template> -->
					<u-form-item label="备注" prop="remark" >
						<u--textarea :count="!!info.remark" maxlength="800"  v-model="info.remark"  class="int"   placeholder="备注"  />
					</u-form-item>
					
				</div>
			</u--form>
        </div>
		<u-datetime-picker
			:show="dateTimeShow"
			v-model="currentTime"
			confirmColor="#849EB2"
			closeOnClickOverlay
			@confirm="onDateTimeConfirm"
			@cancel="dateTimeShow = false"
			@close="dateTimeShow = false"
			:mode="dateMode"
		></u-datetime-picker>
        <footer-btn :cancelBtnShow="false"  confirm_btn_text="保存" @onconfirm="save()"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>

<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject, watch} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import {queryDetails,saveEdit,queryBeforeLiquid}  from  '../../api/naturalGasLog'
import {queryListByStaff}  from  '@/api/company'
import {useDateTime}  from  '../../vueUse/datetimeUse'
import {upload,asyncUpload}  from  '@/api/common'
import { isArray, map } from 'lodash';
	const prop =  defineProps({
			id: String,
			isComponent: {
				type: Boolean,
				default: false,
			},
			accountId:{
				type: String,
				default: null,
			}
		})
	const routerQuery = prop;
	const appStore = useAppStore();
	const router = useRouter();
	const { userInfo } = useUserStore();
	
    // 相关字典
    const dictStore = useDictStore()
    const companyList = ref([])
	const util = inject("util");
    const deleteFild = ['accName','createdStaffName','updateStaffName','salesLeadName','marketingName','areaName'];
    const requiredFild = [
        // {key:'name',message:'请填写机会名称',required: true,},
        // {key:'amount',message:'请填写金额',required: true,},
        // {key:'type',message:'请选择机会类型',required: true,},
    ]
    const save = () => {
        for(let index  in requiredFild){ 
            var item =  requiredFild[index]
            if(item.required && (!info.value[item.key] || info.value[item.key]=='')){
            	return uni.showToast({
            		title: item.message,
            		duration: 1500,
            		icon:'none'
            	});
            }
        };
        // let postData = {}
        // for(var key in info.value){
        //     if(!deleteFild.includes(key))
        //         postData[key] = info.value[key];
        // }
		if(isArray(info.value.naturalGasImg)) {
		    info.value.naturalGasImg.forEach((item,index) => {
		        info.value[`naturalGasImg${index+1}`] = item.url
		    });
		}
        saveEdit(info.value).then((res)=>{
            uni.showModal({
            	title: '提示',
            	content: info.value.id ?'修改成功':'保存成功',
            	showCancel:false,
            	success: function (confirmres) {
            		if (confirmres.confirm) {
            			if(prop.isComponent)emit('confirm', res)
            			else{
            				uni.$emit("refresh-naturalGasLog", {refresh: true}); 
            				router.back();
            			}
            		} else if (confirmres.cancel) {
            			// uni.$emit("refresh", {refresh: true}); 
            			console.log('用户点击取消');
            		}
            	}
            });
        })
    }
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
			if(res.data.naturalGasImg){
			    info.value.naturalGasImg = res.data.naturalGasImg.split(';').map(i=>{
					return{url:import.meta.env.VUE_APP_BASE_IMG + i}
				})
			}else{
			    info.value.naturalGasImg = []
			}
			
        }).catch((err) => {
        });
    }
    const info = ref({
		beforeUsage:'',
    });
	// const preInfo = ref({})
	// watch(()=>info.value.dailyUsageCm,(newv,oldv)=>{
	// 	if(newv) {
	// 		info.value.dailyUsageKg = parseFloat((newv * 47.297).toFixed(4))
	// 	}
	// },{
	// 	deep:false,
	// 	immediate:false,
	// })
	watch(()=>info.value.dailyReading,(newv,oldv)=>{
		if(newv) {
			// let diff = newv - (info.value.beforeReading??0);
			let beforeUsage = info.value.beforeReading ? newv - info.value.beforeReading : 0 ;
			info.value.beforeUsage = Number(beforeUsage.toFixed(2));
		}
	},{
		deep:false,
		immediate:false,
	})
	const {dateTimeShow,field,currentTime,onDateTimeShow,onDateTimeConfirm,dateMode} = useDateTime(info)
    const init = async() =>{
		await dictStore.getDictBatch(['replace_reason','product_line']);
        if(routerQuery.id){
            getInfo()
        }else{
			let {data} =  await queryBeforeLiquid()
			info.value.beforeReading = data?.beforeReading?? 0
		}
    }
	const afterRead = async(event,type) =>{
		let files = [].concat(event.file)
		if(!info.value[type]) info.value[type] = [];
		let fileListLen =  info.value[type].length
		files.map((item) => {
			info.value[type].push({
				...item,
				status: 'uploading',
				message: '上传中'
			})
		})
		for (let i = 0; i < files.length; i++) {
			try {
				const {data} = await asyncUpload(files[i].url)
				let item = info.value[type][fileListLen];
				info.value[type].splice(fileListLen, 1, {
				  ...item,
				  status: 'success',
				  message: '',
				  url: data.url,
				});
				fileListLen++;
			} catch (e) {
				console.log(e)
				uni.showToast({
					title: "图片上传失败",
					icon: "none"
				})
			}
		}
	}
	
	const beforeDelete = ({file,index},type) =>{
		uni.showModal({
			title: '提示',
			content: '是否删除该附件？',
			success: function (res) {
				if (res.confirm) {
					info.value[type].splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
	
    onMounted(() => {
        init()
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
	:deep(.card_info){
		.u-form-item__body{
			padding: 15px 0 0 0;
		}
	}
</style>