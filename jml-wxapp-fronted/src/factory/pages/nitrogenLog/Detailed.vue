<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                       {{ info.name }}
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
					
					<div class="item">
					    <div class="left">上一次液位示数</div><div class="rigth">{{info.beforeLiquidLevel}}</div>
					</div>
					<div class="item">
					    <div class="left">今日液位示数</div><div class="rigth">{{info.liquidLevel}}</div>
					</div>
                    <div class="item">
                        <div class="left">今日液位示数照片</div>
						<div v-if="info.liquidLevelImg" class="rigth">
							<image v-for="(item,index) in info.liquidLevelImg.split(';')" :key="index"   mode="widthFix"  :src="imgBase+item" />
						</div>
                    </div>
                    <div class="item">
                        <div class="left">氮气充值量(单付cmH20)</div><div class="rigth">{{info.nitrogenAmount}} cmH2O</div>
                    </div>
                    <div class="item">
                        <div class="left">上一次液位耗用量(单付cmH20)</div><div class="rigth">{{info.beforeUsageCm}} cmH2O</div>
                    </div>
                    <div class="item">
                        <div class="left">上一次液位耗用量(单位KG)</div><div class="rigth">{{info.beforeUsageKg}} KG</div>
                    </div>
					<div class="item">
					    <div class="left">备注</div>
					    <div class="rigth">{{info.remark}}</div>
					</div>
                   
                </div>
				
				
                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.createdTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.updateTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>

        <footer-btn v-if="info.isEdit=='1' && hasFactoryPermission('nitrogenLog','U')"  :cancelBtnShow="false" @onconfirm="router.push({name: 'nitrogenLogEdit',params: {id:routerQuery.id}})" confirm_btn_text="编辑"></footer-btn>
		<floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import {queryDetails}  from  '../../api/nitrogenLog'

    const prop =  defineProps(['id'])
    const router = useRouter();
    const util = inject("util");
	const dictStore = useDictStore()
    const routerQuery =  prop;
    const info = ref({
    });
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const init = async() =>{
        getInfo()
    }

	onShow(() => {
		 init()
	})
	onMounted(() => {
	   
	})
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
	@import '@/styles/factory.scss';
	.card_info{
		.card{
			.item{
				padding-bottom: 5px;
				.left{
					line-height: 1.2;
				}
				.rigth{
					text-align: right;
				}
			}
			
			.head_status{
				background: #999 !important;
			}
		}
	}
	
</style>