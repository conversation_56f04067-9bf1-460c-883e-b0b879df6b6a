<template>
    <div class="page pd100">
        <div class="card_info">
			<u--form labelPosition="top" labelWidth="auto" ref="form1" >
            <div class="card">
                <!-- <div class="card_head">
                    <div class="card_head_tit">氮气登记表</div>
                </div> -->
					<view  class="u-form-item__body__left__content" style="font-size: 15px; padding-top: 10px;">
						<uni-text class="u-form-item__body__left__content__label">
							<span>上一次液位示数（单位cmH2O）:</span>
							{{ info.beforeLiquidLevel }}
							
						</uni-text>
					</view>
					<u-form-item label="今日液位示数" required prop="liquidLevel">
						<u--input v-model.number="info.liquidLevel" type="number" class="int"   placeholder="请输入"  />
					</u-form-item>
					<u-form-item label="今日液位示数照片" required prop="liquidLevelImg" >
						<u-upload
						    :max-count="1"
						    @after-read="afterRead($event,'liquidLevelImg')"
						    :deletable="true"
							:sizeType="['compressed']"
						    :before-delete="(file,detail)=>beforeDelete(file,detail)"
							@delete="beforeDelete($event,'liquidLevelImg')"
							:fileList="info.liquidLevelImg"
						>
						</u-upload>
					</u-form-item>
                    <u-form-item label="氮气充气值" prop="nitrogenAmount">
						<u--input  type="number" v-model.number="info.nitrogenAmount"  class="int"   placeholder="请输入"  />
					</u-form-item>
					<!-- <u-form-item label="当天耗用量（单位：cmH2O）" prop="dailyUsageCm" required>
						<u--input  type="number" v-model.number="info.dailyUsageCm"  class="int"   placeholder="请输入"  />
					</u-form-item>
					<view class="u-tips-color mt10">
						1cmH2O*47.51 = 47.51kg
					</view>
					<view class="u-form-item__body__left__content" style="font-size: 15px; margin-top: 10px;">
						<uni-text class="u-form-item__body__left__content__label">
							<span>当天耗用量（单位：Kg）:</span>
							{{ info.dailyUsageKg }}
						</uni-text>
					</view> -->
					<!-- <template> -->
						<view class="u-form-item__body__left__content" style="font-size: 15px; margin-top: 10px;">
							<uni-text class="u-form-item__body__left__content__label">
								<span>上一次液位耗用量（单位：cmH2O）:</span>
								{{ info.beforeUsageCm }}
							</uni-text>
						</view>
						<view class="u-form-item__body__left__content" style="font-size: 15px; margin-top: 10px;">
							<uni-text class="u-form-item__body__left__content__label">
								<span>上一次液位耗用量（单位：kg）:</span>
								{{ info.beforeUsageKg }}
							</uni-text>
						</view>
					<!-- </template> -->
					<u-form-item label="备注" prop="remark" >
						<u--textarea :count="!!info.remark" maxlength="800"  v-model="info.remark"  class="int"   placeholder="备注"  />
					</u-form-item>
					
				</div>
			</u--form>
        </div>
		<u-datetime-picker
			:show="dateTimeShow"
			v-model="currentTime"
			confirmColor="#849EB2"
			closeOnClickOverlay
			@confirm="onDateTimeConfirm"
			@cancel="dateTimeShow = false"
			@close="dateTimeShow = false"
			:mode="dateMode"
		></u-datetime-picker>
        <footer-btn :cancelBtnShow="false"  confirm_btn_text="保存" @onconfirm="save()"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject, watch} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import {queryDetails,saveEdit,queryBeforeLiquid}  from  '../../api/nitrogenLog'
import {useDateTime}  from  '../../vueUse/datetimeUse'
import {upload,asyncUpload}  from  '@/api/common'
import { isArray, map } from 'lodash';
	const prop =  defineProps({
			id: String,
			isComponent: {
				type: Boolean,
				default: false,
			},
			accountId:{
				type: String,
				default: null,
			}
		})
	const routerQuery = prop;
	const appStore = useAppStore();
	const router = useRouter();
	const { userInfo } = useUserStore();
	
    // 相关字典
    const dictStore = useDictStore()
    const companyList = ref([])
	const util = inject("util");
    const deleteFild = ['accName','createdStaffName','updateStaffName','salesLeadName','marketingName','areaName'];
    const requiredFild = [
        // {key:'name',message:'请填写机会名称',required: true,},
        // {key:'amount',message:'请填写金额',required: true,},
        // {key:'type',message:'请选择机会类型',required: true,},
    ]
    const save = () => {
        for(let index  in requiredFild){ 
            var item =  requiredFild[index]
            if(item.required && (!info.value[item.key] || info.value[item.key]=='')){
            	return uni.showToast({
            		title: item.message,
            		duration: 1500,
            		icon:'none'
            	});
            }
        };
        // let postData = {}
        // for(var key in info.value){
        //     if(!deleteFild.includes(key))
        //         postData[key] = info.value[key];
        // }
		if(isArray(info.value.liquidLevelImg)) {
		    info.value.liquidLevelImg.forEach((item,index) => {
		        info.value[`liquidLevelImg${index+1}`] = item.url
		    });
		}
        saveEdit(info.value).then((res)=>{
            uni.showModal({
            	title: '提示',
            	content: info.value.id ?'修改成功':'保存成功',
            	showCancel:false,
            	success: function (confirmres) {
            		if (confirmres.confirm) {
            			if(prop.isComponent)emit('confirm', res)
            			else{
            				uni.$emit("refresh-nitrogenLog", {refresh: true}); 
            				router.back();
            			}
            		} else if (confirmres.cancel) {
            			// uni.$emit("refresh", {refresh: true}); 
            			console.log('用户点击取消');
            		}
            	}
            });
        })
    }
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
			if(res.data.liquidLevelImg){
			    info.value.liquidLevelImg = res.data.liquidLevelImg.split(';').map(i=>{
					return{url:import.meta.env.VUE_APP_BASE_IMG + i}
				})
			}else{
			    info.value.liquidLevelImg = []
			}
			
        }).catch((err) => {
        });
    }
    const info = ref({
		dailyUsageCm:'',
    });
	// const preInfo = ref({})
	// watch(()=>info.value.dailyUsageCm,(newv,oldv)=>{
	// 	if(newv) {
	// 		info.value.dailyUsageKg = parseFloat((newv * 47.297).toFixed(4))
	// 	}
	// },{
	// 	deep:false,
	// 	immediate:false,
	// })
	watch(()=>info.value.liquidLevel,(newv,oldv)=>{
		if(newv) {
			let diff = info.value.beforeLiquidLevel> 0 ? info.value.beforeLiquidLevel - newv : 0;
			info.value.beforeUsageCm = diff;
			info.value.beforeUsageKg = parseFloat((diff * 47.297).toFixed(4))
		}
	},{
		deep:false,
		immediate:false,
	})
	const {dateTimeShow,field,currentTime,onDateTimeShow,onDateTimeConfirm,dateMode} = useDateTime(info)
    const init = async() =>{
		await dictStore.getDictBatch(['replace_reason','product_line']);
        if(routerQuery.id){
            getInfo()
        }else{
			let {data} =  await queryBeforeLiquid()
			info.value.beforeLiquidLevel = data?.beforeLiquidLevel?? 0
		}
    }
	const afterRead = async(event,type) =>{
		let files = [].concat(event.file)
		if(!info.value[type]) info.value[type] = [];
		let fileListLen =  info.value[type].length
		files.map((item) => {
			info.value[type].push({
				...item,
				status: 'uploading',
				message: '上传中'
			})
		})
		for (let i = 0; i < files.length; i++) {
			try {
				const {data} = await asyncUpload(files[i].url)
				let item = info.value[type][fileListLen];
				info.value[type].splice(fileListLen, 1, {
				  ...item,
				  status: 'success',
				  message: '',
				  url: data.url,
				});
				fileListLen++;
			} catch (e) {
				console.log(e)
				uni.showToast({
					title: "图片上传失败",
					icon: "none"
				})
			}
		}
	}
	
	const beforeDelete = ({file,index},type) =>{
		uni.showModal({
			title: '提示',
			content: '是否删除该附件？',
			success: function (res) {
				if (res.confirm) {
					info.value[type].splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
	
    onMounted(() => {
        init()
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
	:deep(.card_info){
		.u-form-item__body{
			padding: 15px 0 0 0;
		}
	}
</style>