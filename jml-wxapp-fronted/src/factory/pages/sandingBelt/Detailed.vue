<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        {{ info.name }}
						<view> <u-icon name="calendar" :label="`${info.replaceDate} ${info.replaceStartTime}/${info.replaceEndTime}`" labelSize="12"></u-icon> </view>
                    </div>
                </div>
            </div>
        </div>
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                       基本信息
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
					<div class="item">
					    <div class="left">机器编号</div><div class="rigth">{{info.machineNo}}</div>
					</div>
					<div class="item">
					    <div class="left">所属线体</div><div class="rigth">{{info.productLine}}</div>
					</div>
					<!-- <div class="item">
					    <div class="left">日期</div><div class="rigth">{{info.replaceDate}}</div>
					</div>
					<div class="item">
					    <div class="left">更换时间</div><div class="rigth">{{info.replaceStartTime}} - {{info.replaceEndTime}} </div>
					</div> -->
					<div class="item">
					    <div class="left">砂纸序号</div><div class="rigth">{{info.sandpaperSn}}</div>
					</div>
					<div class="item">
					    <div class="left">砂纸型号</div><div class="rigth">{{info.sandpaperModel}}</div>
					</div>
					<div class="item">
					    <div class="left">砂光数量</div><div class="rigth">{{info.sandingQty}}</div>
					</div>
                    <div class="item">
                        <div class="left">更换次数</div><div class="rigth">{{info.replaceCnt}}</div>
                    </div>
                    <div class="item">
                        <div class="left">更换原因</div><div class="rigth">{{info.replaceReason}}</div>
                    </div>
                    <div class="item">
                        <div class="left">原因说明</div><div class="rigth">{{info.reasonDesc}}</div>
                    </div>
                   
                    <div class="item">
                        <div class="left">备注</div>
                        <div class="rigth">{{info.remark}}</div>
                    </div>
                </div>

                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.createdTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.updateTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>

        <footer-btn v-if="hasFactoryPermission('sandingBelt','U')"  :cancelBtnShow="false" @onconfirm="router.push({name: 'sandingBeltEdit',params: {id:routerQuery.id}})" confirm_btn_text="编辑"></footer-btn>
		<floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import {queryDetails}  from  '../../api/sandingBelt'

    const prop =  defineProps(['id'])
    const router = useRouter();
    const util = inject("util");
	const dictStore = useDictStore()
    const routerQuery =  prop;
    const info = ref({
    });
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const init = async() =>{
        getInfo()
    }

	onShow(() => {
		 init()
	})
	onMounted(() => {
	   
	})
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
	@import '@/styles/factory.scss';
	.card_info{
		.card{
			.item{
				padding-bottom: 5px;
				.left{
					line-height: 1.2;
				}
				.rigth{
					text-align: right;
				}
			}
			
			.head_status{
				background: #999 !important;
			}
		}
	}
	
</style>