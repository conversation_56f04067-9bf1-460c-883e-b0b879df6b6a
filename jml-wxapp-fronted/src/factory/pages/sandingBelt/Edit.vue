<template>
    <div class="page pd100">
        <div class="card_info">
			<u--form labelPosition="top" labelWidth="auto" ref="form1" >
            <div class="card">
                <!-- <div class="card_head">
                    <div class="card_head_tit">砂带更换记录</div>
                </div> -->
					
					<u-form-item label="所属线体" required prop="productLine">
						<zxz-uni-data-select  placeholder="请选择" v-model="info.productLine" dataText="dval" dataValue="dname" :localdata="dict['product_line']"></zxz-uni-data-select>
					</u-form-item>
					<u-form-item label="机器编号" required prop="MachineNo">
						<!-- <u--input v-model="info.machineNo" class="int"   placeholder="请输入"  /> -->
						<zxz-uni-data-select  placeholder="请选择" v-model="info.machineNo" dataText="dval" dataValue="dname" :localdata="dict['machine_no']"></zxz-uni-data-select>
					</u-form-item>
					<u-form-item required label="日期"  prop="replaceDate">
						<u--input v-model="info.replaceDate"  class="int" readonly  placeholder="日期"  />
						<template #right><u-icon name="calendar" size="22" @click="onDateTimeShow('replaceDate')"></u-icon></template>
					</u-form-item>
					<u-form-item required  label="更换时间" prop="replaceStartTime">
						<view style="display: flex; width: 100%;">
							<view class="start int-time" style="flex: 1;">
								<u--input inputAlign="left" v-model="info.replaceStartTime"  class="int" readonly  placeholder="--:--"  >
									<template #suffix>
										<u-icon name="clock" size="20" @click="onRangeTimeShow('replaceStartTime','replaceEndTime')"></u-icon>
									</template>
								</u--input>
							</view>
							<u-icon name="minus"  size="22"></u-icon>
							
							<view class="end int-time" style="flex: 1;">
								<u--input inputAlign="right" v-model="info.replaceEndTime"  class="int" readonly  placeholder="--:--"  >
									<template #suffix>
										<u-icon name="clock" size="20" @click="onRangeTimeShow('replaceStartTime','replaceEndTime')"></u-icon>
									</template>
								</u--input>
							</view>
							
						</view>
					</u-form-item>

					<u-form-item label="砂纸序号" prop="sandpaperSn" required>
						<u--input  v-model="info.sandpaperSn"  class="int"   placeholder="请输入"  />
					</u-form-item>
					<u-form-item label="砂纸型号" required prop="sandpaperModel">
						<u--input  v-model="info.sandpaperModel"  class="int"   placeholder="请输入"  />
					</u-form-item>
					<!-- <u-form-item label="市场活动" required prop="marketing_id">
						<load-selector v-model="info.marketing_id" value-key="id"  value-fild="id"   load_url="/api/marketing/queryListByStaff"></load-selector>
					</u-form-item> -->
					<u-form-item label="砂光数量" required prop="sandingQty">
						<u--input type="number"  v-model="info.sandingQty"  class="int"   placeholder="请输入"  />
					</u-form-item>
					<u-form-item label="更换次数" prop="replaceCnt" required>
						<u--input type="number" v-model="info.replaceCnt" class="int"   placeholder="请输入"  />
					</u-form-item>

					<u-form-item label="更换原因" required prop="replaceReason">
						<zxz-uni-data-select  placeholder="请选择" v-model="info.replaceReason" dataText="dval" dataValue="dname" :localdata="dict['replace_reason']"></zxz-uni-data-select>
					</u-form-item>
					<!-- v-if="info.replaceReason=='其他'" -->
					<u-form-item  label="原因说明" required prop="reasonDesc">
						<u--textarea :count="!!info.reasonDesc" maxlength="200"  v-model="info.reasonDesc"  class="int"   placeholder="请输入"  />
					</u-form-item>
					
					<u-form-item label="备注" prop="remark" >
						<u--textarea :count="!!info.remark" maxlength="800"  v-model="info.remark"  class="int"   placeholder="备注"  />
					</u-form-item>
					
				</div>
			</u--form>
        </div>
		<hbxw-time-range-picker
			v-model:visible="rangeTimeShow"
			@cancel="rangeTimeShow= false"
			isShowType
			:isShowSecond='false'
			:value="rangeTime" 
			delimiter="point"
			@sure="onRangeTimeConfirm"
		/>
		<u-datetime-picker
			:show="dateTimeShow"
			v-model="currentTime"
			confirmColor="#849EB2"
			closeOnClickOverlay
			@confirm="onDateTimeConfirm"
			@cancel="dateTimeShow = false"
			@close="dateTimeShow = false"
			:mode="dateMode"
		></u-datetime-picker>
        <footer-btn :cancelBtnShow="false"  confirm_btn_text="保存" @onconfirm="save()"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import LoadSelector from '@/components/LoadSelector.vue';
import Selector from '@/components/Selector.vue';
import dayjs from "dayjs";
import {queryDetails,saveEdit}  from  '../../api/sandingBelt'
import {queryListByStaff}  from  '@/api/company'
import {useDateTime}  from  '../../vueUse/datetimeUse'
import {useRangeTime}  from  '../../vueUse/rangeTimeUse'
	const prop =  defineProps({
			id: String,
			isComponent: {
				type: Boolean,
				default: false,
			},
			accountId:{
				type: String,
				default: null,
			}
		})
	const routerQuery = prop;
	const appStore = useAppStore();
	const router = useRouter();
	const { userInfo } = useUserStore();

    // 相关字典
    const dictStore = useDictStore()
    const companyList = ref([])
	const util = inject("util");
    const deleteFild = ['accName','createdStaffName','updateStaffName','salesLeadName','marketingName','areaName'];
    const requiredFild = [
        // {key:'name',message:'请填写机会名称',required: true,},
        // {key:'amount',message:'请填写金额',required: true,},
        // {key:'type',message:'请选择机会类型',required: true,},
    ]
    const save = () => {
        for(let index  in requiredFild){ 
            var item =  requiredFild[index]
            if(item.required && (!info.value[item.key] || info.value[item.key]=='')){
            	return uni.showToast({
            		title: item.message,
            		duration: 1500,
            		icon:'none'
            	});
            }
        };
        // let postData = {}
        // for(var key in info.value){
        //     if(!deleteFild.includes(key))
        //         postData[key] = info.value[key];
        // }
        saveEdit(info.value).then((res)=>{
            uni.showModal({
            	title: '提示',
            	content: info.value.id ?'修改成功':'保存成功',
            	showCancel:false,
            	success: function (confirmres) {
            		if (confirmres.confirm) {
            			if(prop.isComponent)emit('confirm', res)
            			else{
            				uni.$emit("refresh-sandingBelt", {refresh: true}); 
            				router.back();
            			}
            		} else if (confirmres.cancel) {
            			console.log('用户点击取消');
            		}
            	}
            });
        })
    }
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const info = ref({
    });
	const {dateTimeShow,field,currentTime,onDateTimeShow,onDateTimeConfirm,dateMode} = useDateTime(info)
	const {rangeTime,rangeTimeShow,onRangeTimeShow,onRangeTimeConfirm}  = useRangeTime(info)
    const init = async() =>{
		await dictStore.getDictBatch(['replace_reason','product_line','machine_no']);
        if(routerQuery.id){
            getInfo()
        }
    }
    onMounted(() => {
        init()
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
	:deep(.card_info){
		.u-form-item__body{
			padding: 15px 0 0 0;
		}
	}
</style>