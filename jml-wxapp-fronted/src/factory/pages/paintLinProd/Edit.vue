<template>
    <div class="page pd100">
        <div class="card_info">
			<u--form labelPosition="top" labelWidth="auto" ref="form1" >
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">烤漆线-生产工艺单</div>
                </div>
				
					<!-- <u-form-item label="地区" prop="district" required>
						<zxz-uni-data-select clearable placeholder="请选择"  v-model="info.district" dataText="district" @change="areaChange" dataValue="district"  :localdata="userInfo.staffAreaList"></zxz-uni-data-select>
					</u-form-item> -->
					<u-form-item label="生产工单号" prop="prodOrderNo" required>
						<scan-code-input  v-model="info.prodOrderNo" />
						<!-- <u--input  v-model="info.prodOrderNo"  class="int"   placeholder="请输入"  /> -->
					</u-form-item>
					<u-form-item label="型号" required prop="model">
						<u--input  v-model="info.model"  class="int"   placeholder="请输入"  />
					</u-form-item>
					<!-- <u-form-item label="市场活动" required prop="marketing_id">
						<load-selector v-model="info.marketing_id" value-key="id"  value-fild="id"   load_url="/api/marketing/queryListByStaff"></load-selector>
					</u-form-item> -->
					<u-form-item label="进料板温" required prop="materialTemp">
						<u--input type="number"  v-model="info.materialTemp"  class="int"   placeholder="请输入"  />
					</u-form-item>
					<u-form-item label="线速" prop="lineSpeed" required>
						<u--input type="number" v-model="info.lineSpeed" class="int"   placeholder="请输入"  />
					</u-form-item>
					<view class="u-tips-color">浅色:10-12m/min;深色15m/min</view>
					<u-form-item label="附着底涂固化" required prop="baseCoatCuring">
						<u--input type="number" v-model="info.baseCoatCuring" class="int"   placeholder="请输入"  />
					</u-form-item>
					<view class="u-tips-color">
						能量开度:80%+80%+80%<br>
						UVB.450-550mj/cm²；UVV.800-800mj/cm²
					</view>
					<u-form-item label="附着底涂布量H-8022(JML)  UV辊涂附着底漆" required prop="baseCoatAmount8022UvSpecial">
						<u--input type="number" v-model="info.baseCoatAmount8022UvSpecial" class="int"   placeholder="请输入"  />
					</u-form-item>
					<view class="u-tips-color">
						15-20g/m
					</view>
					<u-form-item label="附着底涂UV能量" required prop="baseCoatUvEnergy">
						<u--input type="number" v-model="info.baseCoatUvEnergy" class="int"   placeholder="请输入"  />
					</u-form-item>
					<view class="u-tips-color">
						能量开度:50%+50%+50%<br>
						UVB.350-450mj/cm²；UVV.700-800mj/cm²
					</view>
					<u-form-item label="砂光底涂量_H-8123(JML)  UV辊涂附着底漆" required prop="sandBaseCoatAmount8123Uv">
						<u--input type="number" v-model="info.sandBaseCoatAmount8123Uv" class="int"   placeholder="请输入"  />
					</u-form-item>
					<view class="u-tips-color">
						40-45g/m²
					</view>
					<u-form-item label="砂光底涂UV能量" required prop="sandBaseCoatUvEnergy">
						<u--input type="number" v-model="info.sandBaseCoatUvEnergy" class="int"   placeholder="请输入"  />
					</u-form-item>
					<view class="u-tips-color">
						双汞550+50mj/cm²;50%、60%<br>
						UVB:200-300mj/cm²
					</view>
					<u-form-item label="面漆涂布量H-82519(JML)  UV镭射肤感清面漆" required prop="topCoatAmount8215UvClear">
						<u--input type="number" v-model="info.topCoatAmount8215UvClear" class="int"   placeholder="请输入"  />
					</u-form-item>
					<view class="u-tips-color">
						40-45g/m²
					</view>
					<u-form-item label="面漆预固化能量(单镓)" required prop="topCoatPrecureEnergy">
						<u--input type="number" v-model="info.topCoatPrecureEnergy" class="int"   placeholder="请输入"  />
					</u-form-item>
					<view class="u-tips-color">
						300-400mj/cm²;75%
					</view>
					<u-form-item label="氮气固化含氧量" required prop="airPressureContent">
						<u--input type="number" v-model="info.airPressureContent" class="int"   placeholder="请输入"  />
					</u-form-item>
					<view class="u-tips-color">
						500Ppm 以下
					</view>
					<u-form-item label="准分子灯能量" required prop="monomerLampEnergy">
						<u--input type="number" v-model="info.monomerLampEnergy" class="int"   placeholder="请输入"  />
					</u-form-item>
					<view class="u-tips-color">
						75%
					</view>
					<u-form-item label="面漆全国能量(双汞+单镓)" required prop="topCoatNationalEnergyDualS">
						<u--input type="number" v-model="info.topCoatNationalEnergyDualS" class="int"   placeholder="请输入"  />
					</u-form-item>
					<view class="u-tips-color">
						能量开度:80%+80%+80%；UVB:550±50mj/cm²<br>
						能量开度:80%；UVV:1100±50mj/cm²
					</view>
					<u-form-item label="备注" prop="remark" >
						<u--textarea :count="!!info.remark" maxlength="800"  v-model="info.remark"  class="int"   placeholder="备注"  />
					</u-form-item>
					
				</div>
			</u--form>
        </div>

        <footer-btn :cancelBtnShow="false"  confirm_btn_text="保存" @onconfirm="save()"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import LoadSelector from '@/components/LoadSelector.vue';
import Selector from '@/components/Selector.vue';
import scanCodeInput from '../../components/scanCodeInput.vue';
import dayjs from "dayjs";
import {queryDetails,saveEdit}  from  '../../api/paintLinProd'
import {queryListByStaff}  from  '@/api/company'
	const prop =  defineProps({
			id: String,
			isComponent: {
				type: Boolean,
				default: false,
			},
			accountId:{
				type: String,
				default: null,
			}
		})
	const routerQuery = prop;
	const appStore = useAppStore();
	const router = useRouter();
	const { userInfo } = useUserStore();

    // 相关字典
    const dictStore = useDictStore()
    const companyList = ref([])
	const util = inject("util");
    const deleteFild = ['accName','createdStaffName','updateStaffName','salesLeadName','marketingName','areaName'];
    const requiredFild = [
        // {key:'name',message:'请填写机会名称',required: true,},
        // {key:'amount',message:'请填写金额',required: true,},
        // {key:'type',message:'请选择机会类型',required: true,},
    ]
    const save = () => {
        for(let index  in requiredFild){ 
            var item =  requiredFild[index]
            if(item.required && (!info.value[item.key] || info.value[item.key]=='')){
            	return uni.showToast({
            		title: item.message,
            		duration: 1500,
            		icon:'none'
            	});
            }
        };
        // let postData = {}
        // for(var key in info.value){
        //     if(!deleteFild.includes(key))
        //         postData[key] = info.value[key];
        // }
        saveEdit(info.value).then((res)=>{
            uni.showModal({
            	title: '提示',
            	content: info.value.id ?'修改成功':'保存成功',
            	showCancel:false,
            	success: function (confirmres) {
            		if (confirmres.confirm) {
            			if(prop.isComponent)emit('confirm', res)
            			else{
            				uni.$emit("refresh-paintLinProd", {refresh: true}); 
            				router.back();
            			}
            		} else if (confirmres.cancel) { 
            			console.log('用户点击取消');
            		}
            	}
            });
        })
    }
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const info = ref({
    });
    const init = async() =>{
        if(routerQuery.id){
            getInfo()
        }
    }
    onMounted(() => {
        init()
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
	:deep(.card_info){
		.u-form-item__body{
			padding: 15px 0 0 0;
		}
	}
</style>