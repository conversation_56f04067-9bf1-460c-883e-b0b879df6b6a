<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        {{ info.name }}
						<!-- <u-icon name="calendar" size="12" /> -->
                    </div>
                </div>
            </div>
        </div>
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                       基本信息
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
					<div class="item">
					    <div class="left">生产工单号</div><div class="rigth">{{info.prodOrderNo}}</div>
					</div>
					<div class="item">
					    <div class="left">型号</div><div class="rigth">{{info.model}}</div>
					</div>
					<div class="item">
					    <div class="left">进料板温</div><div class="rigth">{{info.materialTemp}}</div>
					</div>
                    <div class="item">
                        <div class="left">线速</div><div class="rigth">{{info.lineSpeed}}</div>
                    </div>
                    <div class="item">
                        <div class="left">附着底涂固化</div><div class="rigth">{{info.baseCoatCuring}}</div>
                    </div>
                    <div class="item">
                        <div class="left">附着底涂布量H-8022(JML)  UV辊涂附着底漆</div><div class="rigth">{{info.baseCoatAmount8022UvSpecial}}</div>
                    </div>
                    <div class="item">
                        <div class="left">附着底涂UV能量</div><div class="rigth">{{info.baseCoatUvEnergy}}</div>
                    </div>
                    <div class="item">
                        <div class="left">砂光底涂量_H-8123(JML)  UV辊涂附着底漆</div><div class="rigth">{{info.sandBaseCoatAmount8123Uv}}</div>
                    </div>
                    <div class="item">
                        <div class="left">砂光底涂UV能量</div><div class="rigth">{{info.sandBaseCoatUvEnergy}}</div>
                    </div>
                    <div class="item">
                        <div class="left">面漆涂布量H-82519(JML)  UV镭射肤感清面漆</div>
                        <div class="rigth">{{info.topCoatAmount8215UvClear}}</div>
                    </div>
                    <div class="item">
                        <div class="left">面漆预固化能量(单镓)</div><div class="rigth">{{info.topCoatPrecureEnergy}}</div>
                    </div>
                    <div class="item">
                        <div class="left">氮气固化含氧量</div>
                        <div class="rigth">{{info.airPressureContent}}</div>
                    </div>
                    <div class="item">
                        <div class="left">准分子灯能量</div>
                        <div class="rigth">{{info.monomerLampEnergy}}</div>
                    </div>
                    <div class="item">
                        <div class="left">面漆全国能量(双汞+单镓)</div>
                        <div class="rigth">{{info.topCoatNationalEnergyDualS}}</div>
                    </div>
                    <div class="item">
                        <div class="left">备注</div>
                        <div class="rigth">{{info.remark}}</div>
                    </div>
                    <!-- <div class="item">
                        <div class="left">客户产品</div>
                        <div class="rigth">
                            <div class="tag_list">
                                <div v-for="(item,index) in info.customerProductList" :key="index"  class="tag">
                                    <div class="tag_icon"><van-icon name="success" /></div> {{item}}
                                </div>
                            </div>
                        </div>
                    </div> -->
                </div>

                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.createdTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.updateTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>

        <footer-btn v-if="hasFactoryPermission('paintLinProd','U')"  :cancelBtnShow="false" @onconfirm="router.push({name: 'paintLinProdEdit',params: {id:routerQuery.id}})" confirm_btn_text="编辑"></footer-btn>
		<floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import {queryDetails}  from  '../../api/paintLinProd'

    const prop =  defineProps(['id'])
    const router = useRouter();
    const util = inject("util");
	const dictStore = useDictStore()
    const routerQuery =  prop;
    const info = ref({
    });
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const init = async() =>{
        getInfo()
    }

	onShow(() => {
		 init()
	})
	onMounted(() => {
	   
	})
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
	.card_info{
		.card{
			.item{
				padding-bottom: 5px;
				.left{
					line-height: 1.2;
				}
				.rigth{
					text-align: right;
				}
			}
			
			.head_status{
				background: #999 !important;
			}
		}
	}
	
</style>