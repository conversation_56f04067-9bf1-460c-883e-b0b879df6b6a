import request from "@/utils/http";
// 分页
export function queryPageData (data) {
    return request({
        url: '/api/jobAssignment/queryPageBy',
        method: 'get',
        data:data
    })
}

// 新增/修改
export function saveEdit (data) {
    return request({
        url: '/api/jobAssignment/save',
        method: 'post',
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        },
        data:data
    })
}


// 详细
export function queryDetails (id) {
    return request({
        url: '/api/jobAssignment/queryDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}

export function operate (operateType,data) {
    return request({
        url: `/api/jobAssignment/${operateType}`,
        method: 'post',
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        },
        data:data
    })
}

// 删除
export function deleteById (id) {
    return request({
        url: '/api/jobAssignment/deleteById',
        method: 'get',
        data:{
            id:id,
        }
    })
}