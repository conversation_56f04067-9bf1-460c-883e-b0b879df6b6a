import request from "@/utils/http";
// 分页
// http://yapi.vbeehive.com:81/project/31/interface/api/651
export function queryPageData (data) {
    return request({
        url: '/api/printingLineReport/queryPageBy',
        method: 'get',
        data:data
    })
}

// 新增/修改
// http://yapi.vbeehive.com:81/project/31/interface/api/643
export function saveEdit (data) {
    return request({
        url: '/api/printingLineReport/save',
        method: 'post',
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        },
        data:data
    })
}


// 详细
// http://yapi.vbeehive.com:81/project/31/interface/api/659
export function queryDetails (id) {
    return request({
        url: '/api/printingLineReport/queryDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}
