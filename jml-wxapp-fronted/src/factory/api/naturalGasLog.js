import request from "@/utils/http";
// 分页
export function queryPageData (data) {
    return request({
        url: '/api/naturalGasLog/queryPageBy',
        method: 'get',
        data:data
    })
}

// 新增/修改
export function saveEdit (data) {
    return request({
        url: '/api/naturalGasLog/save',
        method: 'post',
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        },
        data:data
    })
}


// 详细
export function queryDetails (id) {
    return request({
        url: '/api/naturalGasLog/queryDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}

// 上一次
export function queryBeforeLiquid () {
    return request({
        url: '/api/naturalGasLog/queryBeforeReading',
        method: 'get',
    })
}
