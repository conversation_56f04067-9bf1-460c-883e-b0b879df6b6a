import request from "@/utils/http";
// 分页
// http://yapi.vbeehive.com:81/project/31/interface/api/595
export function queryPageData (data) {
    return request({
        url: '/api/paintLinProd/queryPageBy',
        method: 'get',
        data:data
    })
}

// 新增/修改
// http://yapi.vbeehive.com:81/project/24/interface/api/141
export function saveEdit (data) {
    return request({
        url: '/api/paintLinProd/save',
        method: 'post',
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        },
        data:data
    })
}


// 详细
// http://yapi.vbeehive.com:81/project/24/interface/api/138
export function queryDetails (id) {
    return request({
        url: '/api/paintLinProd/queryDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}
