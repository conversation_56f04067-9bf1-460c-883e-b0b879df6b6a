import request from "@/utils/http";
// 分页
// http://yapi.vbeehive.com:81/project/31/interface/api/675
export function queryPageData (data) {
    return request({
        url: '/api/pressLineReport/queryPageBy',
        method: 'get',
        data:data
    })
}

// 新增/修改
// http://yapi.vbeehive.com:81/project/24/interface/api/141
export function saveEdit (data) {
    return request({
        url: '/api/pressLineReport/save',
        method: 'post',
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        },
        data:data
    })
}


// 详细
// http://yapi.vbeehive.com:81/project/24/interface/api/138
export function queryDetails (id) {
    return request({
        url: '/api/pressLineReport/queryDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}
