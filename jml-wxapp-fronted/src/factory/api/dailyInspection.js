import request from "@/utils/http";
// 分页
export function queryPageData (data) {
    return request({
        url: '/api/dailyInspection/queryPageBy',
        method: 'get',
        data:data
    })
}

// 新增/修改
export function saveEdit (data) {
    return request({
        url: '/api/dailyInspection/save',
        method: 'post',
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        },
        data:data
    })
}


// 详细
export function queryDetails (id) {
    return request({
        url: '/api/dailyInspection/queryDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}

// 确认
export function confirm (data) {
    return request({
        url: '/api/dailyInspection/confirm',
        method: 'get',
        data
    })
}
