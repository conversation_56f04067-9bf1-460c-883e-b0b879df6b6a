import request from "@/utils/http";
// 分页
export function queryPageData (data) {
    return request({
        url: '/api/deviceRepair/queryPageBy',
        method: 'get',
        data:data
    })
}

// 新增/修改
export function saveEdit (data) {
    return request({
        url: '/api/deviceRepair/submit',
        method: 'post',
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        },
        data:data
    })
}


// 详细
export function queryDetails (id) {
    return request({
        url: '/api/deviceRepair/queryDetails',
        method: 'get',
        data:{
            id:id,
        }
    })
}

export function operate (operateType,data) {
    return request({
        url: `/api/deviceRepair/${operateType}`,
        method: 'post',
        headers: {
            'Content-Type': 'application/json;charset=UTF-8'
        },
        data:data
    })
}

export function accept (id) {
    return request({
        url: '/api/deviceRepair/accept',
        method: 'get',
        data:{
            id:id,
        }
    })
}

