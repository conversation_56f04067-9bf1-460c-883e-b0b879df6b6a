<template>
	<u-picker
		title="创建员工"
		v-model:show="showPicker"
		:columns="[userList]"
		@confirm="onUserConfirm"
		@cancel="showPicker = false"
		keyName="name"
		closeOnClickOverlay
	/>
</template>

<script setup>
	import {queryUserList}  from  '@/api/common'
	const props = defineProps({
	    show: {
	        type: [String,Boolean],
	        default: false
	    }
	})
	const showPicker = computed({
	    get() {
	        return props.show
	    },
	    set(value) {
	        emits('onUpdate:show', value)
	    }
	})
	const emits = defineEmits(['confirm','onUpdate:show'])
	const userList =ref([])
	const userLoad = ref(true)
	const getUserList = ()=>{
	    queryUserList().then((res) => {
	        userList.value = res.data
	        userList.value.unshift({name:'全部',id:''})
	        userLoad.value = false
	        // if(prop.createdStaffId) selectedUser.value = userList.value.find(i=>i.id==prop.createdStaffId)
	    }).catch((err) => {
	    });
	}
	watch(()=>showPicker.value,(newVal,oldVal)=>{
		if(newVal && !userList.value || userList.value.length==0){
			getUserList()
			showPicker.value = true
		}
	},{ immediate: true,deep:true })
	
	const selectedUser = ref(null)
	const onUserConfirm = ({value,indexs}) => {
		console.log('onUserConfirm',value,indexs)
		let select = value[0];
		if(select){
			emits('confirm',select)
	    }
		showPicker.value = false
	}
</script>

<style>
</style>