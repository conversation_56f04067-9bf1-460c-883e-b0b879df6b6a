<template>
	<u--input class="scanCodeInput" style="width: 100%;" v-model="data"  placeholder="可点击扫码按钮" >
		<template #suffix>
			<u-button
				@tap="getCode"
				text="扫一扫"
				type="success"
				size="mini"
			></u-button>
		</template>
	</u--input>	
</template>
<script>
	export default {
		options: {
			// #ifdef MP-WEIXIN
			// 微信小程序中 options 选项
			multipleSlots: true, //  在组件定义时的选项中启动多slot支持，默认启用
			styleIsolation: "shared", //  启动样式隔离。当使用页面自定义组件，希望父组件影响子组件样式时可能需要配置。具体配置选项参见：微信小程序自定义组件的样式
			addGlobalClass: true, //  表示页面样式将影响到自定义组件，但自定义组件中指定的样式不会影响页面。这个选项等价于设置 styleIsolation: apply-shared
			virtualHost: true, //  将自定义节点设置成虚拟的，更加接近Vue组件的表现。我们不希望自定义组件的这个节点本身可以设置样式、响应 flex 布局等，而是希望自定义组件内部的第一层节点能够响应 flex 布局或者样式由自定义组件本身完全决定
			// #endif
		},
	}
</script>
<script setup>
	const props = defineProps({
	   modelValue:{
			type:[String,Number,Boolean],
	   },
	   disabled : {
		   type:[String,Number,Boolean],
		   default:false
	   }
	})
	const emits = defineEmits(['update:modelValue','change'])
	const data = computed({
	    get() {
	        return props.modelValue
	    },
	    set(value) {
	        emits('update:modelValue', value)
	    }
	})
	
	const  extractCode=(str)=> {
		const match = str.match(/\{(.*?):\d+\}/);
		return match ? match[1] : null;
	}

	const getCode = ()=>{
		if(props.disabled) return
		uni.scanCode({
			onlyFromCamera: true,
			success: function (res) {
				console.log('条码类型：' + res.scanType);
				console.log('条码内容：' + res.result);
				if(res.result) data.value = extractCode(res.result) || res.result
			},
			fail:(err)=>{
				uni.showToast({
					title:err.errMsg || '失败',
					icon:'none'
				})
			}
		});
	}
</script>

<style>
	.scanCodeInput{
		width: 100%;
	}
</style>