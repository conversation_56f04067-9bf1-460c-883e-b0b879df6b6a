<template>
	<view @click="showPicker = true"  class="item">
	    <u-icon size="6" labelSize="12" labelPos="left" :label="selectedData && selectedData[dataText]?selectedData[dataText]: title" name="/static/icon/sel_icon.png" />
		
		<u-picker
			:title="pickerTitle"
			v-model:show="showPicker"
			v-if="showPicker"
			:columns="[dataList]"
			@confirm="onConfirm"
			@cancel="showPicker = false"
			:keyName="dataText"
			closeOnClickOverlay
		/>
	</view>
</template>

<script setup>
	import {getRecordData}  from  '@/api/common'
	const props = defineProps({
	    show: {
	        type: [String,Boolean],
	        default: false
	    },
		modelValue:{
		    type:[String,Number,Boolean],
		},
		dataText:{
		    type:[String,Number,Boolean],
			default: 'name'
		},
		dataValue:{
		    type:[String,Number,Boolean],
			default: 'id'
		},
		load_url: {
		    type:[String],
		    default: null
		},
		dictKey: {
		    type: String,
		    default: null
		},
		title: {
		    type:String,
		    default: '请选择'
		},
		pickerTitle: {
		    type:String,
		    default: '请选择'
		},
		payload:{
		    type:Object,
		    default:{}
		},
		localdata:{
		    type: [Array],
		    default:[]
		},
	})
	const dataValue = computed({
	    get() {
	      return props.modelValue
	    },
	    set(value) {
	      emits('update:modelValue', value)
	    }
	})
	// const showPicker = computed({
	//     get() {
	//         return props.show
	//     },
	//     set(value) {
	//         emits('onUpdate:show', value)
	//     }
	// })
	const showPicker = ref(false)
	const emits = defineEmits(['confirm','onUpdate:show','update:modelValue','change'])
	const dataList =ref(props.localdata)
	const userLoad = ref(true)
	const dictStore = useDictStore()
	const getDataList = ()=>{
	    if(!props.load_url && !props.dictKey){
			showPicker.value = false
	    	return uni.showToast({
	    		title:`load_url 为空 和 dictKey，不同同时为空`,
	    		icon:'none',
	    		duration:1500
	    	}) 
	    }
		if(props.dictKey){
			dictStore.getDict(props.dictKey).then((response) => {
				dataList.value = response
			})
		}else{
			getRecordData(props.load_url,props.payload).then((res) => {
			    dataList.value = res.data
			}).catch((err) => {
			});
		}
	}
	const selectedData = ref(null)
	watch(()=>showPicker.value,(newVal,oldVal)=>{
		if(newVal && !dataList.value || dataList.value.length==0){
			getDataList()
		}
	},{ immediate: false,deep:true })
	
	watch(()=>dataValue.value,(newVal,oldVal)=>{
		if(!newVal){
			selectedData.value = []
		}
	},{ immediate: false,deep:true })
	const onConfirm = ({value,indexs}) => {
		selectedData.value = value[0];
		if(selectedData.value){
			dataValue.value = selectedData.value[props.dataValue]
			emits('confirm',selectedData.value)
	    }
		showPicker.value = false
	}
</script>

<style lang="scss" scoped>
	.item {
	    margin-right: 10px;
	    align-items: center;
	    display: flex;
	    justify-content: center;
	}
</style>