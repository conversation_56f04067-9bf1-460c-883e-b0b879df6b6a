<template>
	<view>
		<button @click="time_show=true">选择时段</button>
		<view>
			时间：<text v-if="hour<10">0</text>{{hour}}:<text v-if="minute<10">0</text>{{minute}}
				- <text v-if="hour2<10">0</text>{{hour2}}:<text v-if="minute2<10">0</text>{{minute2}} 
		</view>
		<u-popup :safeAreaInsetBottom="true" :show="time_show" :round="4" @close="time_show = false">
			<view class="time_title">
				<view style="color: #666666;" @click="time_show=false">取消</view>
				<view style="color: #FF6E00;" @click="time_show=false">确认</view>
			</view>
			<picker-view :indicator-style="indicatorStyle" :value="value" @change="bindChange" class="picker-view">
				<picker-view-column>
					<view class="item" v-for="(item,index) in hours" :key="index">{{item}}时</view>
				</picker-view-column>
				<picker-view-column>
					<view class="item" v-for="(item,index) in minutes" :key="index">{{item}}分</view>
				</picker-view-column>
				<view class="box">至</view>
				<picker-view-column>
					<view class="item" v-for="(item,index) in hours" :key="index">{{item}}时</view>
				</picker-view-column>
				<picker-view-column>
					<view class="item" v-for="(item,index) in minutes" :key="index">{{item}}分</view>
				</picker-view-column>
			</picker-view>
		</u-popup>
	</view>
</template>
<script>
	export default {
		data: function() {
			const date = new Date()
			const hours = []
			const hours2 = []
			const minutes = []
			const minutes2 = []
			for (let i = 0; i <= 23; i++) {
				hours.push(i)
			}
			for (let i = 0; i <= 59; i++) {
				minutes.push(i)
			}
			for (let i = 0; i <= 23; i++) {
				hours2.push(i)
			}
			for (let i = 0; i <= 59; i++) {
				minutes2.push(i)
			}
			return {
				time_show: false,
				hour: date.getHours(),
				minute: date.getMinutes(),
				hour2: date.getHours(),
				minute2: date.getMinutes(),
				hours2,
				minutes2,
				hours,
				minutes,
				indicatorStyle: `height: 50px;`
			}
		},
		methods: {
			bindChange: function(e) {
				const val = e.detail.value
				this.hour = this.hours[val[0]] ? this.hours[val[0]] : '0'
				this.minute = this.minutes[val[1]] ? this.minutes[val[1]] : '0'
				this.hour2 = this.hours[val[3]] ? this.hours[val[3]] : '0'
				this.minute2 = this.minutes[val[4]] ? this.minutes[val[4]] : '0'
			}
		}
	}
</script>
<style>
	.time_title {
		display: flex;
		justify-content: space-between;
		padding: 30rpx;
		font-size: 32rpx;
	}
 
	.box {
		position: absolute;
		left: 360rpx;
		top: 280rpx;
	}
 
	.picker-view {
		width: 750rpx;
		height: 600rpx;
		margin-top: 20rpx;
	}
 
	.item {
		line-height: 100rpx;
		text-align: center;
	}
</style>