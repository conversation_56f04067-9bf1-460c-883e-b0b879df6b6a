// 提交：SPZ；拒绝：已拒绝；通过：YTG
export const partsPurchaseStatus = {
    submit:'SPZ',
    refuse:'YJJ',
    pass:'YTG',
	draft:"DTJ",
}

export const partsPurchaseStatusInfo = {
    SPZ:{text:'审批中',color:'#FDBA00',btnColor:'#849EB2'},
    DTJ:{text:'待提交中',color:'#B6C2CE',btnColor:'#FDBA00',},
    YTG:{text:'通过',color:'#82CA94',btnColor:'#82CA94',icon:'checkmark-circle-fill'},
	YJJ:{text:'已拒绝',color:'#E97056',btnColor:'#E97056',icon:'close-circle-fill'},
}

export const  deviceRepairStatusInfo = {
    assign:{text:'分配维修员',color:'#FDBA00',btnColor:'#849EB2'},
    repair:{text:'添加维修进展',color:'#B6C2CE',btnColor:'#FDBA00',icon:"plus-circle-fill",dialogShow:true,dialogTitle:"添加维修内容"},
    complete:{text:'完成维修',color:'#82CA94',btnColor:'#4085CB',icon:'checkmark-circle-fill'},
	confirm:{text:'认可',color:'#82CA94',btnColor:'#82CA94',icon:'checkmark-circle-fill',dialogShow:true,dialogTitle:"评分"},
	cancel:{text:'取消',color:'#E97056',btnColor:'#E97056',icon:'close-circle-fill',dialogShow:true,dialogTitle:"取消原因"},
	re_assign:{text:'重新分配',color:'#FDBA00',btnColor:'#FDBA00',icon:'more-circle-fill',dialogShow:true,dialogTitle:"重新分配"},
}

export const impactLevel = {
    "生产已停止":{color:'#E97056',bgColor:'#E97056'},
    "机器运行，会影响品质":{color:'#FDBA00',bgColor:'#FDBA00',},
    "机器运行，会影响产量":{color:'#82CA94',bgColor:'#82CA94'},
}

export const jobAssignStatus = {
    "新建":{color:'#BEBEBE',bgColor:'#BEBEBE'},
    "员工完成":{color:'#4085CB',bgColor:'#4085CB',},
    "已分配":{color:'#FDBA00',bgColor:'#FDBA00'},
	"主管确认":{color:'#82CA94',bgColor:'#82CA94'},
}

export const dailyInspection = {
    "待提交":{color:'#BEBEBE',bgColor:'#BEBEBE'},
    // "员工完成":{color:'#4085CB',bgColor:'#4085CB',},
    "待确认":{color:'#FDBA00',bgColor:'#FDBA00'},
	"已确认":{color:'#82CA94',bgColor:'#82CA94'},
}

export const dailyInspectionTabs = {
    0:{btnCode:'isEbConfirm',type:'ebSkin','text':'肤感线、EB线'},
    1:{btnCode:'isPressConfirm',type:'press','text':'压贴线'},
    2:{btnCode:'isChamferConfirm',type:'chamfer','text':'倒角线'},
    3:{btnCode:'isPrintConfirm',type:'printLine','text':'打印线'},
}

export const  jobAssignBtnInfo = {
	save:{btnName:'编辑',color:'#82CA94',btnColor:'#849EB2'},
    complete:{text:'完成',color:'#82CA94',btnColor:'#849EB2',dialogShow:true,dialogTitle:"工人完成",otherData:{completionStatus:'已完成'}},
	confirm:{text:'确认',color:'#849EB2',btnColor:'#849EB2',dialogShow:true,dialogTitle:"确认完成",otherData:{}},
}

export const dailyInspectionImgFild = ['ebSkinCompCheckImg','ebSkinEqInspectDoneImg','ebSkinDailyMaintDoneImg','ebSkinAirPressCheckImg','ebSkinBeltPosCheckImg','ebSkinBladeCheckImg',
	"ebSkinGlueLeakCheckImg","ebSkinCoaterCheckImg","ebSkinLoaderCheckImg","ebSkinCuringBoxCheckImg","ebSkinConvCleanlinessImg",
	"pressEqInspectionDoneImg","pressDailyMaintenanceDoneImg","pressAirPressureCheckImg","pressToolCheckImg","pressSensorCheckImg","pressLocatorCheckImg",
	"pressConveyorCleanlinessImg","pressBrushMachineCheckImg","pressBrushLoaderCheckImg",
	"chamferEqInspectionDoneImg","chamferDailyMaintenanceDoneImg","chamferAirPressureCheckImg","chamferToolCheckImg","chamferSensorCheckImg",
	"chamferLocatorCheckImg","chamferConveyorCleanlinessImg","chamferBrushMachineCheckImg","chamferLoaderCheckImg",
    "printLineCompCheckImg","printLineEqInspectDoneImg","printLineDailyMaintDoneImg","printLineAirPressCheckImg","printLineBeltPosCheckImg","printLineGlueLeakCheckImg",
    "printLineDustCheckImg","printLineRollerCheckImg","printLineTempCheckImg","printLineHopperCheckImg","printLineConveyorCleanImg"
];