.tabInfo{
	border-radius: 6px 6px 0pt 0pt;
	background-color: rgba(132,158,178,0.22);
	color: rgba(132,158,178,1);
	font-size: 14px;
	display: inline-flex;
	padding: 5px 15px;
}
.card_head_tit3{
	font-size: 14px;
	font-weight: 600;
	line-height: 1;
}
.pd10{
	padding-bottom: 10px;
}
:deep(.card_info){
	.u-form-item__body{
		// padding: 5px 0 0 0;
	}
}
.page{
	.addInfo_pop{
		max-height: 90vh;
		.card_head{
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 15px 0 10px 0;
			.card_head_tit{
				display: flex;
				    align-items: center;
				    font-size: 18px;
				    font-weight: 600;
				    color: #333333;
			}
		}
		.u-form{
			// height: 100%;
		}
		.card{
			max-height: auto !important;
			overflow-y: hidden !important;
			height: calc(100% - 150px) !important;
			padding: 0 0 70px 0 !important;
		}
		.card_from{
			// max-height: 70vh;
			// overflow-x: hidden;
			// overflow-y: scroll;
			padding: 0 20px;
		}
	}
}

.footer .footer_btn .btn{
	flex: 1;
	border-radius: 0px;
	.u-icon{
		margin-right: 5px;
	}
	&.draft_btn{
		flex: 1;
		order: -1;
		background-color: rgba(234,175,12,1);
		color: #fff;
	}
}

.mlauto{
	margin-left: auto;
}
:deep(.tabHead){
	.u-form-item__body__right{
		border:none;
		box-shadow:none;
		border-bottom: 1upx solid #E2E6F6;
		padding: 4px 2px;
		border-radius:0;
		display: flex;
		// text-align: right;
	}
	.u-form-item__body{
		padding: 5px 0;
	}
	.int-time{
		box-shadow: 0 1px 4px rgba(0, 0, 0, 0.0509803922);
		border-radius: 4px;
		opacity: 1;
		border: 1px solid #E2E6F6;
	}
	.no-border{
		.u-form-item__body__right{
			border: none !important;
		}
	}
}
.total-content{
	display: flex;
}
.section{
	width: 100%;
}
.section-item{
	// display: block;
	width: 100%;
}
.u-text__value--content{
	color: #606266;
}
.value--content{
	color: #999999;
	margin-top: 3px;
}
:deep(.card_main){
	.u-steps-item__content--column{
		margin-top: 0 !important;
		margin-bottom: 10px;
	}
}