import { computed } from 'vue'
import useUserStore from '@/store/modules/userStore'
/**
 * 封装权限
 */
export function usePermission() {
    const store = useUserStore();
    const {userId,token,menuList } = store;

    const hasPermission = (menu_code,function_code) => {
        if (!userId || !menuList) return false;
        let check = menuList.find(item => item.menu_code == menu_code && item.function_code == function_code)
        return check?true:false
    }
    return {
        hasPermission,
    }
}