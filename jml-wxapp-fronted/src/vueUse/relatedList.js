import { computed, ref, unref } from 'vue'

/**
 * 封装获取列表
 * @param {string|Ref<string>} url 请求地址
 * @param {boolean} immediate 是否在初始化时加载，默认: `true`
 * @param {function} transform 转换搜索表单
 */
export function useRelatedList({size = 20, url, transform, immediate = true }) {
    const pageNo = ref(1)
    const pageSize = ref(size)
    const totalRow = ref(0)
    const query = ref({})
    const loading = ref(false)
    const finished = ref(false)
    const refreshLoading = ref(false)
    const list = ref([])

  // 请求上传的表单 (为了方便处理搜索绑定的表单与请求所需表单存在差异的情况)
    const payload = computed(() => {
        const _query = transform ? transform(unref(query)) : query.value
        return Object.assign({}, _query, {
            pageNo: pageNo.value, // 请求的当前页字段
            pageSize: pageSize.value, // 请求的条数字段
        })
    })

    immediate && loadFun()

    async function loadFun() {
        // TODO: 请求api及赋值 (使用 `unref` 解构 `url`)
    }

    function search() {
        pageSize.value = 1
        list.value = []
        loadFun()
    }


    return {
        query,
        pageNo,
        pageSize,
        totalRow,
        list,
        payload,
        loading,
        finished,
        refreshLoading,
        loadFun,
        search,
    }
}