import { reactive } from 'vue'
export function useDataList() {
    const data = reactive({
        pageNo:1,
        pageSize:5,
        totalRow:0,
        list:[],
        loading: false,
        finished: false,
        query:{},

        initEmpty:()=>{
			data.pageNo=1,
			data.pageSize=5,
			data.totalRow=0,
			data.list=[],
			data.loading= false,
			data.finished= false,
			data.query={}
        }
    })
	
	const finishedText = computed(() => {
	    return  data.list.length>0?'没有更多了':'';
	})
	
    return { data,finishedText }
}