#  hbxw-time-range-picker组件

## 介绍
一个时间段选择器插件如: 15:00:00-16:00:00 / 15时00分00秒-16时00分00秒 / 15:00-16:00 / 15时00分-16时00分

## 使用示例
推荐先直接复制示例代码到工程中看效果了解下使用方法再投入项目使用。

```html

<template>
	<view>
		<view>{{result}}</view>
		<button class="button-item" @click="pickerAction0">中文单位的时分秒时间段选择器</button>
		<button class="button-item" @click="pickerAction1">冒号分隔的时分秒时间段选择器</button>
		<button class="button-item" @click="pickerAction2">冒号分隔的时分时间段选择器</button>
		<button class="button-item" @click="pickerAction3">冒号分隔的限制起始和结束的时分秒时间段选择器</button>
		<hbxw-time-range-picker 
			:value="values0"
      <!-- #ifdef VUE2 -->
			:visible.sync="showPicker0"
      <!-- #endif -->
      <!-- #ifdef VUE3 -->
      v-model:visible="showPicker0"
      <!-- #endif -->
			@change="pickerChange0"
			@cancel="pickerCancel0"
      maskOpacity=".3"
      isAni
			@sure="pickerSure0"
		/>
		<hbxw-time-range-picker 
			:value="values1" 
			ref="testRangePicker"
			:isShowType="false"
			delimiter="point"
			@change="pickerChange1"
			@cancel="pickerCancel1"
			@sure="pickerSure1"
		/>
		<hbxw-time-range-picker 
			:value="values2" 
			:visible="showPicker2"
			:isShowType="false"
			delimiter="point"
			:isShowSecond="false"
			@change="pickerChange2"
			@cancel="pickerCancel2"
			@sure="pickerSure2"
		/>
		<hbxw-time-range-picker 
			:value="values3" 
			:visible="showPicker3"
			:isShowType="false"
			delimiter="point"
			limitStart="08:08:08"
			limitEnd="22:22:22"
			@change="pickerChange3"
			@cancel="pickerCancel3"
			@sure="pickerSure3"
		/>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				showPicker0: false,
				showPicker1: false,
				showPicker2: false,
				showPicker3: false,
				result: null,
				values0: '01时01分01秒-01时01分02秒',
				values1: '08:06:09-08:09:09',
				values2: '08:06-08:09',
				values3: ''
			}
		},
		methods: {
			pickerAction0() {
				this.showPicker0 = true;
			},
			pickerCancel0() {
				this.showPicker0 = false;
			},
			pickerSure0(result) {
				console.log('---- pickerSure0 ----:', result);
				this.result = result;
				this.showPicker0 = false;
			},
			pickerAction1() {
        this.$refs.testRangePicker.show();
			},
			pickerCancel1() {
        console.log('---- pickerCancel1 ----');
			},
			pickerSure1(result) {
				console.log('---- pickerSure1 ----:', result);
				this.result = result;
				this.showPicker1 = false;
			},
			pickerAction2() {
				this.showPicker2 = true;
			},
			pickerCancel2() {
				this.showPicker2 = false;
			},
			pickerSure2(result) {
				console.log('---- pickerSure2 ----:', result);
				this.result = result;
				this.showPicker2 = false;
			},
			pickerAction3() {
				this.showPicker3 = true;
			},
			pickerCancel3() {
				this.showPicker3 = false;
			},
			pickerSure3(result) {
				console.log('---- pickerSure3 ----:', result);
				this.result = result;
				this.showPicker3 = false;
			},
			pickerChange0(result) {
				console.log('---- pickerChange0 ----:', result);
				this.result = result;
			},
			pickerChange1(result) {
				console.log('---- pickerChange1 ----:', result);
				this.result = result;
			},
			pickerChange2(result) {
				console.log('---- pickerChange2 ----:', result);
				this.result = result;
			},
			pickerChange3(result) {
				console.log('---- pickerChange3 ----:', result);
				this.result = result;
			}
		}
	}
</script>

<style lang="scss" scoped>
.button-item{
	margin-bottom: 20rpx;
	font-size: 26rpx;
}
</style>

```

## API

### Props

| 属性名       | 类型          | 默认值    | 必填 | 说明                               |
| ------------ | -------------| -------- | ---- | -----------------------------------|
| value      | Array/String   | []       | 否   | 时间段选择的初始值              |
| visible      | Boolean      | false    | 是   | 时间段选择器的显示与隐藏,vue2项目可以通过visible.sync实现双向绑定，vue3通过v-model:visible实现双向绑定           |
| title        | String       | 选择时间  | 否   | 时间段选择器标题，如果不需要传空即可   |
| zIndex       | Number       | 9999     | 否   | 时间选择器的层级                     |
| isShowType     | Boolean    | true     | 否   | 是否显示开始时间和结束时间标题                   |
| delimiter     | String      | 'cn'     | 否   | 时间选择器分隔符格式:cn(时分秒)/point(:)/none(无)        |
| isShowSecond     | Boolean  | true     | 否   | 是否要选择到秒，为true则是一个时分秒时间段选择器，如果是false，则上一个时分时间段选择器        |
| limitStart     | Array/String  | ['00','00','00']     | 否   | 时间段选择限制可选开始时间，配置10:10:10 或 10时10分10秒 或者 ['10','10','10']都有效，如果想配置不小于当前时间，可以直接设置为now        |
| limitEnd     |  Array/String  | ['23','59','59']     | 否   | 时间段选择限制可选结束时间，配置10:10:10 或 10时10分10秒 或者 ['10','10','10']都有效         |
| maskOpacity     |  Number  | 0     | 否   | 值范围0-1，设置大于0的值就会有遮罩，值越大遮罩越深         |
| isAni     |  Boolean  | false     | 否   | 是否有动效     |
| isMaskClose     |  Boolean  | false     | 否   | 是否点击遮罩关闭     |

### Events

| 事件名       | 说明   | 返回值                                                                                                                                 |
| ------------ | ------------  |  ------------ |
| cancel       | 点击取消按钮的触发事件 |  undefined                                                                                                             |
| sure         | 点击确认按钮的触发事件 |    举列：{index: Array(6), values: Array(6), valuesStr0: "01时01分01秒-01时01分02秒", valuesStr1: "01:01:01-01:01:02"}  |
| change       | picker状态发生变化触发事件 |  举列：{index: Array(6), values: Array(6), valuesStr0: "01时01分01秒-01时01分02秒", valuesStr1: "01:01:01-01:01:02"} |

### 注意

可以通过ref获取组件实例，获取实例属性和方法，其中show为显示方法，hide为隐藏方法

组件大小显示较大，是因为其中有示例图片，真正打包到项目中的体积是很小的