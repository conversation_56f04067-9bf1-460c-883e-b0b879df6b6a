## 1.2.18（2023-06-01）
更新
## 1.2.16（2023-05-10）
增加单元格点击事件
完善文档
增加示例
## 1.2.15（2022-08-25）
优化部分细节
## 1.2.14（2022-04-25）
修改vue 3 报错小程序Generated an empty chunk: "uni_modules/zb-table/components/zb-table/js/util"
## 1.2.13（2022-04-22）
增加图片宽度设置
## 1.2.12（2022-04-22）
修复pc端 滚动条占位问题
## 1.2.11（2022-04-22）
增加多图片展示
## 1.2.10（2022-04-19）
版本解决冲突
## 1.2.9（2022-04-19）
暂时去掉多级表头...有着某些问题，正在修复中
## 1.1.9（2022-04-19）
暂时去掉多级表头...有着某些问题，正在修复中
## 1.1.23（2022-04-19）
暂时去掉多级表头，有着某些问题，修复中。。。
## 1.1.22（2022-04-19）
暂时去掉多级表头，存在某些问题 ，正在修复中
## 1.1.21（2022-03-29）
优化数字问题
## 1.1.20（2022-03-29）
优化按钮，可以自定义按钮，自定义添加class
## 1.1.19（2022-03-28）
进行优化加载
## 1.1.18（2022-03-28）
修复pc端滚动条问题
## 1.1.17（2022-03-25）
修改 数据回显的时候，全选框没有选中效果
## 1.1.16（2022-03-25）
新增：table属性 cell-style 修改单元格样式
## 1.1.15（2022-03-23）
fix:支付宝小程序上拉加载e.detail 没有值导致上拉加载失效 ，已修复
## 1.1.14（2022-03-23）
fix: 支付宝小程序左右无法滑动的问题
## 1.1.13（2022-03-21）
fix：英文宽度自适应问题
## 1.1.12（2022-03-20）
修改自适应宽度问题
## 1.1.11（2022-03-19）
增加上拉加载功能
## 1.1.10（2022-03-18）
修改合计不更新问题
## 1.1.9（2022-03-16）
优化css 样式
## 1.1.8（2022-03-16）
增加表尾合计行
## 1.1.7（2022-03-15）
修改css样式
## 1.1.6（2022-03-14）
进行代码优化
## 1.1.5（2022-03-12）
更新一个操作按钮的时候 报错问题，进行代码优化
## 1.1.4（2022-03-12）
增加图片统一高度
## 1.1.3（2022-03-12）
增加图片地址 ，并且图片支持预览功能
## 1.1.2（2022-03-11）
新增默认 是否选中功能，进行优化
## 1.1.1（2022-03-10）
新增单击事件
## 1.1.0（2022-03-10）
- 增加单击事件

## 1.1.0（2022-03-10）
- 增加checkbox功能 ，进行优化

## 1.0.11（2022-03-09）
- 修改小程序中排序问题

## 1.0.10（2022-03-09）
- 做了兼容性处理

## 1.0.8（2022-03-09）
- 进行优化滚动防止 多次计算

## 1.0.7（2022-03-09）
- 修改一些问题 新增过滤器

## 1.0.6（2022-03-08）
- 修改样式 按钮自适应宽度

## 1.0.5（2022-03-08）
- 新增按钮 修改问题

## 1.0.4（2022-03-04）
- 增加空占位符"--"

## 1.0.3（2022-03-02）
- 新增表格斑马纹配置、列宽配置、表头显示配置

## 1.0.2（2022-03-02）
- 新增排序功能，优化样式

## 1.0.1（2022-03-01）
- 可以传入动态数据，可以对左边列表进行是否固定首列

## 1.0.0（2022-03-01）
- 初始化