{"id": "hd-utils", "displayName": "hd-utils fant-mini-plus用到的公共css和js库", "version": "0.0.3", "description": "hd-utils fant-mini-plus用到的公共css和js库", "keywords": ["hd-utils", "fant-mini-plus", "vue3", "组件库", "工具类"], "repository": "https://gitee.com/fant-mini/uniapp-vue3-fant-ts", "engines": {"HBuilderX": "^3.7.11"}, "dcloudext": {"type": "sdk-js", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "https://github.com/Moonofweisheng/uniapp-vue3-fant-ts"}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"Vue": {"vue2": "n", "vue3": "y"}, "App": {"app-vue": "y", "app-nvue": "u"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "u", "IE": "u", "Edge": "u", "Firefox": "u", "Safari": "u"}, "小程序": {"微信": "y", "阿里": "y", "百度": "u", "字节跳动": "u", "QQ": "y", "钉钉": "y", "快手": "u", "飞书": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}}}}}