@import "../theme.scss";

@mixin hairline-common() {
  position: absolute;
  box-sizing: border-box;
  transform-origin: center; /* cover wechat button:after default transforn-origin */
  content: ' ';
  pointer-events: none;
}

@mixin hairline($color: $border-color) {
  @include hairline-common();
  top: -50%;
  right: -50%;
  bottom: -50%;
  left: -50%;
  border: 0 solid $color;
  transform: scale(0.5);
}

@mixin hairline-top($color: $border-color, $left: 0, $right: 0) {
  @include hairline-common();

  top: 0;
  right: $right;
  left: $left;
  border-top: 1px solid $color;
  transform: scaleY(0.5);
}

@mixin hairline-bottom($color: $border-color, $left: 0, $right: 0) {
  @include hairline-common();
  right: $right;
  bottom: 0;
  left: $left;
  border-bottom: 1px solid $color;
  transform: scaleY(0.5);
}
