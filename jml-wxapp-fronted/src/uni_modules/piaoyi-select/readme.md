### piaoyi-select 选择框

**使用方法：**

```
<template>
	<view style="padding: 20rpx;">
		<piaoyi-select :inputValue.sync="inputValue" ref="piaoyiSelect" :clearable="clearable" :filterable="filterable" :placeholder="placeholder" :options="options"></piaoyi-select>
		<view class="title" style="margin-top: 400rpx;">
			当前Select的值: {{inputValue}}
		</view>
		<view class="title" style="margin-top: 20rpx;">
			是否可搜索
		</view>
		<radio-group @change="radioChange" class="radioGroup">
			<label class="radioGroup-item" v-for="(item, index) in filteritems" :key="item.value">
				<view>
					<radio :value="item.value" :checked="index === current" />
				</view>
				<view>{{item.name}}</view>
			</label>
		</radio-group>
		<view class="title" style="margin-top: 20rpx;">
			是否可清空
		</view>
		<radio-group @change="clearradioChange" class="radioGroup">
			<label class="radioGroup-item" v-for="(item, index) in clearitems" :key="item.value">
				<view>
					<radio :value="item.value" :checked="index === current2" />
				</view>
				<view>{{item.name}}</view>
			</label>
		</radio-group>
		<view class="title" style="margin-top: 20rpx;color: red;">
			具体使用方法请看插件市场简介
		</view>
	</view>
</template>

<script>
	import piaoyiSelect from '@/uni_modules/piaoyi-select/components/piaoyi-select/piaoyi-select.vue'
	export default {
		data() {
			return {
				clearable: true,
				filterable: true,
				inputValue: '',
				filteritems: [{
                    value: '是',
                    name: '是'
                },
                {
                    value: '否',
                    name: '否'
                }],
				clearitems: [{
                    value: '是',
                    name: '是',
                    checked: true
                },
                {
                    value: '否',
                    name: '否',
					checked: false
                }],
				current: 0,
				current2: 0,
				options: [{
					label: '选项1',
					value: '选项1'
				}, {
					label: '选项2',
					value: '选项2'
				}, {
					label: '选项3',
					value: '选项3'
				}, {
					label: '选项4',
					value: '选项4'
				}],
				placeholder: '请选择关键字'
			};
		},
		components: {
			piaoyiSelect
		},
		methods: {
			getValue() {
				var res = this.$refs.getValue()
				console.log(res)
			},
			radioChange(e) {
				if (e.detail.value == '是') {
					this.filterable = true
				} else {
					this.filterable = false
				}
			},
			clearradioChange(e) {
				if (e.detail.value == '是') {
					this.clearable = true
				} else {
					this.clearable = false
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
.title {
	padding: 20rpx;
	font-size: 28rpx;
	line-height: 28rpx;
	font-weight: 700;
	color: #333;
}
.radioGroup {
	display: flex;
	padding: 0 18rpx;
}
.radioGroup-item {
	display: flex;
	margin-right: 30rpx;
	uni-radio {
		transform: scale(0.8);
	}
}
</style>

```

#### 事件说明

|   事件名    | 返回值 |      描述      |
| :---------: | :----: | :------------: |
| getValue |   组件input绑定的值   | 可通过ref调用getValue事件获取 |

#### Prop

| 参数名称 | 描述                           |     默认值      |
| -------- | ------------------------------ |   ---------   |
| inputValue | 实现数据绑定的key值，注意加上sync |            |
| options | 下拉选项数据 |            |
| filterable | 是否可编辑 |      false      |
| clearable | 是否可清空 |      true      |
| placeholder | 提示文本 |      请选择关键字      |

### 可接定制化组件开发
### 右侧有本人代表作小程序二维码以及插件体验小程序，可以扫码体验，部分插件由于带有图片导致小程序无法正常发布，所以只展示部分插件
### 如使用过程中有问题或有一些好的建议，欢迎加QQ群互相学习交流：120594820