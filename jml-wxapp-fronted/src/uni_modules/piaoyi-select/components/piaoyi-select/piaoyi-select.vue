<template>
	<view class="select-container">
		<u-input class="input" @tap="changeActive" type="text" :disabled="!filterable" v-model="myvalue" @input="handleInput" :placeholder="placeholderStr ? placeholderStr : placeholder" @blur="onBlur"
			@focus="onFocus" />
		<view class="dropdown" v-if="showDropdown">
			<view class="item" :class="active === index ? 'itemActive' : ''" v-for="(option, index) in selectOptions" :key="option[valueType.value]" @click="handleSelect(option, index)">
				{{ option[valueType.label] }}
			</view>
			<slot name="empty"></slot>
			<view class="no-data" v-if="selectOptions.length === 0">无数据</view>
		</view>
		<!-- <image class="jiantou" :class="showDropdown ? 'jtactive' : ''" src="./jiantou.png" mode="widthFix" @tap.stop=""></image> -->
		<u-icon class="jiantou" size="14" :class="showDropdown ? 'jtactive' : ''"	slot="right" name="arrow-down"></u-icon>
		<image class="close" v-if="clearable && (placeholderStr || inputValue) && showDropdown" src="./close.png" mode="widthFix" @tap.stop="onClose"></image>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				showDropdown: false,
				selectOptions: [],
				placeholderStr: '',
				active: null,
				myvalue: '',
			};
		},
		props: {
			inputValue: {
				type: [String,Number,Boolean],
				default: ''
			},
			modelValue:{
				type:[String,Number,Boolean],
			},
			options: {
				type: Array,
				default: () => []
			},
			clearable: {
				type: Boolean,
				default: false
			},
			filterable: {
				type: Boolean,
				default: false
			},
			placeholder: {
				type: String,
				default: '请选择关键字'
			},
			valueType: {
				type: Object,
				default:()=>{
					return {label:'dname',value:'dname'}
				} 
			},
		},
		watch:{
			'modelValue':{
				immediate:true,
				// deep:true,
				handler(news){
					if(news){
						let index = this.options.findIndex(ite=>ite[this.valueType.value]==news)
						if(index==-1){
							uni.showToast({
								title:`选项里不存在~${news}`,
								icon:'none',
								duration:1500
							})
						}else{
							this.myvalue = this.options[index][this.valueType.label]
						}
					}
				}
			}
		},
		mounted() {
			this.selectOptions = [...this.options]
		},
		methods: {
			handleInput() {
				const keyword = this.myvalue;
				// 根据关键词进行筛选数据
				const filteredOptions = this.options.filter(item => item[this.valueType.label].includes(keyword));
				this.selectOptions = filteredOptions;
				this.showDropdown = true;
			},
			handleSelect(option, index) {
				this.myvalue = option[this.valueType.label];
				this.active = index
				// this.$emit('update:inputValue', option[this.valueType.value]);
				this.$emit('update:modelValue', option[this.valueType.value])
				this.$emit('change', option)
				this.showDropdown = false;
			},
			onBlur() {
				if (this.placeholderStr) {
					this.myvalue = this.placeholderStr
				}
				this.selectOptions = [...this.options]
				setTimeout(() => {
					this.showDropdown = false;
				}, 200)
			},
			onFocus() {
				if (this.myvalue) {
					this.placeholderStr = this.myvalue
					this.myvalue = ''
				}
				this.selectOptions = [...this.options]
				if (this.placeholderStr || this.myvalue) {
					this.selectOptions.forEach((item, index) => {
						if (item[this.valueType.value] == this.placeholderStr || item[this.valueType.value] == this.myvalue) {
							this.active = index
							return
						}
					})
				}
				this.showDropdown = true;
			},
			onClose() {
				this.placeholderStr = ''
				this.myvalue = ''
				this.active = null
				this.selectOptions = [...this.options]
				this.$emit('update:modelValue', option[this.valueType.value])
				this.showDropdown = false;
			},
			changeActive() {
				if (this.filterable) return
				this.showDropdown = true
			}
		},
	};
</script>

<style lang="scss" scoped>
	.select-container {
		position: relative;
		width: 100%;
		display: flex;
	}
	.select-container > image {
		width: 30rpx;
		height: 28rpx;
		position: absolute;
		right: 20rpx;
		top: 24rpx;
		border-radius: 30rpx;
		z-index: 10;
	}
	.select-container > .close {
		right: 80rpx;
		width: 30rpx;
		height: 30rpx;
		top: 22rpx;
	}
	.input {
		width: calc(100% - 40rpx - 2px);
		height: calc(100% - 30rpx - 2px);
		padding: 15rpx 20rpx;
		/* border: 1px solid #ccc; */
		border-radius: 8rpx;
		font-size: 28rpx;
		color: #333;
	}
	.input::-webkit-input-placeholder {
	  color: var(--placeholder-color);
	}
	.input::-moz-input-placeholder {
	  color: var(--placeholder-color);
	}
	.input::-ms-input-placeholder {
	  color: var(--placeholder-color);
	}

	.dropdown {
		position: absolute;
		background: #fff;
		top: 90rpx;
		left: 0;
		width: calc(100% - 2px);
		max-height: 400rpx;
		overflow-y: auto;
		border: 1px solid #0000000d;
		font-size: 28rpx;
		color: #333;
		box-shadow: 0 1px 4px #0000000d;
		border-radius: 4px;
		opacity: 1;
		z-index: 9;
	}

	.item {
		padding: 15upx 20rpx;
		background-color: #fff;
		cursor: pointer;
	}

	.no-data {
		padding: 20rpx;
		text-align: center;
		color: #999;
	}
	.select-container > .jiantou {
		transition: 0.2s;
		
	}
	.jtactive {
		transform: rotate(180deg);
	}
	.itemActive {
		color:var(--main-selected-c,#409eff);
		font-weight: 700;
	}
</style>
