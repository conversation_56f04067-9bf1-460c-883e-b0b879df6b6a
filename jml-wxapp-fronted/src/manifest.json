{
    "name" : "金米龙",
    "appid" : "__UNI__4EB86B2",
    "description" : "",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    /* 5+App特有相关 */
    "app-plus" : {
        "usingComponents" : true,
        "nvueStyleCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        /* 模块配置 */
        "modules" : {},
        /* 应用发布信息 */
        "distribute" : {
            /* android打包配置 */
            "android" : {
                "permissions" : [
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ]
            },
            /* ios打包配置 */
            "ios" : {},
            /* SDK配置 */
            "sdkConfigs" : {}
        }
    },
    /* 快应用特有相关 */
    "quickapp" : {},
    /* 小程序特有相关 */
    "mp-weixin" : {
        "appid" : "wx9db5737bff3ab31a",
        "setting" : {
            "urlCheck" : false,
            "minified" : true,
            "postcss" : true
        },
        "usingComponents" : true,
        "style" : "v2",
        "requiredPrivateInfos" : [ "getLocation" ],
        "permission" : {
            "scope.userLocation" : {
                "desc" : "你的位置信息将用于定位打卡"
            }
        },
        "lazyCodeLoading" : "requiredComponents",
		"mergeVirtualHostAttributes":true,
		"optimization" : {
		    "subPackages" : true
		}
    },
    // "renderer": "skyline",
    // "componentFramework": "glass-easel"
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "uniStatistics" : {
        "enable" : false
    },
    "vueVersion" : "3",
    "h5" : {
        "title" : "金米龙",
        "sdkConfigs" : {
            // 使用地图或位置相关功能必须填写其一
            "maps" : {
                "qqmap" : {
                    // 腾讯地图秘钥 https://lbs.qq.com/dev/console/key/manage
                    "key" : ""
                },
                "google" : {
                    // 谷歌地图秘钥（HBuilderX 3.2.10+）https://developers.google.com/maps/documentation/javascript/get-api-key
                    "key" : ""
                },
                "amap" : {
                    // 高德地图秘钥（HBuilderX 3.6.0+）https://console.amap.com/dev/key/app
                    "key" : "",
                    // 高德地图安全密钥（HBuilderX 3.6.0+）https://console.amap.com/dev/key/app
                    "securityJsCode" : "",
                    // 高德地图安全密钥代理服务器地址（HBuilderX 3.6.0+）https://lbs.amap.com/api/jsapi-v2/guide/abc/prepare
                    "serviceHost" : ""
                },
                "bmap" : {
                    // 百度地图秘钥（HBuilderX 3.99+）http://lbsyun.baidu.com/apiconsole/key#/home
                    "key" : "zLwD9giQfEWLsfg03ZDR2uEpVpDg2V4P"
                }
            }
        },
        "devServer" : {
            "https" : false
        }
    }
}
