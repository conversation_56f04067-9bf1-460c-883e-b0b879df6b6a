import { defineStore } from 'pinia'
import { getAllMenu,userInfo} from '@/api/common'
import {byCode,staffLogin}  from  '@/api/user';
import {factoryMenu}  from  '@/api/home';

export const useUserStore = defineStore('userStore', {
    state: () => {
        return {
            token: '你的token',
			//#ifndef H5
            userId: null,
			//#endif
			//#ifdef H5
			userId: import.meta.env.VUE_RUN_ENV=='local' ? import.meta.env.VUE_APP_TEST_USER_ID : null,
			//#endif
            menuList:null,
			factoryMenuList:null,
			factoryMenuObj : {},
            userInfo:null,
			miniInfo:null,
			openid:null,
			isCheck:false,
        }
    },
    actions: {
        async getUserRole (reload = false) {
            return new Promise((resolve, reject) => {
                if(this.menuList && !reload){
                    resolve(this.menuList)
                }else{
                    getAllMenu().then(res => {
                        const { data } = res
                        this.menuList = data;
                        resolve(data)
                    }).catch(error => {
                        reject(error)
                    })
                }
            })
        },
		async getFactoryMenuList (reload = false) {
		    return new Promise((resolve, reject) => {
		        if(this.factoryMenuList && !reload){
		            resolve(this.factoryMenuList)
		        }else{
		            factoryMenu().then(res => {
		                const { data } = res
		                this.factoryMenuList = data;
						this.factoryMenuObj = data.reduce((res, item) => (res[item.menuUrl] = item, res), {});
		                resolve(data)
		            }).catch(error => {
		                reject(error)
		            })
		        }
		    })
		},
		getMiniUserInfo () {
			let that = this;
		    return new Promise((resolve, reject) => {
		        if(this.miniInfo){
		            resolve(this.miniInfo)
		        }else{
					//#ifdef MP-WEIXIN
					uni.login({
						provider: 'weixin', //使用微信登录
						success: function (loginRes) {
							byCode(loginRes.code).then(res => {
							    const { data } = res
							    that.miniInfo = data;
								that.openid = data.wxAppInfo.openid
								if(data.userInfo){
									that.userInfo = data.userInfo;
									that.userId = data.userInfo.id
									that.token = data.userInfo.userid
									uni.showToast({ title: '登录成功', icon: 'none' })
								}
							    resolve(that.miniInfo)
							}).catch(error => {
							    reject(error)
							})
							
						}
					});	
					//#endif
					//#ifndef MP-WEIXIN
						uni.showToast({ title: '该平台未开放...', icon: 'none' })
						resolve({})
					//#endif
		            
		        }
		    })
		},
		userLogin (postData) {
			return new Promise((resolve, reject) => {
				let that = this;
				if(this.userInfo){
					resolve(this.userInfo)
				}else{
					staffLogin(postData).then((res)=>{
						const { data } = res
						this.userInfo = data;
						this.userId = data.id
						this.token = data.userid
						uni.showToast({ title: '登录成功', icon: 'none' })
						resolve(data)
					}).catch(error => {
						// uni.showToast({ title: '登录失败', icon: 'none' })
						reject(error)
					})
				}
			})	
		},
		userLogout () {
			return new Promise((resolve, reject) => {
				let that = this;
				if(that.userInfo){
					that.userInfo = null;
					that.userId = null;
					that.token = null;
					that.miniInfo = null;
					that.openid = null
					this.menuList = null;
				}
				resolve(true)
			})	
		},
		setData(key,payload) {
		  this[key] = payload;
		},
    },
    getters: {},
    persist: {
        // 开启持久化
        enabled: true,
    	// 默认值，就是使用localStorage
    	        // storage: localStorage
    	        // 重写存取方法，转调给UniApp
    	storage: {
    		getItem(key) {
    			return uni.getStorageSync(key)
    		},
    		setItem(key, val) {
    			uni.setStorageSync(key, val)
    		}
    	}
    }
})

export default useUserStore