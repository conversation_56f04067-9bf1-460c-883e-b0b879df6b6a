import { defineStore } from 'pinia'
import { loadConf} from '@/api/common'
export const useAppStore = defineStore('appStore', {
    state: () => {
        return {
            title: '',
            wechatConfig:null,
            keepAliveExcludes :[]//存在这里面的 都不会缓存
        }
    },
    actions: {
        async getWechatConfig() {
            return new Promise((resolve, reject) => {
                if(this.wechatConfig){
                    resolve(this.wechatConfig)
                }else{
                    loadConf().then(res => {
                        const { data } = res
                        this.wechatConfig = data;
                        resolve(data)
                    }).catch(error => {
                        reject(error)
                    })
                }
            })
        },
        resetKeepAliveCache(name) {
            this.keepAliveExcludes = this.keepAliveExcludes.filter((item) => item !== name);
        },
        removeKeepAliveCache(name) {
            this.keepAliveExcludes.push(name);
        },
        clearAllExcludes(){
            this.keepAliveExcludes = [];
        },
        resetBatchExcludes(arr){
            this.keepAliveExcludes = [...this.keepAliveExcludes,...arr];
        }
    },
    getters: {},
    persist: {
        // 开启持久化
        enabled: true,
		// 默认值，就是使用localStorage
		        // storage: localStorage
		        // 重写存取方法，转调给UniApp
		storage: {
			getItem(key) {
				return uni.getStorageSync(key)
			},
			setItem(key, val) {
				uni.setStorageSync(key, val)
			}
		}
    }
})

export default useAppStore