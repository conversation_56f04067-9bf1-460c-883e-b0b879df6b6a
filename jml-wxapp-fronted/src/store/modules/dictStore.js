import { defineStore } from 'pinia'
import { getDictList, getDictBatch,getRegionTree,dictTree} from '@/api/common'
export const useDictStore = defineStore('dictStore', {
    state: () => ({
        dict: {},
		dictTree: {},
        area:[],
    }),
    actions: {
        hasDict(key){
            return Reflect.has(this.dict,key) && this.dict[key].length>0
        },
        //单个获取
        async getDict (key) {
            return new Promise((resolve, reject) => {
                if(this.dict[key]){
                    resolve(this.dict[key])
                }else{
                    getDictList(key).then(response => {
                        const { data } = response
                        this.dict[key] = data;
                        resolve(data)
                    }).catch(error => {
                    reject(error)
                    })
                }
            })
        },
		//单个获取
		async getDictTree (key) {
		    return new Promise((resolve, reject) => {
		        if(this.dictTree[key]){
		            resolve(this.dictTree[key])
		        }else{
		            dictTree(key).then(response => {
		                const { data } = response
		                this.dictTree[key] = data;
		                resolve(data)
		            }).catch(error => {
		            reject(error)
		            })
		        }
		    })
		},
        //批量获取
        async getDictBatch(keysArr = []){
            return new Promise((resolve, reject) => {
                let res = {},none =[]
                keysArr.forEach(i => {
                    if(this.dict[i]) res[i]=this.dict[i]
                    else none.push(i)
                });
                if(none.length>0){
                    getDictBatch(none.join(',')).then(response => {
                        const { data } = response
                        this.dict = {...this.dict,...data}
                        resolve(this.dict)
                    }).catch(error => {
                        reject(error)
                    })
                }else{
                    resolve(res)
                }
            })
        },
        //省市数据
        async getArea (key) {
            return new Promise((resolve, reject) => {
                if(this.area && this.area.length>0){
                    resolve(this.area)
                }else{
                    getRegionTree().then(response => {
                        const { data } = response
						this.area = data;
                        // this.area = data.map(itme=>{
                        //     return{
                        //         text: itme.rname, value: itme.rname,children:itme.children.map(
                        //             childItem =>{
                        //                 return {text: childItem.rname, value: childItem.rname,isleaf:true}
                        //             }
                        //         )
                        //     }
                        // });
                        resolve(this.area)
                    }).catch(error => {
                    reject(error)
                    })
                }
            })
        },
    },
    getters: {},
    persist: {
        enabled: true,
		storage: {
			getItem(key) {
				return uni.getStorageSync(key)
			},
			setItem(key, val) {
				uni.setStorageSync(key, val)
			}
		}
    }
})

export default useDictStore