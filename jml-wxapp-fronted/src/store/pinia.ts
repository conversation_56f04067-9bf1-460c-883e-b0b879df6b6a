import { createPinia } from 'pinia'
// import piniaPluginPersist from 'pinia-plugin-persist'//数据持久化
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
const pinia = createPinia()
console.log(pinia)
pinia.use(piniaPluginPersistedstate)
export default pinia

// /**
//  * @description 自定义pinia持久化api储存方式为 unisetStorageSync
//  * @returns 
//  */
// function customStorage() {
//   return createPersistedState({
//     storage: {
//       getItem(key: string) {
//         return uni.getStorageSync(key)
//       },
//       setItem(key: string, value: any) {
//         uni.setStorageSync(key, value)
//       }
//     }
//   })
// }
