//
//                  ___====-_  _-====___
//            _--^^^#####//      \\#####^^^--_
//         _-^##########// (    ) \\##########^-_
//        -############//  |\^^/|  \\############-
//      _/############//   (@::@)   \############\_
//     /#############((     \\//     ))#############\
//    -###############\\    (oo)    //###############-
//   -#################\\  / VV \  //#################-
//  -###################\\/      \//###################-
// _#/|##########/\######(   /\   )######/\##########|\#_
// |/ |#/\#/\#/\/  \#/\##\  |  |  /##/\#/  \/\#/\#/\#| \|
// `  |/  V  V  `   V  \#\| |  | |/#/  V   '  V  V  \|  '
//    `   `  `      `   / | |  | | \   '      '  '   '
//                     (  | |  | |  )
//                    __\ | |  | | /__
//                   (vvv(VVV)(VVV)vvv)
//
//     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
//
//               神兽保佑            永无BUG


<template>
	<factory-user-home v-if="userInfo?.isFactory==1"></factory-user-home>
	<user-home v-else></user-home>
</template>

<script setup lang="ts">
	import factoryUserHome from '@/pages/factoryUserHome.vue';
	import userHome from '@/pages/userHome.vue';
	onShow(()=>{
		console.log('onShow')
		uni.$off()
	})
    onMounted(() => {
		
    });
</script>

<style lang="scss">
@import '@/styles/home.scss';
</style>