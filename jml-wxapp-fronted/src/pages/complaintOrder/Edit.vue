<template>
    <div class="page pd100" v-if="routerQuery.case_id || routerQuery.id">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">客诉订单明细</div>
                </div>
                <van-field  v-model="info.buy_date"  label="购买日期"  @click="dateShow = true"  required readonly  placeholder="购买日期"  label-align="top" class="int" />
                <van-field  v-model="info.shipping_address"  label="发货地址"  required   placeholder="发货地址"  label-align="top" class="int" />
                <van-field  v-model="info.order_no"  label="订单号"  required   placeholder="订单号"  label-align="top" class="int" />
                <van-field  v-model="info.product_spec"  label="产品名称及规格"  required   placeholder="产品名称及规格"  label-align="top" class="int" />
                <van-field  type="number" v-model="info.buy_qty"  label="购买数量"   required   placeholder="购买数量"  label-align="top" class="int" />
                <van-field  type="number" v-model="info.bad_qty"  label="发现不良品数量"  required   placeholder="发现不良品数量"  label-align="top" class="int" />
                <van-field  v-model="info.bad_remark"  label="不良品状态描述"  required   placeholder="不良品状态描述"  label-align="top" class="int" />
                <!-- <van-field  v-model="info.remark"  label="备注"   maxlength="150" show-word-limit placeholder="备注" type="textarea"  label-align="top" class="int" /> -->
            </div>
        </div>

        <!-- <page-tip :subtotal="data.list.length" :total="data.totalRow"></page-tip> -->
        <footer-btn :cancelBtnShow="false"  confirm_btn_text="保存" @onconfirm="save()"></footer-btn>
        <floating-window></floating-window>
        <van-calendar v-model:show="dateShow" :show-confirm="false"
            :min-date="new Date(2010, 0, 1)"
            :max-date="new Date(2050, 0, 1)"
            :default-date="info.buy_date?dayjs(info.buy_date).toDate():dayjs().toDate()"
            color="#849EB2"  @confirm="onDateConfirm" />
    </div>
    <div v-else class="page"></div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '../components/FloatingWindow.vue';
import FooterBtn from '../components/FooterBtn.vue';
import LoadSelector from '../components/LoadSelector.vue';
import { useRouter } from 'vue-router';
import {useDictStore} from '@/store/modules/dictStore'
import dayjs from "dayjs";
import { showDialog  } from 'vant';
import {saveCaseOrderDetails,queryCaseOrderDetails}  from  '@/api/complaint'
import {ElMessage} from "element-plus";
    const router = useRouter();
    const routerQuery =  router.currentRoute.value.query;
    if(!routerQuery.case_id && !routerQuery.id){
        ElMessage.error('参数错误');
    }
    const meta =  router.currentRoute.value.meta
    const pageType = routerQuery.id?'编辑':'新建'
    document.title = `${pageType}${meta.title}`

    // 相关字典
    const dictStore = useDictStore()
    const calendarShow = ref(false)

	const util = inject("util");
    const deleteFild = ['updateStaffName','createdStaffName','caseSubject'];
    const save = () => {
        let postData = {}
        for(var key in info.value){
            if(!deleteFild.includes(key))
            postData["caseOrderDetail." + key] = info.value[key];
        }
        saveCaseOrderDetails(postData).then((res)=>{
            showDialog ({
                title:'提示',
                className:'dialog',
                message: info.value.id ?'修改成功':'保存成功',
            }).then(() => {
                router.back()
            })
        })
    }
    const getInfo = () =>{
        queryCaseOrderDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const info = ref({
        case_id:routerQuery.case_id,
    });
    const init = async() =>{
        // await dictStore.getDictBatch(['marketing_member_status','marketing_status']);
        if(routerQuery.id){
            getInfo()
        }
    }
    const onCalendarConfirm = (values) => {
        const [start, end] = values;
        calendarShow.value = false;
        info.value.start_date = `${dayjs(start).format('YYYY-MM-DD')}`;
        info.value.end_date = `${dayjs(end).format('YYYY-MM-DD')}`;
    };
    const dateShow = ref(false)
    const onDateConfirm = (value) => {
        info.value.buy_date = `${dayjs(value).format('YYYY-MM-DD')}`;
        // popInfo.value[calendarType.value] = `${currentDate.value.join('-')} ${currentTime.value.join(':')}`
        dateShow.value = false
    };
    onMounted(() => {
        init()
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
</style>