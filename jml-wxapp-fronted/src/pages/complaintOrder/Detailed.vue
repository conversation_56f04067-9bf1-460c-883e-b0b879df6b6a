<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        客诉订单明细
                        {{ info.basic.name }}
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">订单编号</div><div class="rigth">{{info.basic.order_no}}</div>
                    </div>
                    <div class="item">
                        <div class="left">客诉主题</div>
                        <div class="rigth">
                            <span @click="router.push({name: 'complaintInfo',params: {id:info.basic.case_id}})" class="link">
                                {{info.basic.caseSubject}}
                            </span>
                        </div>
                    </div>
                    <div class="item">
                        <div class="left">产品名称及规格</div><div class="rigth">{{info.basic.product_spec}}</div>
                    </div>
                    <div class="item">
                        <div class="left">购买数量</div><div class="rigth">{{info.basic.buy_qty}}</div>
                    </div>
                    <div class="item">
                        <div class="left">购买日期</div><div class="rigth">{{info.basic.buy_date}}</div>
                    </div>
                    <div class="item">
                        <div class="left">发现不良品数</div><div class="rigth">{{info.basic.bad_qty}}</div>
                    </div>
                    <div class="item">
                        <div class="left">不良品状态描述</div><div class="rigth">{{info.basic.bad_remark}}</div>
                    </div>
                </div>

                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.basic.created_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.basic.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.basic.update_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.basic.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>
        <footer-btn  v-has="{ menu_code: 'Marketing', function_code: 'U' }" :cancelBtnShow="false" @onconfirm="router.push({name: 'complaintOrderEdit',params: {id:routerQuery.id}})" confirm_btn_text="编辑"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '../components/FloatingWindow.vue';
import FooterBtn from '../components/FooterBtn.vue';
import { useRouter } from 'vue-router';
import {queryCaseOrderDetails}  from  '@/api/complaint'
    const router = useRouter();
	const util = inject("util");
    const routerQuery =  router.currentRoute.value.query;
    const info = ref({
        basic:{},
        relatedList:[],
    });
    const getInfo = () =>{
        queryCaseOrderDetails(routerQuery.id).then((res) => {
            info.value.basic = res.data
        }).catch((err) => {
        });
    }
    const init = () =>{
        getInfo()
    }
    onMounted(() => {
        init()
    })
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
</style>