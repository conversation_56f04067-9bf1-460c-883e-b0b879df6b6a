<template>
    <div class="page">
        <div class="head">
            <!-- <div class="qty">共{{data.list.length}}条</div> -->
            <div class="search">
                <van-search
                    v-model="searchQuery.kw"
                    placeholder="姓名/手机/经销商"
                    right-icon=""
                    left-icon="search"
                    show-action
                    @update:model-value="util.throttle(onSearch)"
                >
                <template #action>
                    <div @click="util.throttle(onSearch)">搜索</div>
                </template>
                </van-search>
            </div>
            <!-- <div class="search_btn">搜索</div> -->
        </div>

        <div ref="card_list" class="card_list">

            <div class="tool">
                <div class="item">业务员 <van-icon size="6"  name="/assets/icon/sel_icon.png" /></div>
                <div class="item" @click="show= true">日期：<van-icon size="6"  name="/assets/icon/sel_icon.png" /></div>
            </div>

            <van-tabs  shrink title-inactive-color="#373D5C" title-active-color="#849EB2" background="none" type="card">
                <van-tab title="标签 1">
                    <div ref="table" class="">
                        <el-table  :data="tableData" stripe :height="setTable" style="width: 100%">
                            <el-table-column fixed prop="date" label="姓名" min-width="120" />
                            <el-table-column prop="name" label="5月27" min-width="120" />
                            <el-table-column prop="state" label="5月27" min-width="120" />
                            <el-table-column prop="city" label="5月27" min-width="120" />
                            <el-table-column prop="address" label="5月27" min-width="120"/>
                            <el-table-column prop="zip" label="5月27" min-width="120" />
                        </el-table>
                    </div>
                </van-tab>
                <van-tab title="标签 2"></van-tab>
            </van-tabs>
        </div>
        <!-- <page-tip :subtotal="data.list.length" :total="data.totalRow"></page-tip> -->
        <floating-window></floating-window>
        <van-popup
            v-model:show="showLeft"
            position="bottom"
            round
        >
            <van-picker-group
            title="选择日期"
            :tabs="['开始日期', '结束日期']"
            @confirm="onConfirm"
            @cancel="onCancel"
            >
                <van-date-picker
                    v-model="startDate"
                    :formatter="formatter"
                />
                <van-date-picker v-model="endDate" :min-date="minDate" :formatter="formatter" />
            </van-picker-group>
        </van-popup>
        <van-calendar v-model:show="show" color="#849EB2" confirm-disabled-text="请选择结束时间" type="range" @confirm="onCalendarConfirm" />
    </div>
</template>
<script setup>
	import FloatingWindow from '@/components/FloatingWindow.vue';
	import dayjs from "dayjs";
    const router = useRouter();
	const util = inject("util");
    const {data} = useDataList()
    const showLeft = ref(false)
    const show = ref(false)
    const onSearch = () => {
        initEmpty()
    }
    const onCancel = () =>{
    }
    const startDate = ref([]);
    const endDate = ref([]);

    const onConfirm = () => {
        let diffDay = dayjs(endDate.value.join('-')).diff(startDate.value.join('-'), "day");
        if(diffDay<0){
            return showToast('结束日期不能早于开始日期');
        }
    };
    const onCalendarConfirm = (values) => {
        const [start, end] = values;
        show.value = false;
        // debugger
        showToast(`${dayjs(start).format('YYYY-MM-DD')} - ${dayjs(end).format('YYYY-MM-DD')}`)
    };
    const searchQuery = reactive({
        kw:null,
        pageNo:data.pageNo,
        pageSize:data.pageSize,
    })
    const getDataPage = (pageNo) =>{
       if (data.finished || data.loading)return;
       searchQuery.pageNo = pageNo || data.pageNo;
       data.loading = true;
        queryComplaintPage(searchQuery).then((res) => {
            data.list = [...data.list, ...res.data.list]
            data.totalRow = res.data.totalRow
            data.pageNo = data.pageNo + 1 ;
            data.loading = false;
            data.finished = res.data.lastPage;
        }).catch((err) => {
            data.finished = true;
        });
    }
    const init = () =>{
        // getDataPage()
    }
    const initEmpty =() =>{
        data.initEmpty()
    }
    onMounted(() => {
      console.log("挂载");
    })

    const handleClick = () => {
        console.log('click')
    }
    const table = ref();
    const { width, height } = useWindowSize();
    watch([width, height], () => {
        setTable.value = height.value;
    });
    const clientHeight = ref(document.documentElement.clientHeight);
    const setTable = computed({
        get(){
            const rect = useRect(table);
            return clientHeight .value - rect.top - 10
        },
        set(value){
            clientHeight.value = value
        }
    })
    const formatter = (type, option) => {
      if (type === 'year') {
        option.text += '年';
      }
      if (type === 'month') {
        option.text += '月';
      }
      if (type === 'day') {
        option.text += '日';
      }
      return option;
    };
    // const head = [
    //     {'label':'姓名',fild:'ddd'},
    //     {'label':'2月21号',fild:'d221'},
    //     {'label':'2月23号',fild:'d223'}
    // ]
    const tableData = [
    {
        date: '2016-05-03',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-02',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },
    {
        date: '2016-05-04',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-01',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },{
        date: '2016-05-03',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-02',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },
    {
        date: '2016-05-04',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-01',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },{
        date: '2016-05-03',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-02',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },
    {
        date: '2016-05-04',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-01',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },{
        date: '2016-05-03',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-02',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },
    {
        date: '2016-05-04',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-01',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },{
        date: '2016-05-03',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-02',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },
    {
        date: '2016-05-04',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-01',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },{
        date: '2016-05-03',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-02',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },
    {
        date: '2016-05-04',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-01',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },{
        date: '2016-05-03',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-02',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },
    {
        date: '2016-05-04',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-01',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },{
        date: '2016-05-03',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-02',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },
    {
        date: '2016-05-04',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-01',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },{
        date: '2016-05-03',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-02',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },
    {
        date: '2016-05-04',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-01',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },{
        date: '2016-05-03',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-02',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },
    {
        date: '2016-05-04',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-01',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },{
        date: '2016-05-03',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-02',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },
    {
        date: '2016-05-04',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Home',
    },
    {
        date: '2016-05-01',
        name: 'Tom',
        state: 'California',
        city: 'Los Angeles',
        address: 'No. 189, Grove St, Los Angeles',
        zip: 'CA 90036',
        tag: 'Office',
    },
    ]
</script>
<style lang="scss" scoped>
    @import '@/styles/list.scss';
    .page{
        :deep(.van-popup){
            --van-padding-md:15px;
        }
        :deep(.el-table--striped){
            --el-table-border:0;
            --el-table-bg-color:none;
            .el-table__row{
                background: #F7F7F7;
                td{
                    background: #F7F7F7;
                }
                &.el-table__row--striped{
                    background: #fff;
                    td{
                        background: #fff;
                    }
                }
            }
        }
    }
</style>