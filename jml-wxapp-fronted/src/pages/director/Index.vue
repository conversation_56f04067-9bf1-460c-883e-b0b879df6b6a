<template>
    <div class="page">
       
        <u-sticky >
			<div style="background: #F5F6F8;">
				<div class="head">
				<!-- <div class="qty">共{{data.list.length}}条</div> -->
					<div class="search">
					    <u-search
					        v-model="searchQuery.multiFiled"
					        placeholder="请输入报告人姓名"
					        shape="square"
					        @change="util.debounce(onSearch)"
							@custom="util.debounce(onSearch)"
					    >
					    </u-search>
					</div>
					<!-- <div class="search_btn">搜索</div> -->
				</div>
				<div class="card_list" style="margin: 0; padding: 10px  10px 0 10px; height: auto !important;">
					<div class="card_tool">
						
						<view class="van-tabs">
							<view class="van-tab" :class="{'active':active_tabs=='AllReport' }" title="全部报告" @click="tabChange(1,'AllReport')">全部报告</view>
							<view class="van-tab" :class="{'active':active_tabs=='CustReprot' }" title="区域报告" @click="tabChange(2,'CustReprot')">区域报告</view>
							<view class="van-tab" :class="{'active':active_tabs=='SingReport' }" title="签到报告" @click="tabChange(3,'SingReport')">签到报告</view>
						</view>
						
						<div class="tool">
							<div @click="calendarShow= true" class="item">
								<template v-if="searchQuery.startDate">{{ `${searchQuery.startDate} - ${searchQuery.endDate}  `}}</template>
								<template v-else>报告日期：</template>
								<u-icon size="6"  name="/static/icon/sel_icon.png" />
							</div>
						</div>
					</div>
				</div>
			</div>
        </u-sticky>
        <div class="card_list" style="margin: 0; padding: 10px  10px 0 10px; height: auto !important;">
            <div ref="table" class="">
				<uni-table border stripe emptyText="暂无更多数据" >
					<!-- 表头行 -->
					<uni-tr  >
						<uni-th fixed  align="center" :width="80">姓名</uni-th>
						<uni-th v-for="(item,index) in data.dayList"  :key="index"  :width="100" align="center">{{item.label}}</uni-th>
					</uni-tr>
					<!-- 表格数据行 -->
					<uni-tr v-for="(info,i) in data.reportList" :key="i">
						<uni-td fixed  align="center">{{info.staffName}}</uni-td>
						<uni-td v-for="(item,index) in data.dayList" align="center"  :key="index">
							<view v-if="active_tabs=='SingReport' && info[item.field]>0" class="link" style="color: #849EB2; font-weight: bold;" @click="openiframe(item.field,info['staffId'])">{{info[item.field]}}</view>
							<view v-else>{{info[item.field]}}</view>
						</uni-td>
					</uni-tr>
				</uni-table>
				
				<!-- <lh-table :rows="state.rows" :columns="state.columns" :showStatistics="true" @tapRow="tapRow" @tapToolbar="tapToolbar" @tapOper="tapOper" @refresh="refresh" @check="check" @checkAll="checkAll" @loadMore="loadMore">
					<template #statistics="{row}">
						<view>{{ row.yj + row.amount }}</view>
					</template>
				</lh-table>
				
				<hd-table :dataSource="data.reportList" :stripe="true">
				  <hd-table-column  :fixed="true" prop="staffName" align="center" label="姓名" width="60"></hd-table-column>
				  <hd-table-column v-for="(item,index) in data.dayList" align="center" :key="index"  :prop="item.field" :label="item.label" width="80">
					  <template v-if="active_tabs=='SingReport'" #default="scope">
					      <span v-if="scope.row[item.field]>0" class="link" style="color: #849EB2; font-weight: bold;" @click="openiframe(item.field,scope.row['staffId'])">{{ scope.row[item.field] }} </span>
					      <span v-else>{{ scope.row[item.field] }} </span>
					  </template>
				  </hd-table-column>
				</hd-table> -->
						
                <!-- <el-table  :data="data.reportList" stripe  :header-cell-style="{textAlign: 'center'}" style="width: 100%">
                    <el-table-column fixed prop="staffName" align="center" label="姓名" min-width="60" />
                    <el-table-column v-for="(item,index) in data.dayList" align="center" :key="index"  :prop="item.field" :label="item.label" min-width="80" >
                        <template v-if="active_tabs==2" #default="scope">
                            <span v-if="scope.row[item.field]>0" class="link" style="color: #849EB2; font-weight: bold;" @click="openiframe(item.field,scope.row['staffId'])">{{ scope.row[item.field] }} </span>
                            <span v-else>{{ scope.row[item.field] }} </span>
                        </template>
                    </el-table-column>
                </el-table> -->
            </div>
        </div>

		<u-calendar v-model:show="calendarShow" color="#849EB2"
			monthNum = "24"
			:minDate="dayjs().add(-1, 'year').format('YYYY-MM-DD')"
			:maxDate="dayjs().add(1, 'days').format('YYYY-MM-DD')"
			confirm-disabled-text="请选择结束时间" mode="range"  round="10" 
			@confirm="onCalendarConfirm"
			closeOnClickOverlay
			@close="calendarShow=false" 
		/>
		

        <!-- <u-popup v-model:show="iframeShow"  closeable :style="{ height: '80%' }" round position="bottom"> -->
		<u-popup v-model:show="iframeShow" round="10" mode="bottom"  zIndex="998" closeable closeOnClickOverlay @close="iframeShow=false">
			 <div class="iframeAddInfo" :style="{ height: '80vh', position:'relative', }" > 
				<h3 class="iframe_tit">工作报告</h3>
				<div class="iframe_Info">
					<workreport  ref="iframeworkreport" :date="iframeDate" :createdStaffId="staffId" isComponent></workreport>
				</div>
			</div>	
        </u-popup>

        <floating-window>
            <!-- <div
                v-has="{ menu_code: 'Contact', function_code: 'U' }"
                @click="router.push({name: 'contactEdit'})"
                class="item add_btn"
            >
                <u-icon size="20"  name="/static/icon/add.png" />
            </div> -->
        </floating-window>
    </div>
</template>
<script setup>
import FloatingWindow from '@/components/FloatingWindow.vue';
import dayjs from "dayjs";
import {queryGmReport}  from  '@/api/workreport'
import workreport from '../workreport/Index.vue';
    const router = useRouter();
	const util = inject("util");
    const data = ref({});
    
	const state = reactive({
		columns: [
			{name: 'yjbl', title: '佣金比例'},
			{name: 'yj', title: '佣金', align: 'right'},
			{name: 'amount', title: '消费金额', align: 'right'},
			{name: 'user', title: '被邀请用户'},
			{name: 'date', title: '消费时间', formate: data => data},
		],
		rows: [
			{id: 0, yjbl: '10%', yj: 10, user: 666666, amount: 100, date: '2023-08-23 10:00:25'},
			{id: 1, yjbl: '10%', yj: 10, user: 666666, amount: 100, date: Date.now()},
		]
	})
	const tapOper = data => {
		console.log(data)
	}
	
	const tapRow = data => {
		console.log(data);
	}
	
	const scroll = e => {
		// console.log(e);
	}
	
	const loadMore = () => {
		// state.rows = state.rows.concat([
		// 	{id: 1, yjbl: '10%', yj: 10, user: 666666, amount: 100, date: Date.now()},
		// 	{id: 1, yjbl: '10%', yj: 10, user: 666666, amount: 100, date: Date.now()},
		// ])
	}
	
	const refresh = () => {
		// state.rows = [
		// 	{id: 0, yjbl: '10%', yj: 10, user: 666666, amount: 100, date: '2023-08-23 10:00:25'},
		// 	{id: 1, yjbl: '10%', yj: 10, user: 666666, amount: 100, date: Date.now()}
		// ]
	}
	
	const check = param => {
		state.rows[param.index] = param.data
	}
	
	const checkAll = rows => {
		state.rows = rows
	}
	
	const tapToolbar = btnName => {
		console.log(btnName);
	}
	
    const onSearch = () => {
        getDataPage()
    }
    const searchQuery = reactive({
        multiFiled:'',
        startDate:dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
        endDate:dayjs().subtract(0, 'day').format('YYYY-MM-DD'),
        isAccReport:1
    })
    const active_tabs = ref('AllReport')
	const tabChange = (title,name)=>{
	    searchQuery.isAccReport = title
		active_tabs.value = name
	}
    const getDataPage = () =>{
        queryGmReport(searchQuery).then((res) => {
            data.value = res.data
        }).catch((err) => {
        });
    }
    const init = () =>{
        getDataPage()
    }

    watch(() => active_tabs.value,(newValue, oldValue) => {
        getDataPage()
    });

    const calendarShow = ref(false)
	const onCalendarConfirm = (values) => {
		const start= values[0],end = values[values.length - 1];
	    calendarShow.value = false;
	    searchQuery.startDate = `${dayjs(start).format('YYYY-MM-DD')}`;
	    searchQuery.endDate = `${dayjs(end).format('YYYY-MM-DD')}`;
	    getDataPage()
	};

    const showPicker = ref(false)
    const table = ref();
	// const windowSize = ref({})
	// const windowResizeCallback = (res) => {
	// 	windowSize.value = res.size
	// }
	// uni.onWindowResize(windowResizeCallback)
	
    // watch([width, height], () => {
    //     setTable.value = height.value;
    // });
    // const clientHeight = ref(document.documentElement.clientHeight);
    // const setTable = computed({
    //     get(){
    //         const rect = useRect(table);
    //         return clientHeight .value - rect.top - 10
    //     },
    //     set(value){
    //         clientHeight.value = value
    //     }
    // })
    const iframeShow = ref(false)
    const iframeDate = ref(null)
    const iframeworkreport = ref(null)
    const staffId = ref(null)
    const openiframe =  (date,userId) =>{
        iframeDate.value = date
        staffId.value = userId
        iframeShow.value = true
        // iframeworkreport.value?.getDataPage()
    }
    onMounted(() => {
        init()
    })
	onUnmounted(()=>{
		// uni.offWindowResize(windowResizeCallback)
	})
</script>
<style lang="scss" scoped>
    @import '@/styles/list.scss';
    .page{
        :deep(.van-popup){
            --van-padding-md:15px;
        }
        :deep(){
            .van-sticky{
                background: #fff;
            }
        }
        :deep(.el-table){
            .cell{
                padding: 0 5px;
            }
        }
        :deep(.el-table--striped){
            --el-table-border:0;
            --el-table-bg-color:none;
            .el-table__row{
                background: #F7F7F7;
                td{
                    background: #F7F7F7;
                }
                &.el-table__row--striped{
                    background: #fff;
                    td{
                        background: #fff;
                    }
                }
            }
        }
	}
</style>
<style lang="scss">
	.van-tabs {
	    position: relative;
		display: flex;
		
		.van-tab{
		    background: none;
		    position: relative;
			display: flex;
			align-items: center;
			justify-content: center;
			box-sizing: border-box;
			padding: 0 var(--van-padding-base);
			color: var(--van-tab-text-color);
			font-size: var(--van-tab-font-size);
			line-height: var(--van-tab-line-height);
			cursor: pointer;
			border: 1px solid #D1D2D6;
			&.active{
				border-color: #849eb2;
				color: var(--van-tab-act-text-color);
				font-weight: 600;
			}
			&:first-child{
				border-radius: 4px 0 0 4px;
			}
			&:last-child{
				border-radius:0 4px 4px 0;
			}
		}
	}
</style>