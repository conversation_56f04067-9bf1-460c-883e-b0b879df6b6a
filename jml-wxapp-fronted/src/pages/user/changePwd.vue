<template>
	<view class="page">
		<view class="head">
			<view class="title">重置密码！</view>
			<image  class="logo" mode="widthFix" src="@/static/logo.png"/>
		</view>
		<view class="mian ">
			<view class="itme ">
				<view class="lable">新密码</view>
				<view class="inp">
					<input type="password" v-model="model.password" placeholder-style="font-size:26rpx;color:#D1D2D6;"
						class="inp_txt" placeholder="输入新密码" />
				</view>
			</view>
			<view class="itme">
				<view class="lable">确认密码</view>
				<view class="inp">
					<input type="password" v-model="model.confirmpwd"
						placeholder-style="font-size:26rpx;color:#D1D2D6;" class="inp_txt" placeholder="输入确认密码" />
				</view>
			</view>
			<!-- <button class="confirm-btn" open-type="getUserInfo" @getuserinfo="wxLogin">微信授权登录</button> -->
			<view @click="changePwd" class="btn_login" :class="{'isOK':isOK}">确认重置</view>
			<!-- <view class="resetpwd_btn">重置密码</view> -->
		</view>
				
	</view>
</template>
<script setup lang="ts">
	import {resetPwd}  from  '@/api/user';
	// import useUserStore from '@/store/modules/userStore';
	import { ref } from 'vue'
	let router = useRouter()
	const util = inject("util");
	// const userStore = useUserStore();
	const model = ref({
		password:'',
		confirmpwd:'',
	});
	// const systemInfo = uni.getSystemInfoSync();
	const changePwd = async()=>{
		if(!isOK.value) return false;
		if(model.value.password!==model.value.confirmpwd) return uni.showToast({ title: '两次密码不一致', icon: 'none' })
		resetPwd(model.value.password).then(()=>{
			router.replaceAll({ name: 'home' })
		})
	}
	// watch(
	// 	() => model.value,val => {
	// 		debugger
	// 		console.log(val)
	// 	},{ deep: true , immediate: false }
	// )
    const isOK = computed(() => {
        return model.value.password!=='' && model.value.confirmpwd!=='';
    })
</script>

<style lang="scss" >
	@import '@/styles/user.scss';
</style>


