<template>
	<view class="page">
		<view class="head">
			<view class="title">您好，欢迎登录！</view>
			<image  class="logo" mode="widthFix" src="@/static/logo.png"/>
		</view>
		<view class="mian ">
			<view class="itme ">
				<view class="lable">手机号</view>
				<view class="inp">
					<input type="text" v-model="model.mobile" placeholder-style="font-size:26rpx;color:#D1D2D6;"
						class="inp_txt" placeholder="请输入手机号" />
				</view>
			</view>
			<view class="itme">
				<view class="lable">密码</view>
				<view class="inp">
					<input type="password" v-model="model.password"
						placeholder-style="font-size:26rpx;color:#D1D2D6;" class="inp_txt" placeholder="请输入密码" />
				</view>
			</view>
			<!-- <button class="confirm-btn" open-type="getUserInfo" @getuserinfo="wxLogin">微信授权登录</button> -->
			<view @click="login" open-type="getUserInfo" class="btn_login" :class="{'isOK':isOK}">立即登录</view>
			<!-- <view class="resetpwd_btn">重置密码</view>			 -->
		</view>
	</view>
</template>
<script setup lang="ts">
	import FloatingWindow from '@/components/FloatingWindow.vue';
	import { ref } from 'vue'
	let router = useRouter()
	const util = inject("util");
	const userStore = useUserStore();

	const model = ref({
		mobile:'',
		password:'',
	});
	const systemInfo = uni.getSystemInfoSync();
	const login = async()=>{
		if(!isOK.value) return false;
		const miniUserInfo = await userStore.getMiniUserInfo();
		const user =  await userStore.userLogin({
			mobile:model.value.mobile,
			pwd:model.value.password,
			openId:miniUserInfo.wxAppInfo.openid,
			unionId:miniUserInfo.wxAppInfo.unionId,
			userAgent: systemInfo.system,
			device:systemInfo.model,
		});
		if(user){
			router.replaceAll({ name: 'home' })
		}
	}
    const isOK = computed(() => {
        return model.value.mobile!='' && model.value.password!='' && util.test.mobile(model.value.mobile);
    })
</script>


<style lang="scss" >
	@import '@/styles/user.scss';
</style>

