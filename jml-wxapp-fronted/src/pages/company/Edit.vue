<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">公司信息</div>
                </div>
				<u--form labelPosition="top" labelWidth="auto" ref="form1" >
					<u-form-item label="地区" prop="info.district" required>
						<zxz-uni-data-select clearable placeholder="请选择"  v-model="info.district" dataText="district" @change="areaChange" dataValue="district"  :localdata="userInfo.staffAreaList"></zxz-uni-data-select>
					</u-form-item>
					
					<u-form-item label="公司名称" prop="info.name" required 	ref="item1">
						<u--input  v-model="info.name"  label="公司名称"  class="int"   placeholder="请输入公司名称"  />
					</u-form-item>
					<u-form-item label="请选择省份/城市" prop="info.type" required    ref="item1">
						<uni-data-picker placeholder="请选择地址" popup-title="请选择城市"
							v-slot:default="{data, error, options}"
							:map="{text:'rname',value:'code'}"
							:localdata="options"
							@change="areaConfirm"
						 >
							<!-- <view v-if="error" class="error showarea">
							  <text>{{error}}</text>
							</view> -->
							<view v-if="info.province" class="selected showarea">
								 <text>{{ info.province  }}{{ info.city }}</text>
							</view>
							<view v-else class="showarea">
								<text class="readonly">请选择省份/城市</text>
							</view>
						</uni-data-picker>
					</u-form-item>
					<u-form-item label="代理商" prop="info.agent_account_id">
						<load-selector
						 v-model="info.agent_account_id" filterable  load_url="/api/account/queryListByStaff?type=1"></load-selector>
					</u-form-item>
					<u-form-item label="公司地址" prop="info.address" required>
						<u--input  v-model="info.address"   class="int"   placeholder="请输入公司地址"  />
					</u-form-item>
					<u-form-item label="电话" prop="info.phone" required>
						<u--input  v-model="info.phone"   class="int"   placeholder="请输入电话"  />
					</u-form-item>
					<u-form-item label="网址" prop="info.website">
						<u--input  v-model="info.website"  class="int"   placeholder="请输入网址"  />
					</u-form-item>
					<u-form-item label="类型" prop="info.type" required>
						<zxz-uni-data-select clearable filterable placeholder="请选择"  v-model="info.type" dataText="dval" dataValue="dname"  :localdata="dict['account_type']"></zxz-uni-data-select>
					</u-form-item>
					<u-form-item label="公司性质" prop="info.company_business" required>
						<zxz-uni-data-select clearable filterable placeholder="请选择"  v-model="info.company_business" dataText="dval" dataValue="dname" :localdata="dict['account_business']"></zxz-uni-data-select>
					</u-form-item>
					<u-form-item label="是否有效客户" prop="info.if_validated_dealer" required>
						<u-radio-group v-model="info.if_validated_dealer" activeColor="#849EB2" iconPlacement="left">
						    <u-radio :customStyle="{marginRight: '16px'}"  label="是" name="1"></u-radio>
						    <u-radio  label="否" name="0"></u-radio>
						</u-radio-group>
					</u-form-item>
					<u-form-item label="客户有效无效备注信息" prop="info.validation_note">
						<u--textarea :count="!!info.validation_note"  maxlength="800"  v-model="info.validation_note"  class="int"   placeholder="客户有效无效备注信息"  />
					</u-form-item>					
					<u-form-item label="经销商代理有效期" prop="dealer_date_dl"  @click="onCalendarShow('dealer_date_dl')">
						<u--input
							v-model="info.dealer_date_dl"
							placeholder="经销商代理有效期"
							readonly
							disabled
							disabledColor="#ffffff"
							border="none"
							style="width: 100%;pointer-events: none;"
						/>
						<template #right><u-icon name="calendar" @click="onCalendarShow('dealer_date_dl')"></u-icon></template>
					</u-form-item>
					
					<u-form-item label="母公司" prop="info.parent_id">
						<zxz-uni-data-select clearable filterable placeholder="请选择"  v-model="info.parent_id" dataText="name" dataValue="id"  :localdata="companyList"></zxz-uni-data-select>
					</u-form-item>
					<u-form-item label="销售额" prop="info.sales_revenue">
						<u--input  v-model="info.sales_revenue"   class="int"   placeholder="请输入销售额"  />
					</u-form-item>
					<u-form-item label="销售额年份" prop="info.sales_revenue_year">
						<u--input  v-model="info.sales_revenue_year"   class="int"   placeholder="请输入销售额年份"  />
					</u-form-item>
					<u-form-item label="税号" prop="info.tax_number">
						<u--input  v-model="info.tax_number"   class="int"   placeholder="请输入税号"  />
					</u-form-item>
					
					<u-form-item label="法人" prop="info.legal_entity">
						<u--input  v-model="info.legal_entity"   class="int"   placeholder="请输入法人"  />
					</u-form-item>
					<u-form-item label="员工人数" prop="info.staff_qty" >
						<u--input  v-model="info.staff_qty"   class="int"   placeholder="请输入员工人数"  />
					</u-form-item>
					<u-form-item label="公司占地面积" prop="info.land" >
						<u--input  v-model="info.land"   class="int"   placeholder="请输入公司占地面积"  />
					</u-form-item>
					<u-form-item label="公司规模" prop="info.scale" >
						<piaoyi-select clearable filterable placeholder="公司规模" v-model="info.scale" :options="dict['account_scale']"></piaoyi-select>
					</u-form-item>
					<u-form-item label="客户产品" prop="info.customer_product" required>
						<zxz-uni-data-select clearable  multiple placeholder="请选择"  v-model="info.customerProductList" dataText="dval" dataValue="dname"  :localdata="dict['account_product']"></zxz-uni-data-select>
					</u-form-item>
					<u-form-item label="仓库数量" prop="info.warehouse_qty" >
						<u--input  v-model="info.warehouse_qty"   class="int"   placeholder="仓库数量"  />
					</u-form-item>
					<u-form-item label="门店数量" prop="info.store_qty" >
						<u--input  v-model="info.store_qty"   class="int"   placeholder="门店数量"  />
					</u-form-item>
					<u-form-item label="股票代码" prop="info.stock_code" >
						<u--input  v-model="info.stock_code"   class="int"   placeholder="股票代码"  />
					</u-form-item>
					<u-form-item label="ERP代码" prop="info.erp_code" >
						<u--input  v-model="info.erp_code"   class="int"   placeholder="ERP代码"  />
					</u-form-item>
					<u-form-item label="备注" prop="info.remark" >
						<u--textarea :count="!!info.remark" maxlength="800"  v-model="info.remark"  class="int"   placeholder="备注"  />
					</u-form-item>
				</u--form>
            </div>
        </div>
        <footer-btn :cancelBtnShow="false" v-if="isEdit=='1'" confirm_btn_text="保存" @onconfirm="save()"></footer-btn>
        <floating-window></floating-window>
		<!-- 这个日历太卡了 -->
		<!-- <up-calendar :show="calendarShow" round="20"  color="#849EB2" 
			:defaultDate="defaultDate" monthNum = "100" 
			closeOnClickOverlay 
			minDate="2020-01-01" maxDate="2050-01-01" 
			@confirm="onCalendarConfirm" 
			@close="calendarShow=false" >
		</up-calendar> -->
		
		
		<u-datetime-picker
			:show="calendarShow"
			v-model="currentTime"
			confirmColor="#849EB2"
			closeOnClickOverlay
			@confirm="onCalendarConfirm"
			@cancel="calendarShow = false"
			@close="calendarShow = false"
			mode="date"
		></u-datetime-picker>
	</div>
</template>
<script setup>
import FloatingWindow from '@/components/FloatingWindow.vue';
import LoadSelector from '@/components/LoadSelector.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import dayjs from "dayjs";
import {saveAccount,queryAccountDetails,queryListByStaff}  from  '@/api/company'
	const prop =  defineProps({
		id: String,
		isComponent: {
			type: Boolean,
			default: false,
		},
	})
	const emit = defineEmits(['confirm'])
    const router = useRouter();
	const route = useRoute();
    const routerQuery = prop;// prop.isComponent ? {} : router.currentRoute.value.query;
    // 相关字典
    const dictStore = useDictStore()
    const cascaderShow = ref(false)
    const calendarShow = ref(false)
	const defaultDate = ref(`${dayjs().format('YYYY-MM-DD')}`);
    const companyList = ref([])
	const util = inject("util");
    const deleteFild = ['updateStaffName','createdStaffName','customerProductList','parentAccName','salesLeadName','agentAccName','areaName'];
    const requiredFild = [
		
		{key:'district',message:'请选择地区',required: true,},
        {key:'name',message:'请填写公司名称',required: true,},
        {key:'address',message:'请填写地址',required: true,},
        {key:'phone',message:'请填写电话',required: true,},
        {key:'type',message:'请填类型',required: true,},
        {key:'company_business',message:'请填写公司性质',required: true,},
        {key:'customer_product',message:'请选择客户产品',required: true,},
    ]
    const save = () => {
        info.value.customer_product = info.value.customerProductList ? info.value.customerProductList.join(';'):null
        for(let index  in requiredFild){ 
            var item =  requiredFild[index]
			
            if(item.required && (!info.value[item.key] || info.value[item.key]=='')){
				return uni.showToast({
					title: item.message,
					duration: 1500,
					icon:'none'
				});
				break;
			}
        };
        let postData = {}
        for(var key in info.value){
            if(!deleteFild.includes(key))
                postData["account." + key] = info.value[key];
        }
        saveAccount(postData).then((res)=>{
			uni.showModal({
				title: '提示',
				content: info.value.id ?'修改成功':'保存成功',
				showCancel:false,
				success: function (confirmres) {
					if (confirmres.confirm) {
						if(prop.isComponent)emit('confirm', res)
						else{
							uni.$emit("refresh", {refresh: true}); 
							router.back();
						}
					} else if (confirmres.cancel) {
						uni.$emit("refresh", {refresh: true}); 
						console.log('用户点击取消');
					}
				}
			});
        })
    }
	const isEdit=ref(routerQuery.id?0:1);
    const getInfo = () =>{
        queryAccountDetails(routerQuery.id).then((res) => {
            info.value = res.data.basic
            info.value.if_validated_dealer = (info.value.if_validated_dealer===0 || info.value.if_validated_dealer)? `${info.value.if_validated_dealer}`:null;
			isEdit.value = res.data?.isEdit
        }).catch((err) => {
        });
    }
	const { userInfo } = useUserStore();
    const info = ref({
		area_code:userInfo.staffAreaList.length==1 ? userInfo.staffAreaList[0].areaCode : null,
		district:userInfo.staffAreaList.length==1 ? userInfo.staffAreaList[0].district : null ,
	});
    const init = async() =>{
        await dictStore.getDictBatch(['account_type','account_business','account_scale','account_product']);
        options.value = await dictStore.getArea()
        let {data} = await queryListByStaff()
        companyList.value = data
        if(routerQuery.id){
            getInfo()
        }
    }
    const cascaderValue = ref('');
    const options = ref([]);
    const areaConfirm = (selectedValues) => {
        // cascaderShow.value = false;
    	let selectedOptions = selectedValues.detail.value;
        info.value.province =selectedOptions[0].text ;//selectedOptions.map((option) => option.text).join('/');
        info.value.city = selectedOptions[1].text
    };
  //   const onCalendarConfirm = (value) => {
		// calendarShow.value = false
  //       info.value.dealer_date_dl = `${dayjs(value).format('YYYY-MM-DD')}`;
  //   };
	const calendarType = ref(null)
	const currentTime = ref(Date.now());// ref([`${dayjs().format('HH')}`,`${dayjs().format('mm')}`]);
	const onCalendarShow  = (type,check = false) =>{
	    // if(check && routerQuery.id && info.checkin_address && (info.value[calendarType.value] && info.value[calendarType.value]!='')) return false
	    calendarType.value = type
	    calendarShow.value = true;
	}
	const onCalendarConfirm = ({mode,value}) => {
	    info.value[calendarType.value] = `${dayjs(value).format('YYYY-MM-DD')}`;
	    // info.value[calendarType.value] = `${currentDate.value.join('-')} ${currentTime.value.join(':')}`
	    calendarShow.value = false
	};
	
	const change=(e) => {
		console.log(info.value)
	    console.log(e) //这里返回的是value值
	};
	const hideKeyboard =() => {
		debugger
		// uni.hideKeyboard();
	}
	onLoad((option) => {
		if(!prop.isComponent){
			const title = (routerQuery.id?'编辑公司信息':'新建公司信息');
			uni.setNavigationBarTitle({
			　　title
			})
		}
		init()
	})
	const areaChange =(value)=>{
		info.value.area_code = userInfo.staffAreaList.find(i=>i.district===value)?.areaCode;
	}
    onMounted(() => {
    })
</script>
<style lang="scss" scoped>
	@import '@/styles/edit.scss';
</style>