<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        {{ info.basic.name }}
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
					<div class="item">
					    <div class="left">区域</div><div class="rigth">{{info.basic.areaName}}</div>
					</div>
					<div class="item">
					    <div class="left">地区</div><div class="rigth">{{info.basic.district}}</div>
					</div>
                    <div class="item">
                        <div class="left">公司地址</div><div class="rigth">
                            <span v-if="info.basic.latitude!='' && info.basic.longitude!=''" @click="openLocation(info.basic.latitude,info.basic.longitude,info.basic.name,info.basic.address)" class="link">
                                {{info.basic.address}}
                            </span>
                            <span v-else>{{info.basic.address}}</span>
                        </div>
                    </div>
                    <!-- <div class="item">
                        <div class="left">销售线索 </div>
                        <div class="rigth">
                            <span @click="router.push({name: 'leadInfo',params: {id:info.basic.sales_lead_id}})" class="link">
                                {{info.basic.salesLeadName}}
                            </span>
                        </div>
                    </div> -->
                    <div class="item">
                        <div class="left">省份</div><div class="rigth">{{info.basic.province}}</div>
                    </div>
                    <div class="item">
                        <div class="left">城市</div><div class="rigth">{{info.basic.city}}</div>
                    </div>
                    <div class="item">
                        <div class="left">电话</div><div class="rigth"><a :href="`tel:${ info.basic.phone }`">{{info.basic.phone}}</a></div>
                    </div>
                    <div class="item">
                        <div class="left">网址</div><div class="rigth">{{info.basic.website}}</div>
                    </div>
                    <div class="item">
                        <div class="left">类型</div><div class="rigth">{{info.basic.type}}</div>
                    </div>
                    <div class="item">
                        <div class="left">公司性质</div><div class="rigth">{{info.basic.company_business}}</div>
                    </div>
                    <div class="item">
                        <div class="left">是否有效客户</div>
                        <div class="rigth">{{info.basic.if_validated_dealer==1?'是':'否'}}</div>
                    </div>
                    <div class="item">
                        <div class="left">客户有效无效备注信息</div>
                        <div class="rigth">{{info.basic.validation_note}}</div>
                    </div>

                    <div class="item">
                        <div class="left">经销商代理有效期</div>
                        <div class="rigth">{{info.basic.dealer_date_dl}}</div>
                    </div>
                    <div class="item">
                        <div class="left">代理商 </div>
                        <div class="rigth">
                            <span @click="router.push({name: 'companyInfo',params: {id:info.basic.agent_account_id}})" class="link">
                                {{info.basic.agentAccName}}
                            </span>
                        </div>
                    </div>
                    <div class="item">
                        <div class="left">母公司 </div>
                        <div class="rigth">
                            <span @click="router.push({name: 'companyInfo',params: {id:info.basic.parent_id}})" class="link">
                                {{info.basic.parentAccName}}
                            </span>
                        </div>
                    </div>
                    <div class="item">
                        <div class="left">销售额</div><div class="rigth">{{info.basic.sales_revenue}}</div>
                    </div>
                    <div class="item">
                        <div class="left">销售额年份</div><div class="rigth">{{info.basic.sales_revenue_year}}</div>
                    </div>
                    <div class="item">
                        <div class="left">税号</div><div class="rigth">{{info.basic.tax_number}}</div>
                    </div>
                    <div class="item">
                        <div class="left">法人</div><div class="rigth">{{info.basic.legal_entity}}</div>
                    </div>
                    <div class="item">
                        <div class="left">员工人数</div><div class="rigth">{{info.basic.staff_qty}}</div>
                    </div>
                    <div class="item">
                        <div class="left">公司占地面积</div><div class="rigth">{{info.basic.land}}</div>
                    </div>
                    <div class="item">
                        <div class="left">客户产品 </div>
                        <div class="rigth">
                            <div class="tag_list">
                                <div v-for="(item,index) in info.basic.customerProductList" :key="index"  class="tag">
                                    <div class="tag_icon"><up-icon color="#fff" name="checkbox-mark"/></div> {{item}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="left">仓库数量</div><div class="rigth">{{info.basic.warehouse_qty}}</div>
                    </div>
                    <div class="item">
                        <div class="left">门店数量</div><div class="rigth">{{info.basic.store_qty}}</div>
                    </div>
                    <div class="item">
                        <div class="left">股票代码</div><div class="rigth">{{info.basic.stock_code}}</div>
                    </div>
                    <div class="item">
                        <div class="left">ERP编号</div><div class="rigth">{{info.basic.erp_code}}</div>
                    </div>
                    <div class="item">
                        <div class="left">备注</div><div class="rigth">{{info.basic.remark}}</div>
                    </div>
                </div>

                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.basic.created_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.basic.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.basic.update_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.basic.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>
        <related-list :data="info.relatedList" ref="related" :id="routerQuery.id" :baseInfo="info.basic" title="公司相关"></related-list>
        <footer-btn v-if="info.verify_status!='1' && hasPermission('Account','U') && info.isEdit==1 "  
			:cancelBtnShow="false" 
			@onconfirm="router.push({name: 'companyEdit',params: {id:routerQuery.id}})" confirm_btn_text="编辑">
		</footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import RelatedList from '@/components/RelatedList.vue';
import {queryAccountDetails}  from  '@/api/company'
	const prop =  defineProps(['id'])
	const router = useRouter();
	const util = inject("util");
	const routerQuery =  prop;
    const info = ref({
        basic:{},
        relatedList:[],
		isEdit:0,
    });
    const getInfo = () =>{
        queryAccountDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const openLocation = async(latitude,longitude,name,address) =>{
        uni.openLocation({
        	latitude: parseFloat(latitude),
        	longitude: parseFloat(longitude),
        	name: name,
        	address: address,
        	scale: 15,
        	success: function () {
        		console.log('success');
        	}
        });
    }
    
	const init = () =>{
        getInfo()
    }
	onLoad((option) => {
	})
    onMounted(() => {
        init()
    })
	const related = ref();
	onShow(() => {
		uni.$on("refresh", (data) => {
			related.value.reset();
			init()
		})
	});
	onUnload(()=>{
		uni.$off('refresh');
	})
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
</style>