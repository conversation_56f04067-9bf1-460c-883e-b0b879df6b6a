<template>
    <div class="page">
        <div class="head">
            <div class="qty"> 
				<uni-data-picker placeholder="请选择地址" popup-title="请选择城市"
					v-slot:default="{data, error, options}"
					:map="{text:'rname',value:'code'}"
					:localdata="dataTree"
					@change="areaConfirm"
				 >
					<view v-if="error" class="error">
					  <text>{{error}}</text>
					</view>
					<view v-else-if="data.length" class="selected">
					  <view v-for="(item,index) in data" :key="index" class="selected-item">
					    <text>{{item.text}}</text>
					  </view>
					</view>
					<view v-else>
					  <text>省份/城市</text>
					</view>
				</uni-data-picker>
			</div>
			 
            <div class="search">
                <u-search
                    v-model="searchQuery.multiFiled"
                    placeholder="公司名称"
                    shape="square"
                    @change="util.debounce(onSearch)"
					@custom="util.debounce(onSearch)"
                >
                </u-search>
            </div>
        </div>
        <div class="card_list" style="height: 100%;">
			<view class="swiper-item" style="height: 100%;">
				<!-- <scroll-view 
					@scrolltolower="lowerBottom" 
					refresher-enabled	 
					@refresherrefresh="getFresh" scroll-y="true" 
					:refresher-triggered="refresherTriggered"
					@refresherpulling="onPulling"
					class="scrollHeight"
					:enable-flex="true"
					style="height: 100%;"> -->		
				<scroll-view style="height:100%" scroll-y enable-flex  @scrolltolower="getDataPage()">	
					<uni-swipe-action>
						<div v-for="(item,index) in data.list" :key="index" class="van-swipe-cell" @click.stop="handDetail(item)">
							<uni-swipe-action-item >
						        <!-- <template #left>
						        </template> -->
						        <div class="card">
						            <div class="card_head">
						                <div class="card_head_tit">
						                    {{ item.name }}
						                </div>
						                <div v-if="item.is_new =='1'" class="head_status">
						                    <!-- <u-icon size="40" height=""  name="/static/icon/new.png" /> -->
											<u-image class="van-icon__image" width="40"  mode="widthFix" src="/static/icon/new.png" />
						                </div>
						            </div>
						            <u-divider></u-divider>
						            <div class="card_main">
						                <div class="item">
						                    <div class="left">地址</div>
						                    <div class="rigth">{{item.address}}</div>
						                </div>
						                <div class="item">
						                    <div class="left">电话</div>
						                    <div class="rigth">{{item.phone}}</div>
						                </div>
						                <div class="item">
						                    <div class="left">规模</div>
						                    <div class="rigth">{{item.scale}}</div>
						                </div>
						            </div>
						            <u-divider></u-divider>
						            <div class="card_footer">
						                <div class="item">
						                    <div class="left">创建时间</div>
						                    <div class="rigth">{{item.created_time}} <view class="link">查看 <u-icon name="arrow-right" size="12" /></view> </div>
						                </div>
						                <div class="item">
						                    <div class="left">创建员工</div>
						                    <div class="rigth">{{item.createdStaffName}}</div>
						                </div>
						            </div>
						        </div>
						        <template v-if="item.verify_status!='1'" #right>
						            <div class="card_right">
						                <div @click.stop="router.push({name: 'companyEdit',params: {id:item.id}})" class="card_right_btn">
						                    <u-icon size="20" name="/static/icon/edit.png" />
						                    <div class="txt">编辑</div>
						                </div>
						            </div>
						        </template>
						    </uni-swipe-action-item>
						</div>
					</uni-swipe-action>
					<div class="empty_box" v-if="!data.loading && (!data.list ||  data.list.length<=0)">
					    <u-empty v-if="!data.list || data.list.length==0" text="未找到信息" :icon="`${config.static}images/empty/data.png`" ></u-empty>
					</div>
					
				</scroll-view>
			</view>
			
        </div>
        <!-- <page-tip :subtotal="data.list.length" :total="data.totalRow"></page-tip> -->
        <floating-window>
            <view
				v-if="hasPermission('Account','C')" 
                @click="router.push({name:'companyEdit'})"
                class="item add_btn"
            >
				<u-icon size="20" name="/static/icon/add.png"></u-icon>
            </view>
        </floating-window>
    </div>

</template>
<script setup>
	import FloatingWindow from '@/components/FloatingWindow.vue';
	import { useDataList } from '@/vueUse/dataList'
	import {queryAccountPage}  from  '@/api/company'
	// 相关字典
    const dictStore = useDictStore()	
	const dataTree = ref([]);
    const router = useRouter();
	const util = inject("util");
    const {data} = useDataList();
	//触底加载
	const lowerBottom=()=>{
		console.log('lowerBottom');
	}
	// 下拉刷新
	const refresherTriggered = ref(false);
	const getFresh=()=> {
		console.log('refresh');
		refresherTriggered.value = 'restore'
		initEmpty()
	}
	const onPulling=(e)=> {
		console.log('onPulling');
		if (e.detail.deltaY < 0) return
	}
    const finishedText = computed(() => {
        return  data.list.length>0?'没有更多了':'';
    })
    const onSearch = () => {
        initEmpty()
    }
    const searchQuery = reactive({
        multiFiled:'',
        province:'',
        city:'',
        pageNo:data.pageNo,
        pageSize:data.pageSize,
    })
	
    const getDataPage = (pageNo) =>{
    	if (data.finished || data.loading)return;
    	searchQuery.pageNo = pageNo || data.pageNo;
    	data.loading = true;
        queryAccountPage(searchQuery).then((res) => {
			if(refresherTriggered.value !== false) refresherTriggered.value = false
            data.list = [...data.list, ...res.data.list]
            data.totalRow = res.data.totalRow
            data.pageNo = data.pageNo + 1 ;
            data.loading = false;
            data.finished = res.data.lastPage;
        }).catch((err) => {
            data.finished = true;
        });
    }
    const init = () =>{
        getDataPage()
    }
    const initEmpty =() =>{
        data.initEmpty()
		getDataPage(1)
    }
    //点击明细
    const handDetail =(item)=>{
        router.push({name: 'companyInfo',params: {id: item.id}})
    }
    const showArea = ref(false)
	
	const areaConfirm = (selectedValues) =>{
		const selectedOptions = selectedValues.detail.value
		if(selectedOptions[0].text!='请选择'){
		    searchQuery.province = selectedOptions[0].text
		    if(selectedOptions[1].text!='请选择') {
		        searchQuery.city = selectedOptions[1].text
		    }else{
		        searchQuery.city=''
		    }
			initEmpty()
		}else{
		    if(searchQuery.province!=''){
		        searchQuery.province='';
				initEmpty()
		    }
		}
	}
	onPullDownRefresh(() => {
		console.log('refresh');
	})
	onReachBottom(() => {
		console.log('onReachBottom')
		if(!data.finished) getDataPage();
	})
	onMounted( async() => {
		dataTree.value = await dictStore.getArea()
		init()
	})
	onReady(() => {
		console.log('onReady')
	})
	onShow(() => {
		uni.$once("refresh", (data) => {

			uni.$off('refresh');

			initEmpty()

		})
	})
</script>
<style lang="scss" scoped>
	@import '@/styles/list.scss';
	.selected{
		display: flex;
		.selected-item{
			margin-right: 5upx;
		}
	}
</style>