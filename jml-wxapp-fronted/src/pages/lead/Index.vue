<template>
    <div class="page">
		<div class="head">
		    <div class="search">
		        <u-search
		            v-model="searchQuery.multiFiled"
		            placeholder="线索名称/电话"
		            shape="square"
		            @change="util.debounce(onSearch)"
		    		@custom="util.debounce(onSearch)"
		        >
		        </u-search>
		    </div>
		</div>

        <div class="card_list" style="height: 100%;">
        		<scroll-view style="height:100%" scroll-y enable-flex  @scrolltolower="getDataPage()">
        			<uni-swipe-action>
						
            <div v-for="(item,index) in data.list" :key="index" class="van-swipe-cell"  @click="router.push({name: 'leadInfo',params: {id: item.id}})">
				
                <uni-swipe-action-item :disabled="item.is_converted ==1">
                    <template #left>
                    </template>
                    <div class="card">
                        <div class="card_head">
                            <div class="card_head_tit">
                                {{ item.name }}
                            </div>
                            <div v-if="item.is_converted ==1" class="head_status">
								<u-image class="van-icon__image" width="40"  mode="widthFix" src="/static/icon/converted.png" />
                            </div>
                        </div>
                        <u-divider></u-divider>
                        <div class="card_main">
                            <div class="item">
                                <div class="left">电话</div>
                                <div class="rigth">{{item.mobile_phone}}</div>
                            </div>
                            <div class="item">
                                <div class="left">公司</div>
                                <div class="rigth">{{item.company}}</div>
                            </div>
                            <div class="item">
                                <div class="left">创建时间</div>
                                <div class="rigth">{{item.created_time}}</div>
                            </div>
                            <div class="item">
                                <div class="left">创建员工</div>
                                <div class="rigth">{{item.createdStaffName}}</div>
                            </div>
                        </div>
                        <progress-status :readonly="item.is_converted && item.is_converted ==1" dataKey="dval" dataLabel="dname" :status_list="status_list" :active="item.status" @update:active="handChange($event,item)"></progress-status>
                    </div>
                    <template #right>
                        <div class="card_right">
                            <div @click.stop="router.push({name: 'leadEdit',params: {id:item.id}})" class="card_right_btn">
                                <u-icon size="20" name="/static/icon/edit.png" />
                                <div class="txt">编辑</div>
                            </div>
                        </div>
                    </template>
                </uni-swipe-action-item>
            </div>
            </uni-swipe-action>
            		<div class="empty_box" v-if="!data.loading && (!data.list ||  data.list.length<=0)">
            			<u-empty v-if="!data.list || data.list.length==0" text="未找到信息" :icon="`${config.static}images/empty/data.png`" ></u-empty>
            			<div 
            				v-if="hasPermission('Lead','C')"
            				class="btn_add" 
							@click="router.push({name:'leadEdit'})">
            				<u-icon size="20" name="/static/icon/btn_add_icon.png" />  <span>新建</span>
            			</div>
            		</div>
            	</scroll-view>
            </div>
        <!-- <page-tip :subtotal="data.list.length" :total="data.totalRow"></page-tip> -->
        <floating-window>
            <div
                v-if="hasPermission('Lead','C')"
				@click="router.push({name:'leadEdit'})"
                class="item add_btn"
            >
                <u-icon size="20" name="/static/icon/add.png"></u-icon>
            </div>
        </floating-window>
    </div>
</template>
<script setup>
import FloatingWindow from '@/components/FloatingWindow.vue';
import ProgressStatus from '@/components/ProgressStatus.vue';
import { useDataList } from '@/vueUse/dataList'
import {querySalesLeadPage,changeStatus}  from  '@/api/lead'
import {useDictStore} from '@/store/modules/dictStore'
    const router = useRouter();
    const util = inject("util");
    const {data} = useDataList()
    const dictStore = useDictStore()
	
    //触底加载
    const lowerBottom=()=>{
    	console.log('lowerBottom');
    }
    // 下拉刷新
    const refresherTriggered = ref(false);
    const getFresh=()=> {
    	console.log('refresh');
    	refresherTriggered.value = 'restore'
    	initEmpty()
    }
    const onPulling=(e)=> {
    	console.log('onPulling');
    	if (e.detail.deltaY < 0) return
    }
    const finishedText = computed(() => {
        return  data.list.length>0?'没有更多了':'';
    })
    const onSearch = () => {
        initEmpty()
    }
    const searchQuery = reactive({
        multiFiled:'',
        pageNo:data.pageNo,
        pageSize:data.pageSize,
    })
    
    const getDataPage = (pageNo) =>{
    	if (data.finished || data.loading)return;
    	searchQuery.pageNo = pageNo || data.pageNo;
    	data.loading = true;
        querySalesLeadPage(searchQuery).then((res) => {
    		if(refresherTriggered.value !== false) refresherTriggered.value = false
            data.list = [...data.list, ...res.data.list]
            data.totalRow = res.data.totalRow
            data.pageNo = data.pageNo + 1 ;
            data.loading = false;
            data.finished = res.data.lastPage;
        }).catch((err) => {
            data.finished = true;
        });
    }
    const status_list = ref([]);
	const init = async() =>{
		status_list.value = await dictStore.getDict('sales_lead_status');
		getDataPage()
	}
    const initEmpty =() =>{
        data.initEmpty()
    	getDataPage(1)
    }
    
    onMounted(() => {
    	init()
    })
    
    onReachBottom(() => {
    	console.log('onReachBottom')
    	if(!data.finished) getDataPage();
    })
    onShow(() => {
    	uni.$once("refresh", (data) => {
    		initEmpty()
    	})
    })
    const handChange =(status,info)=> {
        changeStatus({id:info.id,status:status}).then((res) => {
            info.status = status
        }).catch((err) => {
        });
    }
</script>
<style lang="scss" scoped>
    @import '@/styles/list.scss';
</style>