<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        状态
                    </div>
                    <div v-if="info.basic.is_converted && info.basic.is_converted ==1" class="head_status">
						<u-image class="van-icon__image" width="40"  mode="widthFix" src="/static/icon/converted.png" />
                    </div>
                </div>
                <progress-status  dataKey="dval" dataLabel="dname"  :readonly="info.basic.is_converted && info.basic.is_converted ==1" :status_list="status_list" :active="info.basic.status" @update:active="handChange($event,info.basic)"></progress-status>
            </div>
        </div>
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        {{ info.basic.name }}
                    </div>
                </div>
                 <u-divider></u-divider>
                <div class="card_main">
					<div class="item">
					    <div class="left">区域</div><div class="rigth">{{info.basic.areaName}}</div>
					</div>
					<div class="item">
					    <div class="left">地区</div><div class="rigth">{{info.basic.district}}</div>
					</div>
                    <div class="item">
                        <div class="left">公司名称</div><div class="rigth">{{info.basic.company}}</div>
                    </div>
                    <div class="item">
                        <div class="left">公司电话</div><div class="rigth">{{info.basic.company_phone}}</div>
                    </div>
                    <div class="item">
                        <div class="left">手机/电话</div><div class="rigth"><a :href="`tel:${ info.basic.mobile_phone }`">{{info.basic.mobile_phone}}</a></div>
                    </div>
                    <div class="item">
                        <div class="left">分级</div><div class="rigth">{{info.basic.rating}}</div>
                    </div>
                    <div class="item">
                        <div class="left">邮箱</div><div class="rigth">{{info.basic.email}}</div>
                    </div>
                    <div class="item">
                        <div class="left">微信</div><div class="rigth">{{info.basic.wechat}}</div>
                    </div>
                    <!-- <div class="item">
                        <div class="left">产品</div><div class="rigth">{{info.basic.product}}</div>
                    </div> -->
                    <div class="item">
                        <div class="left">产品</div>
                        <div class="rigth">
                            <div class="tag_list">
                                <div v-for="(item,index) in info.basic.productList" :key="index"  class="tag">
                                    <div class="tag_icon"><u-icon color="#fff" name="checkbox-mark" /></div> {{item}}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="left">线索来源</div><div class="rigth">{{info.basic.source}}</div>
                    </div>
                    <div class="item">
                        <div class="left">省份</div><div class="rigth">{{info.basic.province}}</div>
                    </div>
                    <div class="item">
                        <div class="left">城市</div><div class="rigth">{{info.basic.city}}</div>
                    </div>
                    <div class="item">
                        <div class="left">具体地址</div><div class="rigth">{{info.basic.address}}</div>
                    </div>
                </div>

                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.basic.created_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.basic.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.basic.update_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.basic.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>
        <related-list :data="info.relatedList" :id="routerQuery.id" :baseInfo="info.basic" title="市场活动相关"></related-list>
		
		<u-modal v-model:show="convertDialog" confirmColor="#849EB2" @confirm="router.push({name: 'company'})"   @close="convertDialog = false" @cancel="convertDialog = false" title="恭喜您！" show-cancel-button>
            <div class="convertSucc">
                <div class="img"><img height="100"  :src="`${config.static}/succ.gif`" /></div>
                <div class="msg">销售线索已经转换成<text>客户</text>和<text>机会</text></div>
            </div>
        </u-modal>

        <footer-btn v-if="hasPermission('Lead','U') && (!info.basic.is_converted || info.basic.is_converted !=1) " :cancelBtnShow="false" 
			@onconfirm="router.push({name: 'leadEdit',params: {id:routerQuery.id}})"  confirm_btn_text="编辑"></footer-btn>
        <floating-window>
            <div v-if="!info.basic.is_converted || info.basic.is_converted !=1" class="item" @click="onchange">
                <span class="gmRead">线索<br>转换</span>
            </div>
        </floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import ProgressStatus from '@/components/ProgressStatus.vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import RelatedList from '@/components/RelatedList.vue';
import {querySalesLeadDetails,changeStatus,convertSalesLead}  from  '@/api/lead'
import {useDictStore} from '@/store/modules/dictStore'
    const router = useRouter();
	const util = inject("util");
    const dictStore = useDictStore()
    const routerQuery =  defineProps({
    	isComponent: {
    		type: Boolean,
    		default: false,
    	},
    	id:{
    		type: String,
    		default: null,
    	}
    })
    const info = ref({
        basic:{},
        relatedList:[],
		isEdit:0,
    });
    const getInfo = () =>{
        querySalesLeadDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const status_list = ref([]);
    const init = async() =>{
        status_list.value = await dictStore.getDict('sales_lead_status');
        getInfo()
    }
    const handChange =(status,info)=> {
        changeStatus({id:info.id,status:status}).then((res) => {
            info.status = status
        }).catch((err) => {
        });
    }
    const convertDialog = ref(false);
    const onchange =()=>{
		uni.showModal({
			title: '提示',
			content: '是否转换该线索？',
			success: function (res) {
				if (res.confirm) {
					convertSalesLead(routerQuery.id).then((res) => {
					    convertDialog.value = true;
					}).catch((err) => {
					});
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
    }
	onShow(() => {
		uni.$once("refresh", (data) => {
			init()
		})
	})
    onMounted(() => {
        init()
    })
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
    .succ_tit{
        font-size: 18px;
        font-weight: bold;
    }
    .convertSucc{
        text-align: center;
        padding-bottom: 30px;
        color: #555;
        text{
            font-weight: bold;
            margin: 0 2px;
        }
    }
</style>