<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">销售线索</div>
                </div>
				<u--form labelPosition="top" labelWidth="auto" ref="form1" >
					<u-form-item label="地区" prop="info.district">
						<zxz-uni-data-select clearable placeholder="请选择"  v-model="info.district" dataText="district" @change="areaChange" dataValue="district"  :localdata="userInfo.staffAreaList"></zxz-uni-data-select>
					</u-form-item>
					
					<u-form-item label="线索名称" prop="name" >
						<u--input  v-model="info.name" class="int"   placeholder="请输入线索名称"  />
					</u-form-item>
                <!-- 2023-04-11 去掉 -->
                <!-- <van-field
                    label="市场活动" required  readonly  label-align="top" right-icon="arrow" class="int" >
                    <template #input>
                        <selector
                            title="选择市场活动(多选)"
                            title_last_txt="条市场活动"
                            v-model="info.marketingArr"
                            :fieldArr="[{label:'市场活动',field:'name'}]"
                            list_api_url="/api/marketing/queryListByStaff"
                            @change="selectorChange($event,'666')"
                        >
                        </selector>
                    </template>
                </van-field> -->
					<u-form-item label="公司" prop="company" >
						<u--input  v-model="info.company"  class="int"   placeholder="请输入公司名称"  />
					</u-form-item>
					<u-form-item label="公司电话" prop="company_phone" >
						<u--input  v-model="info.company_phone"  class="int"   placeholder="请输入公司电话"  />
					</u-form-item>
					<u-form-item label="电话" prop="mobile_phone" >
						<u--input  v-model="info.mobile_phone"  class="int"   placeholder="请输入电话"  />
					</u-form-item>

					<u-form-item label="分级" prop="rating">
						<zxz-uni-data-select  placeholder="请选择分级"  v-model="info.rating" dataText="dval" dataValue="dname" :localdata="dict['sales_lead_grading']"></zxz-uni-data-select>
					</u-form-item>
                <!-- <van-field  label="来源" required  label-align="top"  class="int">
                    <template #input>
                        <el-select v-model="info.source" class="m-2"   placeholder="请选择来源">
                            <el-option v-for="item in dict['sales_lead_source']" :key="item.dval"  :label="item.dname" :value="item.dval" />
                        </el-select>
                    </template>
                </van-field> -->
					<u-form-item label="邮箱" prop="email" >
						<u--input  v-model="info.email"  class="int"   placeholder="请输入邮箱"  />
					</u-form-item>
					<u-form-item label="微信" prop="wechat" >
						<u--input  v-model="info.wechat"  class="int"   placeholder="请输入微信"  />
					</u-form-item>
					
                <!-- <van-field label="状态" required  label-align="top"  class="int">
                    <template #input>
                        <el-select v-model="info.status" class="m-2"   placeholder="请选择线索状态">
                            <el-option v-for="item in dict['sales_lead_status']" :key="item.dval"  :label="item.dname" :value="item.dval" />
                        </el-select>
                    </template>
                </van-field> -->
				
					<u-form-item label="产品" prop="productList" >
						<zxz-uni-data-select  placeholder="请选择产品" multiple v-model="info.productList" dataText="dval" dataValue="dname" :localdata="dict['sales_lead_product']"></zxz-uni-data-select>
					</u-form-item>
					
					<u-form-item label="请选择省份/城市" prop="info.type" ref="item1">
						<uni-data-picker placeholder="请选择地址" popup-title="请选择城市"
							v-slot:default="{data, error, options}"
							:map="{text:'rname',value:'code'}"
							:localdata="options"
							@change="areaConfirm"
						 >
							<!-- <view v-if="error" class="error showarea">
							  <text>{{error}}</text>
							</view> -->
							<view v-if="info.province" class="selected showarea">
								 <text>{{ info.province  }}{{ info.city }}</text>
							</view>
							<view v-else class="showarea">
								<text class="readonly">请选择省份/城市</text>
							</view>
						</uni-data-picker>
					</u-form-item>
					
					<u-form-item label="具体地址" prop="address" >
						<u--textarea :count="!!info.address" maxlength="150"  v-model="info.address"  class="int"   placeholder="具体地址"  />
					</u-form-item>
				</u--form>
            </div>
        </div>

        <footer-btn :cancelBtnShow="false"  confirm_btn_text="保存" @onconfirm="save()"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import Selector from '@/components/Selector.vue';
import dayjs from "dayjs";
import {saveSalesLead,querySalesLeadDetails}  from  '@/api/lead'
	const prop =  defineProps({
		id: String,
		isComponent: {
			type: Boolean,
			default: false,
		},
	})
	const router = useRouter();
	const routerQuery =  prop;
	const emit = defineEmits(['confirm'])
	
	// 相关字典
	const dictStore = useDictStore()
	const util = inject("util");
    const deleteFild = ['updateStaffName','createdStaffName','productList','marketingArr','areaName'];
    const save = () => {
        let postData = {}
        if(info.value.productList)info.value.product = info.value.productList.join(';')
        // info.value.marketing_id_list = info.value.marketingArr.join(',')
        for(var key in info.value){
            if(!deleteFild.includes(key))
                postData["salesLead." + key] = info.value[key];
        }
        saveSalesLead(postData).then((res)=>{
			uni.showModal({
				title: '提示',
				content: info.value.id ?'修改成功':'保存成功',
				showCancel:false,
				success: function (confirmres) {
					if (confirmres.confirm) {
						if(prop.isComponent)emit('confirm', res)
						else{
							uni.$emit("refresh", {refresh: true}); 
							router.back();
						}
					} else if (confirmres.cancel) {
						uni.$emit("refresh", {refresh: true}); 
						console.log('用户点击取消');
					}
				}
			});
        })
    }
    const getInfo = () =>{
        querySalesLeadDetails(routerQuery.id).then((res) => {
            info.value = res.data.basic
            info.value.marketingArr = info.value.marketing_id_list ? info.value.marketing_id_list.split(','):[];
        }).catch((err) => {
        });
    }
	const { userInfo } = useUserStore();
	const info = ref({
		area_code:userInfo.staffAreaList.length==1 ? userInfo.staffAreaList[0].areaCode : null,
		district:userInfo.staffAreaList.length==1 ? userInfo.staffAreaList[0].district : null ,
	});
	
    const init = async() =>{
        await dictStore.getDictBatch(['sales_lead_product','sales_lead_source','sales_lead_grading','sales_lead_status']);
        options.value = await dictStore.getArea()
        if(routerQuery.id){
            getInfo()
        }
    }
    const options = ref([]);
    const areaConfirm = (selectedValues) => {
        // cascaderShow.value = false;
    	let selectedOptions = selectedValues.detail.value;
        info.value.province =selectedOptions[0].text ;//selectedOptions.map((option) => option.text).join('/');
        info.value.city = selectedOptions[1].text
    };
	
	const areaChange =(value)=>{
		info.value.area_code = userInfo.staffAreaList.find(i=>i.district===value)?.areaCode;
	}
    onMounted(() => {
        init()
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
</style>