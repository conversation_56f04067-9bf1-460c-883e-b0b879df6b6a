<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        {{ info.basic.name }}
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <!-- <div class="item">
                        <div class="left">地址明细</div>
                        <div class="rigth">
                            <span @click="router.push({name: 'marketingInfo',params: {id:info.basic.marketing_id}})" class="link">
                                {{info.basic.address}}
                            </span>
                        </div>
                    </div> -->
                    <div class="item">
                        <div class="left">地址明细</div>
                        <div class="rigth">
                            <span v-if="info.basic.latitude!='' && info.basic.longitude!=''" @click="openLocation(info.basic.latitude,info.basic.longitude,info.basic.name,info.basic.address)" class="link">
                                {{info.basic.address}}
                            </span>
                            <span v-else>{{info.basic.address}}</span>
                        </div>
                    </div>
                    <div class="item">
                        <div class="left">类型</div>
                        <div class="rigth">
                            {{info.basic.type}}
                        </div>
                    </div>
                    <!-- <div class="item">
                        <div class="left">联系人</div><div class="rigth"><span @click="router.push({name: 'contactInfo',params: {id:info.basic.contact_id}})" class="link">{{info.basic.contactName}}</span></div>
                    </div> -->
                    <!-- <div class="item">
                        <div class="left">状态</div><div class="rigth">{{info.basic.status}}</div>
                    </div> -->
                    <div class="item">
                        <div class="left">备注</div><div class="rigth">{{info.basic.remark}}</div>
                    </div>
                </div>

                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.basic.created_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.basic.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.basic.update_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.basic.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>
        <footer-btn v-if="hasPermission('Address','C')" :cancelBtnShow="false" @onconfirm="router.push({name: 'addressEdit',params: {id:routerQuery.id}})" confirm_btn_text="编辑"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {queryAddressDetails}  from  '@/api/company'
    const prop =  defineProps(['id'])
    const router = useRouter();
    const util = inject("util");
    const routerQuery =  prop;
    const info = ref({
        basic:{},
        relatedList:[],
    });
    const getInfo = () =>{
        queryAddressDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const openLocation = async(latitude,longitude,name,address) =>{
        uni.openLocation({
        	latitude: parseFloat(latitude),
        	longitude: parseFloat(longitude),
        	name: name,
        	address: address,
        	scale: 15,
        	success: function () {
        		console.log('success');
        	}
        });
    }
    const init = () =>{
        getInfo()
    }
    onMounted(() => {
        init()
    })
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
</style>