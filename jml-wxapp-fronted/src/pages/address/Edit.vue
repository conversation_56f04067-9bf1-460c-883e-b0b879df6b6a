<template>
    <div class="page pd100">
		<u--form labelPosition="top" labelWidth="auto">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">公司：{{routerQuery.name}}</div>
                </div>
				<u-form-item label="公司地址信息" prop="info.address" required >
					<u--input  v-model="info.address"   class="int"   placeholder="请输入公司名称"  />
				</u-form-item>
				<u-form-item label="类型" prop="info.type" required>
					<zxz-uni-data-select clearable filterable placeholder="请选择"  v-model="info.type" dataText="dval" dataValue="dname"  :localdata="dict['account_address_type']"></zxz-uni-data-select>
				</u-form-item>
                <u-form-item label="备注" prop="info.remark" >
                	<u--textarea :count="!!info.remark" maxlength="800"  v-model="info.remark"  class="int"   placeholder="备注"  />
                </u-form-item>
            </div>
        </div>
		</u--form>
        <footer-btn :cancelBtnShow="false"  confirm_btn_text="保存" @onconfirm="save()"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import dayjs from "dayjs";
import {saveAddress,queryAddressDetails}  from  '@/api/company'
	const prop =  defineProps({
		id: String,
		isComponent: {
			type: Boolean,
			default: false,
		},
	})
    const router = useRouter();
    const routerQuery =  prop;
    const emit = defineEmits(['confirm'])

    // 相关字典
    const dictStore = useDictStore()

	const util = inject("util");
    const deleteFild = ['updateStaffName','createdStaffName'];
    const save = () => {
        let postData = {}
        for(var key in info.value){
            if(!deleteFild.includes(key))
            postData["address." + key] = info.value[key];
        }
        saveAddress(postData).then((res)=>{
			uni.showModal({
				title: '提示',
				content: info.value.id ?'修改成功':'保存成功',
				showCancel:false,
				success: function (confirmres) {
					if (confirmres.confirm) {
						if(prop.isComponent)emit('confirm', res)
						else{
							uni.$emit("refresh", {refresh: true}); 
							router.back();
						}
					} else if (confirmres.cancel) {
						uni.$emit("refresh", {refresh: true}); 
						console.log('用户点击取消');
					}
				}
			});
        })
    }
    const getInfo = () =>{
        queryAddressDetails(routerQuery.id).then((res) => {
            info.value = res.data.basic
        }).catch((err) => {
        });
    }
    const info = ref({
        account_id:routerQuery.account_id,
        name:routerQuery.name,
    });
    const init = async() =>{
        await dictStore.getDictBatch(['account_address_type']);
        if(routerQuery.id){
            getInfo()
        }
    }
    onLoad((option) => {
    	if(!prop.isComponent){
    		const title = (routerQuery.id?'编辑公司地址信息':'新建公司地址信息');
    		uni.setNavigationBarTitle({
    		　　title
    		})
    	}
    	init()
    })
    onMounted(() => {
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
</style>