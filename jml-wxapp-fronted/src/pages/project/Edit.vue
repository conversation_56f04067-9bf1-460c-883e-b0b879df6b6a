<template>
    <div class="page pd100">
		
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">项目信息</div>
                </div>
				<u--form labelPosition="top" labelWidth="auto" ref="form1" >
				<u-form-item label="地区" prop="info.district" required>
					<zxz-uni-data-select clearable placeholder="请选择"  v-model="info.district" dataText="district" @change="areaChange" dataValue="district"  :localdata="userInfo.staffAreaList"></zxz-uni-data-select>
				</u-form-item>
				
				<u-form-item label="项目名称" prop="info.name">
					<u--input  v-model="info.name"  class="int"   placeholder="项目名称"  />
				</u-form-item>
				
				<u-form-item label="项目时间" prop="checkin_time"  @click="calendarShow= true">
					<text v-if="info.start_date">{{ info.start_date  }} - {{ info.end_date }}</text>
					<u--input disabled v-else class="van-field__control" readonly placeholder="请选择时间" />
					
					<template #right><u-icon name="calendar" size='22' @click="calendarShow= true"></u-icon></template>
				</u-form-item>
				
                <u-form-item label="阶段" prop="info.stage">
                	<zxz-uni-data-select  placeholder="请选择阶段"  v-model="info.stage" dataText="dval" dataValue="dname" :localdata="dict['project_stage']"></zxz-uni-data-select>
                </u-form-item>
				
				<u-form-item label="类型" prop="info.type">
					<zxz-uni-data-select  placeholder="请选择类型"  v-model="info.type" dataText="dval" dataValue="dname" :localdata="dict['project_type']"></zxz-uni-data-select>
				</u-form-item>
				<u-form-item label="投入" prop="info.investment">
					<u--input  v-model="info.investment"  class="int"   placeholder="请输入投入"  />
					<!-- <input v-model="info.investment" class="int" focus placeholder="请输入投入" /> -->
				</u-form-item>
				<u-form-item label="产出" prop="info.output">
					<u--input  v-model="info.output"  class="int"   placeholder="请输入产出"  />
				</u-form-item>
				<u-form-item label="产品编号" prop="info.product_code">
					<u--input  v-model="info.product_code"  class="int"   placeholder="请输入产品编号"  />
				</u-form-item>
				<u-form-item label="产品分类" prop="info.stage">
					<zxz-uni-data-select  placeholder="产品分类"  v-model="info.category" dataText="dval" dataValue="dname" :localdata="dict['project_category']"></zxz-uni-data-select>
				</u-form-item>
				
				<u-form-item label="规格描述图片" prop="info.spec_desc_img" >
					<u-upload
					    :max-count="2"
					    multiple
					    @after-read="afterRead($event,'spec_desc_img')"
					    :deletable="true"
						:sizeType="['compressed']"
					    :before-delete="(file,detail)=>beforeDelete(file,detail)"
						@delete="beforeDelete($event,'spec_desc_img')"
						:fileList="info.spec_desc_img"
					>
					</u-upload>
				</u-form-item>

				<u-form-item label="规格描述" prop="info.spec_desc" >
					<u--textarea :count="!!info.spec_desc" maxlength="150"  v-model="info.spec_desc"  class="int"   placeholder="规格描述"  />
				</u-form-item>
				</u--form>
            </div>
        </div>
		
        <footer-btn :cancelBtnShow="false"  confirm_btn_text="保存" @onconfirm="save()"></footer-btn>
        <floating-window></floating-window>
        <u-calendar v-if="calendarShow" v-model:show="calendarShow" confirmColor="#849EB2"
        	monthNum = "12"
			:minDate="minDate"
        	:maxDate="maxDate"
        	confirm-disabled-text="请选择结束时间" mode="range"  round="10" 
        	@confirm="onCalendarConfirm"
        	closeOnClickOverlay
        	@close="calendarShow=false" 
        />
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import dayjs from "dayjs";
import {queryDetails,saveEdit}  from  '@/api/project'
import {asyncUpload}  from  '@/api/common'
    const prop =  defineProps({
    	id: String,
		// account_id: String,
    	isComponent: {
    		type: Boolean,
    		default: false,
    	},
    })
    const emit = defineEmits(['confirm'])
    const router = useRouter();
	const { userInfo } = useUserStore();
    const routerQuery = prop;// prop.isComponent ? {} : router.currentRoute.value.query;
    // 相关字典
    const dictStore = useDictStore()
    const deleteFild = ['createdStaffName','updateStaffName','spec_desc_img','areaName'];
    const save = () => {
        let postData = {}
		if(Array.isArray(info.value.spec_desc_img)) {
		    info.value.spec_desc_img.forEach((item,index) => {
		        info.value[`spec_desc_img${index+1}`] = item.url
		    });
		}
        for(var key in info.value){
            if(!deleteFild.includes(key))
                postData["project." + key] = info.value[key];
        }
        saveEdit(postData).then((res)=>{
            uni.showModal({
            	title: '提示',
            	content: info.value.id ?'修改成功':'保存成功',
            	showCancel:false,
            	success: function (confirmres) {
            		if (confirmres.confirm) {
            			if(prop.isComponent)emit('confirm', res)
            			else{
            				uni.$emit("refresh", {refresh: true}); 
            				router.back();
            			}
            		} else if (confirmres.cancel) {
            			uni.$emit("refresh", {refresh: true}); 
            			console.log('用户点击取消');
            		}
            	}
            });
        })
    }
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data.basic
            if(res.data.basic.spec_desc_img && res.data.basic.spec_desc_img!=''){
                info.value.spec_desc_img = res.data.basic.spec_desc_img.split(';').map(i=>{
            		return{url:import.meta.env.VUE_APP_BASE_IMG + i}
            	})
            }else{
                info.value.spec_desc_img = []
            }
        }).catch((err) => {
        });
    }

	
	const info = ref({
		// account_id:routerQuery.account_id,
		spec_desc_img:[],
		area_code:userInfo.staffAreaList.length==1 ? userInfo.staffAreaList[0].areaCode : null,
		district:userInfo.staffAreaList.length==1 ? userInfo.staffAreaList[0].district : null ,
	});
	

    const init = async() =>{
        await dictStore.getDictBatch(['project_stage','project_type','project_category']);
        if(routerQuery.id){
            getInfo()
        }
    }
	
    const calendarShow = ref(false)
	const minDate = dayjs().add(-6, 'month').format('YYYY-MM-DD')
	const maxDate = dayjs().add(6, 'month').format('YYYY-MM-DD')
    const onCalendarConfirm = (values) => {
        const start= values[0],end = values[values.length - 1];
        calendarShow.value = false;
        info.value.start_date = `${dayjs(start).format('YYYY-MM-DD')}`;
        info.value.end_date = `${dayjs(end).format('YYYY-MM-DD')}`;
    };

    const afterRead = async(event,type) =>{
    	let files = [].concat(event.file)
    	if(!info.value[type]) info.value[type] = [];
    	let fileListLen =  info.value[type].length
    	files.map((item) => {
    		info.value[type].push({
    			...item,
    			status: 'uploading',
    			message: '上传中'
    		})
    	})
    	for (let i = 0; i < files.length; i++) {
    		try {
    			const {data} = await asyncUpload(files[i].url)
    			let item = info.value[type][fileListLen];
    			info.value[type].splice(fileListLen, 1, {
    			  ...item,
    			  status: 'success',
    			  message: '',
    			  url: data.url,
    			});
    			fileListLen++;
    		} catch (e) {
    			console.log(e)
    			uni.showToast({
    				title: "图片上传失败",
    				icon: "none"
    			})
    		}
    	}
    }
    
    const beforeDelete = ({file,index},type) =>{
    	uni.showModal({
    		title: '提示',
    		content: '是否删除该附件？',
    		success: function (res) {
    			if (res.confirm) {
    				info.value[type].splice(index, 1)
    			} else if (res.cancel) {
    				console.log('用户点击取消');
    			}
    		}
    	});
    }
	const areaChange =(value)=>{
		info.value.area_code = userInfo.staffAreaList.find(i=>i.district===value)?.areaCode;
	}
	
    onMounted(() => {
        init()
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
</style>