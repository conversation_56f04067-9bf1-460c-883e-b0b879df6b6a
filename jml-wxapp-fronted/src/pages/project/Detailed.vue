<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        {{ info.basic.name }}
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
					<div class="item">
					    <div class="left">区域</div><div class="rigth">{{info.basic.areaName}}</div>
					</div>
					<div class="item">
					    <div class="left">地区</div><div class="rigth">{{info.basic.district}}</div>
					</div>
                    <!-- <div class="item">
                        <div class="left">公司名称 </div>
                        <div class="rigth">
                            <span @click="router.push({name: 'companyInfo',params: {id:info.basic.account_id}})" class="link">
                                {{info.basic.accName}}
                            </span>
                        </div>
                    </div> -->
                    <div class="item">
                        <div class="left">开始时间</div><div class="rigth">{{info.basic.start_date}}</div>
                    </div>
                    <div class="item">
                        <div class="left">结束时间</div><div class="rigth">{{info.basic.end_date}}</div>
                    </div>
                    <div class="item">
                        <div class="left">阶段</div><div class="rigth">{{info.basic.stage}}</div>
                    </div>
                    <div class="item">
                        <div class="left">类型</div><div class="rigth">{{info.basic.type}}</div>
                    </div>
                    <div class="item">
                        <div class="left">投入</div><div class="rigth">{{info.basic.investment}}</div>
                    </div>
                    <div class="item">
                        <div class="left">产出</div><div class="rigth">{{info.basic.output}}</div>
                    </div>
                    <div class="item">
                        <div class="left">产品编号</div><div class="rigth">{{info.basic.product_code}}</div>
                    </div>
                    <!-- <div class="item">
                        <div class="left">产品数量</div><div class="rigth">{{info.basic.cntProduct}}</div>
                    </div> -->
                    <div class="item">
                        <div class="left">产品分类</div><div class="rigth">{{info.basic.category}}</div>
                    </div>
                    <div class="item">
                        <div class="left">规格描述</div><div class="rigth">{{info.basic.spec_desc}}</div>
                    </div>
                    <div class="item">
                        <div class="left">规格描述图片</div>
                        <div class="rigth">
                            <image v-for="(item,index) in info.spec_desc_img" :key="index"   @click="showImagePreview(info.spec_desc_img,index)"  mode="widthFix"  :src="imgBase+item" />
						</div>
                    </div>
                </div>

                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.basic.created_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.basic.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.basic.update_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.basic.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>
        <related-list :data="info.relatedList" :id="routerQuery.id" :baseInfo="info.basic" title="市场活动相关"></related-list>
        <footer-btn v-if="hasPermission('Project','U')"   :cancelBtnShow="false" @onconfirm="router.push({name: 'projectEdit',params: {id:routerQuery.id}})" confirm_btn_text="编辑"></footer-btn>
		<floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import RelatedList from '@/components/RelatedList.vue';
import {queryDetails}  from  '@/api/project'
    const routerQuery =  defineProps({
    	isComponent: {
    		type: Boolean,
    		default: false,
    	},
    	id:{
    		type: String,
    		default: null,
    	}
    })
    const router = useRouter();
    const route = useRoute();
    const util = inject("util");
    const info = ref({
        basic:{},
        relatedList:[],
    	// isEdit:0,
    });
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
			if(res.data.basic.spec_desc_img && res.data.basic.spec_desc_img!=''){
			    info.value.spec_desc_img = res.data.basic.spec_desc_img.split(';')
			}else{
			    info.value.spec_desc_img = []
			}
        }).catch((err) => {
        });
    }
    const init = () =>{
        getInfo()
    }
	onShow(() => {
		uni.$once("refresh", (data) => {
			init()
		})
	})
    onMounted(() => {
        init()
    })
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
</style>