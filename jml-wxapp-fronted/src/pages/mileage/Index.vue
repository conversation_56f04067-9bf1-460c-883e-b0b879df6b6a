<template>
    <div class="page">
		<u-sticky bgColor="#fff">
		    
			<div class="tool">
			    <!-- <u-tabs
			        v-model:active="active_tabs" title-inactive-color="#999" color="#849EB2" title-active-color="#849EB2" background="none" >
			        <u-tab title="当前认证"></u-tab>
			        <u-tab title="认证历史"></u-tab>
			    </u-tabs> -->
				<u-tabs :current="active_tabs" @change="tabChange" lineWidth="50" inactiveStyle="#999" lineColor="#849EB2" activeStyle="#849EB2" background="none" :list="list1"></u-tabs>
			</div>
		</u-sticky>
        
       <div class="card_list">
       		<scroll-view style="height:100%" scroll-y enable-flex  @scrolltolower="getDataPage()">
       			<uni-swipe-action>
       					<div v-for="(item,index) in data.list" :key="index" class="van-swipe-cell">
							
                        <uni-swipe-action-item v-if="item.status=='0'">
                            <div class="card">
                                <div  class="card_head">
                                    <div class="card_head_tit" v-if="item.name">
                                        <div class="txt1">{{ item.name }}</div>
                                    </div>
                                    <div  class="head_status">
										<image  class="van-icon__image" width="40"  mode="widthFix" src="/static/icon/mileage_incomplete.png" />
                                    </div>
                                </div>
                                <div class="card_main"  @click="router.push({name: 'mileageInfo',params: {id: item.id}})">
                                    <div class="item">
                                        <div class="left">开始地址</div>
                                        <div class="rigth">{{item.start_address}}</div>
                                    </div>
                                    <div class="item">
                                        <div class="left">开始时间</div>
                                        <div class="rigth">{{item.start_time}}</div>
                                    </div>
                                    <div class="item">
                                        <div class="left">开始里程数</div>
                                        <div class="rigth">{{item.start_mileage}}</div>
                                    </div>
                                </div>
                                <u-divider></u-divider>
                                <div class="card_footer">
                                    <u-button plain block hairline size="normal" @click.stop="mileageInfo=item;mileageType='end';mapShow=true" type="primary" color="#849EB2">结束里程</u-button>
                                </div>
                            </div>
                            <template  #right>
								<div @click.stop="deleteRecord(item.id)" class="card_left">
									<div class="card_left_btn">
										<u-icon size="20" color="#fff" name="trash" />
										<div class="txt">删除</div>
									</div>
								</div>
                            </template>
                        </uni-swipe-action-item>
                        <uni-swipe-action-item v-else>
                            <div class="card">
                                <div  class="card_head">
                                    <div class="card_head_tit" v-if="item.name">
                                        <div class="txt1">编号：{{ item.name }}</div>
                                    </div>
                                    <div  class="head_status">
										<image  class="van-icon__image" width="40"  mode="widthFix" src="/static/icon/mileage_completed.png" />
                                    </div>
                                </div>
                                <u-divider></u-divider>
                                <div class="card_main"  @click="router.push({name: 'mileageInfo',params: {id: item.id}})">
                                    <div class="item">
                                        <div class="left">开始地址</div>
                                        <div class="rigth">{{item.start_address}}</div>
                                    </div>
                                    <div class="item">
                                        <div class="left">结束地址</div>
                                        <div class="rigth">{{item.end_address}}</div>
                                    </div>
                                    <div class="item">
                                        <div class="left">里程数</div>
                                        <div class="rigth">{{item.mileage}}</div>
                                    </div>
                                    <div class="item">
                                        <div class="left">开始时间</div>
                                        <div class="rigth">{{item.start_time}}</div>
                                    </div>
                                    <div class="item">
                                        <div class="left">结束时间</div>
                                        <div class="rigth">{{item.end_time}}</div>
                                    </div>
                                    <div class="item">
                                        <div class="left">创建时间</div>
                                        <div class="rigth">{{item.created_time}}</div>
                                    </div>
                                    <div class="item">
                                        <div class="left">创建员工</div>
                                        <div class="rigth">{{item.createdStaffName}}</div>
                                    </div>
                                </div>
                            </div>
                        </uni-swipe-action-item>
                    </div>
				</uni-swipe-action>	
				<div class="empty_box" v-if="!data.loading && (!data.list ||  data.list.length<=0)">
					<u-empty v-if="!data.list || data.list.length==0" text="未找到信息" :icon="`${config.static}images/empty/data.png`" ></u-empty>
					<div v-if="active_tabs==0 && hasPermission('MileageCer','C')" class="btn_add" @click="mileageType='start';mapShow=true"><u-icon size="20" name="/static/icon/btn_add_icon.png" />   <span>开启里程认证</span></div>
				</div>
			</scroll-view>
        </div>
        <mileage-map ref="mileageMapDOM"  withUploader capture="camera" :type="mileageType" :mileageInfo="mileageInfo" v-model:show="mapShow" @cancel="mapShow = false" show-cancel-button @confirm="mapConfirm"></mileage-map>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import FloatingWindow from '@/components/FloatingWindow.vue';
import {queryPageData,deleteMileageCer,saveEdit }  from  '@/api/mileage'
import MileageMap from '@/components/MileageMap.vue';
	const prop =  defineProps({
		isComponent: {
			type: Boolean,
			default: false,
		},
	})


    const router = useRouter();
	const util = inject("util");
    const data = ref({});
    const routerQuery =  prop;
    const searchQuery = reactive({
    })
	const list1 = ref([{
                    name: '当前认证',
                }, {
                    name: '认证历史',
                }])
    watch([()=>prop.date,()=>prop.createdStaffId],([date,createdStaffId]) => {
        if(date && createdStaffId){
            searchQuery.startDate = date
            searchQuery.endDate = date
            searchQuery.createdStaffId = createdStaffId
            if(prop.createdStaffId) selectedUser.value = userList.value.find(i=>i.id==prop.createdStaffId)
            initEmpty()
        }
    });
    // const loading = ref(false);
    const active_tabs = ref(0)
    const pageData = ref([])
    const getDataPage = () =>{
        let info = pageData.value[active_tabs.value]?pageData.value[active_tabs.value]:ref({}).value;
        if(!info.loadData){
            info.loadData = useRelatedList({ url:''})
            info.loadData.query = searchQuery;
        }
        info.loadData.query.status = active_tabs.value
		
		if (info.loadData.finished ||  info.loadData.loading)return;
		info.loadData.loading = true;
		
        queryPageData(info.loadData.payload).then((res) => {
            info.loadData.list = [...info.loadData.list, ...res.data.list]
            info.loadData.totalRow = res.data.totalRow
            info.loadData.pageNo = info.loadData.pageNo + 1 ;
            info.loadData.loading = false;
            info.loadData.finished = res.data.lastPage;
            data.value = info.loadData;
            pageData.value[active_tabs.value]= info;
        }).catch((err) => {
            info.loadData.finished = true;
        });
    }
    const init = () =>{
        getDataPage()
    }
    const initEmpty =() =>{
        data.value = {}
        pageData.value = [];
		init();
    }
	const tabChange = ({index})=>{
		active_tabs.value = index
	}
    watch(() => active_tabs.value,(newValue, oldValue) => {
        if(pageData.value[newValue] && pageData.value[newValue].loadData){
            data.value =  pageData.value[newValue].loadData;
        }else{
            getDataPage()
        }
    },{immediate:true});

    const appStore = useAppStore();
    
    onMounted(() => {
      console.log("挂载");
    })
    const handDetail =(item)=>{
        router.push({name: 'mileageInfo',params: {id: item.id}})
    }
    const finishedText = computed(() => {
        return data && data.list &&  data.list.length>0?'没有更多了':'';
    })


    //里程认证
    const mileageType = ref(null)
    const mapShow = ref(false)
    const mileageInfo = ref(null)
    const mileageMapDOM = ref()
    const mapConfirm = (mapInfo) => {
		let imgArr = mapInfo.files.map((item,index) => {
		    return item.url
		});
        let postData = {}
        if(mileageType.value =='start'){
            postData = {
                'mileageCer.start_address': mapInfo.address || '',
                'mileageCer.start_longitude':mapInfo.location.longitude || '',
                'mileageCer.start_latitude':mapInfo.location.latitude || '',
                'mileageCer.start_mileage': mapInfo.mileage || '',
				'mileageCer.area_code': mapInfo.area_code || '',
				'mileageCer.district': mapInfo.district || '',
                'mileageCer.start_odometer_img': imgArr.join(';'),
                'mileageCer.status': '0',
            }
        }else{
            postData = {
                'mileageCer.id': mileageInfo.value.id,
                'mileageCer.end_address': mapInfo.address || '',
                'mileageCer.end_longitude':mapInfo.location.longitude || '',
                'mileageCer.end_latitude':mapInfo.location.latitude || '',
                'mileageCer.end_mileage': mapInfo.mileage || '',
                'mileageCer.end_odometer_img': imgArr.join(';'),
                'mileageCer.status': '1',
            }
        }

        // if(isArray(mapInfo.files)) {
        //     mapInfo.files.forEach((item,index) => {
        //         postData[`mileageCer.start_odometer_img`] = item
        //     });
        // }
        saveEdit(postData).then((res)=>{
			uni.showModal({
			    title: '提示',
			    content: '提交成功',
				showCancel:false,
				success: function (unires) {
					mileageInfo.value = null
					mileageMapDOM.value?.reset()
					mapShow.value = false
					initEmpty()
				}
			})
        })
    }

    const deleteRecord = (id)=>{
        
		uni.showModal({
		    title: '提示',
		    content: '是否删除该记录？',
			success: function (unires) {
				if (unires.confirm) {
					deleteMileageCer(id).then((res) => {
					    uni.showModal({
					        title:'提示',
					        showCancel:false,
					        content: '操作成功',
					    }).then((aa) => {
					        initEmpty()
					    })
					}).catch((err) => {
					});
				}
			}
		})
    }
    defineExpose({
        getDataPage,initEmpty
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/list.scss';
    .page{
       .tool{
           background: #fff;
			
       }
	   :deep(.tool){
	       .u-tabs__wrapper__nav__item{
	       		flex: 1;
	       }
	   }
    }
    
</style>