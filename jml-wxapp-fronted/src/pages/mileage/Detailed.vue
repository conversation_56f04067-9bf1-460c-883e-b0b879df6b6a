<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                       编号： {{ info.name }}
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
					<div class="item">
					    <div class="left">区域</div><div class="rigth">{{info.areaName}}</div>
					</div>
					<div class="item">
					    <div class="left">地区</div><div class="rigth">{{info.district}}</div>
					</div>
                    <div class="item">
                        <div class="left">里程数</div><div class="rigth">{{info.mileage}}</div>
                    </div>
                    <div class="item">
                        <div class="left">开始里程</div><div class="rigth">{{info.start_mileage}}</div>
                    </div>
                    <div class="item">
                        <div class="left">开始里程图片</div>
                        <div class="rigth">
                            <u-image @click="showImagePreview([imgBase+info.start_odometer_img])" v-if="info.start_odometer_img" fit="fill" width="100" :src="imgBase+info.start_odometer_img" />
                        </div>
                    </div>
                    <div class="item">
                        <div class="left">开始地址</div><div class="rigth">{{info.start_address}}</div>
                    </div>
                    <div class="item">
                        <div class="left">开始时间</div><div class="rigth">{{info.start_time}}</div>
                    </div>
                    <u-divider></u-divider>
                    <div class="item">
                        <div class="left">结束里程</div><div class="rigth">{{info.end_mileage}}</div>
                    </div>
                    <div class="item">
                        <div class="left">结束里程图片</div>
                        <div class="rigth">
                            <u-image @click="showImagePreview([imgBase+info.end_odometer_img])" v-if="info.end_odometer_img" fit="fill" width="100" :src="imgBase+info.end_odometer_img" />
                        </div>
                    </div>
                    <div class="item">
                        <div class="left">结束地址</div><div class="rigth">{{info.end_address}}</div>
                    </div>
                    <div class="item">
                        <div class="left">结束时间</div><div class="rigth">{{info.end_time}}</div>
                    </div>
                </div>

                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.created_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.update_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import RelatedList from '@/components/RelatedList.vue';
import {queryDetails}  from  '@/api/mileage'
	const prop =  defineProps(['id'])
    const router = useRouter();
	const util = inject("util");
    const routerQuery =  prop;
    const info = ref({});
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const init = () =>{
        getInfo()
    }
    onMounted(() => {
        init()
    })
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
</style>