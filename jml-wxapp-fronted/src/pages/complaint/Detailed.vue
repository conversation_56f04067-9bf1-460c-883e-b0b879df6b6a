<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        处理进度
                    </div>
                </div>
                <progress-status :status_list="info.stageList" :active="info.basic.caseStageCode" :data="info.basic" readonly :isClosed="info.basic.isClosed"></progress-status>
            </div>
        </div>
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        {{ info.basic.name  }}
                    </div>
                    <div class="head_status bg">
                        {{ info.basic.serviceType }}
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">客户 </div>
                        <div class="rigth">
                            <span @click="router.push({name: 'companyInfo',params: {id:info.basic.accountId}})" class="link">
                                {{info.basic.accName}}
                            </span>
                        </div>
                    </div>
					
					<div class="item">
					    <div class="left">区域</div><div class="rigth">{{info.basic.areaName}}</div>
					</div>
					<div class="item">
					    <div class="left">地区</div><div class="rigth">{{info.basic.district}}</div>
					</div>
					<div class="item">
					    <div class="left">联络人</div><div class="rigth">{{info.basic.contactName}}</div>
					</div>
					<div class="item">
					    <div class="left">电话</div><div class="rigth">{{info.basic.contactPhone}}</div>
					</div>
					<div class="item">
					    <div class="left">销售人员</div><div class="rigth">{{info.basic.salesStaffName}}</div>
					</div>
					
					<template v-if="info.basic.serviceType=='售后服务'">	
						<div class="item">
							<div class="left">板材状态</div><div class="rigth">{{info.basic.sheetStatus}}</div>
						</div>
						<div class="item">
							<div class="left">反馈人类型</div><div class="rigth">{{info.basic.feedbackPersonType}}</div>
						</div>
						<div class="item">
							<div class="left">反馈方式</div><div class="rigth">{{info.basic.feedbackType}}</div>
						</div>
						<div class="item">
							<div class="left">是否现场查看</div><div class="rigth">{{info.basic.onsiteInspection}}</div>
						</div>
					</template>
					
					<div class="item">
					    <div class="left">新建备注</div><div class="rigth">{{info.basic.createRemark}}</div>
					</div>
					
					<div class="item">
					    <div class="left">附件</div><div class="rigth">
							<u-icon v-if="info.basic.attachment" @click="dom(info.basic.attachment)" size="16" name="https://qa-wxapp-crm.chameleon-artec.com/jml/assets/attachlogo.png" :label="info.basic.attachmentName"></u-icon>
						</div>
					</div>
					
                </div>
				
				<!-- 不同阶段的显示内容 -->
				<template v-if="info.basic.serviceType=='售后服务'">								
					<template v-if="findshow('FKYJ')">
					    <div class="card_head_tit2 mt15">
					        反馈意见
					    </div>
					    <u-divider></u-divider>
						<div class="card_main">
							
							<!-- <div class="item">
								<div class="left">照片</div>
								<div class="rigth">
									<image v-for="(item,index) in info.basic.feedback_img" :key="index"  @click="showImagePreview(info.basic.feedback_img,index)"  mode="widthFix"  :src="imgBase+item" />
								</div>
							</div> -->
							<div class="item">
								<div class="left">反馈类型</div><div class="rigth">{{info.basic.feedbackClass}}</div>
							</div>
							<div class="item">
								<div class="left">工厂</div><div class="rigth">{{info.basic.factory}}</div>
							</div>
							<div class="item">
								<div class="left">反馈意见备注</div><div class="rigth">{{info.basic.feedbackRemark}}</div>
							</div>
						</div>	
					</template>
				</template>
				
				<!-- 反馈意见 -->
				<template v-if="info.basic.serviceType && info.basic.serviceType!='售后服务'">
					<template v-if="findshow('FKYJ')">
						<div class="card_head_tit2 mt15">
							反馈意见
						</div>
						<u-divider></u-divider>
						<div class="card_main">
							<div class="item">
								<div class="left">销售助理评论</div><div class="rigth">{{info.basic.feedbackRemark}}</div>
							</div>
						 </div>
					 </template>
				</template>
				
				<!-- 质量意见 -->
				<template v-if="info.basic && info.basic.qaStatus && info.basic.caseStageCode!='NEW'">
					<div class="card_head_tit2 mt15">
					    <view>质量意见</view>
						<view 
							v-if="routerQuery.type=='pages/complaint/Index' &&  
							(!info.qaOpinion || info.qaOpinion.submitStatus=='0') && 
							(hasPermission('QAFeedBack','U') || (userInfo.is_admin=='1') )" 
							style=" margin-left: auto;">
							<u-icon @click="qualityShow = true" bold name="edit-pen" label="质量反馈" />
						</view>
					</div>
					<u-divider></u-divider>
					<div class="card_main">
						<div class="item">
						    <div class="left">质量部受理状态</div>
							<div class="rigth inlineflex">
								<u-tag :bgColor="info.basic.qaStatus=='已受理'?'#88D39B':'#EB7155'" size="mini" style="display: inline-flex; z-index: 9;" :borderColor="info.basic.qaStatus=='已受理'?'#88D39B':'#EB7155'" :text="info.basic.qaStatus" />
							</div>
						</div>
						<template v-if="info.basic.qaStatus=='已受理'">
							<!-- <div class="item">
							    <div class="left">判定</div><div class="rigth">{{ info.qaOpinion.judge }}</div>
							</div>
							<div class="item">
							    <div class="left">判定描述</div><div class="rigth">{{ info.qaOpinion.judgeDesc }}</div>
							</div> -->
							<div class="item">
							    <div class="left">原因分析</div><div class="rigth">{{ info.qaOpinion.causeAnalysis }}</div>
							</div>
							<div class="item">
							    <div class="left">纠正措施</div><div class="rigth">{{ info.qaOpinion.correctiveAction }}</div>
							</div>
							<div class="item">
							    <div class="left">预防措施</div><div class="rigth">{{ info.qaOpinion.preventiveMeasure }}</div>
							</div>
							<div class="item">
							    <div class="left">预计完成时间</div><div class="rigth">{{ info.qaOpinion.expectedCompletionDate }}</div>
							</div>
							
						</template>
					</div>
				</template>
					
				<!-- 结案信息 -->	
				<template v-if="info.basic.isClosed=='1'">
					<div class="card_head_tit2 mt15">
						结案信息
					</div>
					<u-divider></u-divider>
					<div class="card_main">
						<div class="item">
							<div class="left">结案人</div><div class="rigth">{{ info.basic.closeStaffName }}</div>
						</div>
						<div class="item">
							<div class="left">补货发票号</div><div class="rigth">{{ info.basic.replenishInvoiceNo }}</div>
						</div>
						<div class="item">
							<div class="left">结案原因</div><div class="rigth">{{ info.basic.closeReason }}</div>
						</div>
						<div class="item">
							<div class="left">结案时间</div><div class="rigth">{{ info.basic.closeTime }}</div>
						</div>
					</div>
				</template>
				
				
				
                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.basic.createdTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.basic.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.basic.updateTime }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.basic.updateStaffName }}</div>
                    </div>
                </div>
				
				<template v-if="info.basic.serviceType=='售后服务'">
					<ProductList :dataList="info.orderDetailList" :caseStageCode="info.basic.caseStageCode" readOnly></ProductList>
					
					<SolutionList v-if="routerQuery.type && (findshow('FKYJ') || info.solutionList.length!=0)" :caseStageCode="info.basic.caseStageCode" :caseId="info.basic.id" :showBtn="info.showBtn"  :orderDetailList="info.orderDetailList" :solutionList="info.solutionList" :type="routerQuery.type"  @change="solutionChange"></SolutionList>
				</template>	
				
				
				<!-- 不同状态要填写的 -->
				<u--form labelPosition="top" labelWidth="auto" >
					<!-- {{info.btnCode}} -->
                <template v-if="info.basic.serviceType=='售后服务'">
                    
					<!-- 反馈意见 -->
					<template v-if="info.basic.caseStageCode=='FKYJ'">
						<div class="card_head_tit2 mt15">
						    反馈意见
						</div>
						
						<!-- <u-form-item label="板材状态 " prop="sheetStatus"  >
							<zxz-uni-data-select
								clearable  placeholder="请选择"  
								v-model="model.sheetStatus" 
								:localdata="dict['sheet_status']">
							</zxz-uni-data-select>
						</u-form-item>
						<u-form-item label="反馈人类型 " prop="feedbackPersonType"  >
							<zxz-uni-data-select
								clearable  placeholder="请选择"  
								v-model="model.feedbackPersonType" 
								:localdata="dict['feedback_person_type']">
							</zxz-uni-data-select>
						</u-form-item>
						<u-form-item label="反馈方式 " prop="feedbackType"  >
							<zxz-uni-data-select
								clearable  placeholder="请选择"  
								v-model="model.feedbackType" 
								:localdata="dict['feedback_type']">
							</zxz-uni-data-select>
						</u-form-item> -->
						
						<u-form-item label="反馈类型 " prop="feedbackClass"  >
							<zxz-uni-data-select
								clearable  placeholder="请选择"  
								v-model="model.feedbackClass" 
								:localdata="dict['feedback_class']">
							</zxz-uni-data-select>
						</u-form-item>
						
						<u-form-item label="工厂 " prop="factoryCode" required >
							<zxz-uni-data-select
								clearable  placeholder="请选择"  
								v-model="model.factoryCode" 
								:localdata="dict['factory_code']">
							</zxz-uni-data-select>
						</u-form-item>
						<u-form-item label="销售助理评论" prop="feedbackRemark" >
							<u--textarea :count="!!model.feedbackRemark"   v-model="model.feedbackRemark"  class="int" maxlength="800"  placeholder="销售助理评论"  />
						</u-form-item>
					</template>
					
					<!-- 服务单处理方案 -->
					<template  v-if="info.basic.caseStageCode=='CLFA'">
						
					</template>
					
					<template v-if="info.basic.caseStageCode=='GCYJ'">
						<u-form-item label="工厂意见" prop="factory_option" required>
							<u--textarea :count="!!model.factory_option"   v-model="model.factory_option"  class="int" maxlength="800"  placeholder="工厂意见"  />
						</u-form-item>
						<u-form-item label="工厂照片" prop="model.actory_option_img" >
							<u-upload
								:max-count="3"
								multiple
								@after-read="afterRead($event,'feedback_img')"
								:deletable="true"
								:sizeType="['compressed']"
								:before-delete="(file,detail)=>beforeDelete(file,detail)"
								@delete="beforeDelete($event,'feedback_img')"
								:fileList="model.feedback_img"
							>
							</u-upload>
						</u-form-item>
					</template>
					
						
				</template>
				
                <template v-else>
					<!-- <u-divider></u-divider>
                    <div class="card_head_tit2 mt20">
                        {{info.btnName}}
                    </div> -->
                    <template v-if="info.basic.caseStageCode=='FKYJ'">
						<div class="card_head_tit2 mt15">
						    反馈意见
						</div>
						
						<u-divider></u-divider>
						<u-form-item class="mt15" label="销售助理评论" prop="feedbackRemark">
							<u--textarea :count="!!model.feedbackRemark"   v-model="model.feedbackRemark"  class="int" maxlength="800"  placeholder="销售助理评论"  />
						</u-form-item>
					</template>
                </template>
				
				<template v-if="info.basic.isClosed=='0' &&  info.basic.caseStageCode=='CLOSE' && routerQuery.type=='pages/complaint/Index'">
					<div class="card_head_tit2 mt15">
					    结案信息
					</div>
					<u-divider></u-divider>
					<u-form-item label="补货发票号"  prop="contactPhone" >
						<u--input  v-model="model.replenishInvoiceNo"    placeholder="补货发票号"  />
					</u-form-item>
					<u-form-item class="mt15" label="结案原因" prop="closeReason" required>
						<u--textarea :count="!!model.closeReason"   v-model="model.closeReason"  class="int" maxlength="800"  placeholder="结案原因"  />
					</u-form-item>
				</template>
				
				</u--form>
            </div>
        </div>
		<!-- 审批 -->
        <u-modal v-model:show="dialogShow" @confirm="dialogConfirm"  @close="dialogShow = false" @cancel="dialogShow = false" confirmColor="#849EB2" :title="dialogTitle" show-cancel-button>
            <div class="addInfo">
                <div class="card">
					<u--form labelPosition="top" labelWidth="auto" >
                    <u-form-item label="审批备注" prop="approvalRemark" required>
                    	<u--textarea :count="!!popInfo.approvalRemark"   v-model="popInfo.approvalRemark"  class="int" maxlength="800"  placeholder="审批备注"  />
                    </u-form-item>
					</u--form>
                </div>
            </div>
        </u-modal>
		
		<!-- 质量quality -->
		<u-modal v-model:show="qualityShow" @confirm="qualityConfirm('1')" closeOnClickOverlay  @close="qualityShow = false" @cancel="qualityConfirm('0')" closeable confirmColor="#849EB2" title="质量管理" confirm-text="提交意见" cancel-text="保存草稿" show-cancel-button>
		    <div class="addInfo" style="height: 60vh; overflow-y: scroll; overscroll-behavior: contain;">
		        <div class="card">
					<u--form labelPosition="top" labelWidth="auto" >
						<!-- <u-form-item label="判定 " prop="judge" required >
							<zxz-uni-data-select
								clearable  placeholder="请选择"  
								v-model="qualityInfo.judge" 
								:localdata="dict['judge']">
							</zxz-uni-data-select>
						</u-form-item>
						
						<u-form-item label="判定描述" prop="judgeDesc" >
							<u--textarea :count="!!qualityInfo.judgeDesc"   v-model="qualityInfo.judgeDesc"  class="int" maxlength="800"  placeholder="判定描述"  />
						</u-form-item> -->
						<u-form-item label="原因分析" prop="causeAnalysis" >
							<u--textarea :count="!!qualityInfo.causeAnalysis"   v-model="qualityInfo.causeAnalysis"  class="int" maxlength="800"  placeholder="原因分析"  />
						</u-form-item>
						<u-form-item label="纠正措施" prop="correctiveAction" >
							<u--textarea :count="!!qualityInfo.correctiveAction"   v-model="qualityInfo.correctiveAction"  class="int" maxlength="800"  placeholder="纠正措施"  />
						</u-form-item>
						<u-form-item label="预防措施" prop="preventiveMeasure" >
							<u--textarea :count="!!qualityInfo.preventiveMeasure"   v-model="qualityInfo.preventiveMeasure"  class="int" maxlength="800"  placeholder="预防措施"  />
						</u-form-item>
						<u-form-item label="预计完成时间"  prop="expectedCompletionDate" @click="onCalendarShow()">
							<u--input  v-model="qualityInfo.expectedCompletionDate" disabled
							disabledColor="#ffffff"
							placeholder="请选择预计完成时间"
							border="none"    />
						</u-form-item>
					</u--form>
		        </div>
		    </div>
		</u-modal>
		<u-datetime-picker
			:show="calendarShow"
			v-model="currentTime"
			confirmColor="#849EB2"
			closeOnClickOverlay
			@confirm="onCalendarConfirm"
			@cancel="calendarShow = false"
			@close="calendarShow = false"
			mode="date"
		></u-datetime-picker>
		
		<!-- 回退 -->
		<u-modal v-model:show="returnPop" @confirm="doReturn" closeOnClickOverlay  @close="returnPop = false" @cancel="returnPop = false" closeable confirmColor="#849EB2" title="服务单退回" confirm-text="确认退回" show-cancel-button>
		    <div class="addInfo">
		        <div class="card">
					<u--form labelPosition="top" labelWidth="auto" >
		            <u-form-item label="退回原因" prop="remark" required>
		            	<u--textarea :count="!!returnInfo.remark"   v-model="returnInfo.remark"  class="int" maxlength="800"  placeholder="退回原因"  />
		            </u-form-item>
					</u--form>
		        </div>
		    </div>
		</u-modal>
		
		
        <related-list :data="info.relatedList" :id="routerQuery.id" :baseInfo="info.basic" title="相关信息"></related-list>
        
		<approve-list v-if="info.historyRelatedList && info.historyRelatedList.length>0 "  :list="info.historyRelatedList" :id="routerQuery.id" :baseInfo="info.basic" title="客诉受理历史"></approve-list>
        
		<template v-if="routerQuery.type=='pages/complaint/Index'">
            <footer-btn v-if="['NEW'].includes(info.basic.caseStageCode)" cancel_btn_text="编辑" :cancelBtnShow="info.isEdit=='1'"
                @oncancel="router.push({name: 'complaintEdit',params: {id:routerQuery.id}})" :confirm_btn_text="info.btnName"
                @onconfirm="save(info.btnCode)"
            ></footer-btn>
            <footer-btn v-if="['FKYJ','GCYJ','SMT','ZLYJ'].includes(info.basic.caseStageCode)" 
				:confirmBtnShow="info.showBtn=='1'" :confirm_btn_text="info.btnName"
				:cancelBtnShow="['FKYJ'].includes(info.basic.caseStageCode) && hasPermission('CaseReturn','U')" cancel_btn_text="服务单退回" 
                @onconfirm="save(info.btnCode)" @oncancel="returnPop = true"
            >
			</footer-btn>
			<footer-btn v-if="['CLFA'].includes(info.basic.caseStageCode) && info.showBtn=='1'" :cancelBtnShow="info.basic.qaNeed ==0"   cancel_btn_text="质量部介入" :confirm_btn_text="info.btnName"
			    @onconfirm="save(info.btnCode)" @oncancel="submitToQa()"
			>
			</footer-btn>
			<footer-btn v-if="
				info.basic.isClosed=='0' &&  
				info.basic.caseStageCode=='CLOSE' && 
				(hasPermission('SubmitCaseClose','U') || (userInfo.is_admin=='1'))" 
				:cancelBtnShow="false"  confirm_btn_text="结案关闭"
			    @onconfirm="doClose"
			>
			</footer-btn>
        </template>
        <ApproveBtn v-if="routerQuery.type=='pages/complaint/ApproveList'  && info.basic.caseStageCode=='ECSP' && hasPermission('CaseApproval','U')" cancel_btn_text="拒绝"  confirm_btn_text="通过"
            @oncancel="operate('cancel')" @onconfirm="operate('confirm')"
        ></ApproveBtn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import ProgressStatus from '@/components/ProgressStatus.vue';
import RelatedList from '@/components/RelatedList.vue';
import ApproveList from './components/ApproveList.vue';
import ApproveBtn from './components/ApproveBtn.vue';
import ProductList from './components/ProductList.vue';
import SolutionList from './components/SolutionList.vue';
import {querySalesLeadDetails,caseDo,saveEdit,caseClose,saveOpinion,caseReturn,submitQa}  from  '@/api/complaint'
import {asyncUpload}  from  '@/api/common'
import { isArray } from 'lodash';
import dayjs from "dayjs";
	const prop =  defineProps({
		isComponent: {
			type: Boolean,
			default: false,
		},
		id: String,
		type:String
	})
    const router = useRouter();
	const util = inject("util");
    const dictStore = useDictStore()
    const routerQuery =  prop;
    const info = ref({
        basic:{},
        btnName:'',
		showBtn:null,
        relatedList:[],
		isEdit:0,
    });
    //提交用到
    const model = ref({
        id:routerQuery.id,
  //       feedback_img:[],
		// break_img:[],
		// no_img:[],
		// other_img:[],
		// factory_option_img:[],
        caseStageCode :info.value.btnCode,
    })
	const fileFild =new Map([
	    ['break_img',{key:'break_img',message:'损坏的具体照片',maxLenth: 1,}],
	    ['feedback_img',{key:'feedback_img',message:'照片',maxLenth: 3,}],
	    ['no_img',{key:'no_img',message:'带有编号的照片',maxLenth: 1,}],
		['other_img',{key:'other_img',message:'带有编号的照片',maxLenth: 1,}],
	    ['factory_option_img',{key:'factory_option_img',message:'工厂照片',maxLenth: 3,}],
	])
    const getInfo = () =>{
        querySalesLeadDetails(routerQuery.id).then((res) => {
            // info.value = res.data
			const { basic,btnCode,btnName,historyRelatedList,stageList,orderDetailList,qaOpinion,solutionList,showBtn,isEdit } = res.data
			//上传文件的处理
			fileFild.forEach (function(value, key) {
				basic[key] =  res.data.basic[key]?res.data.basic[key].split(';'):[] 
			})
			qualityInfo.value = qaOpinion || {};
			info.value = { basic,btnCode,btnName,historyRelatedList,stageList,orderDetailList,qaOpinion,solutionList,showBtn,isEdit };// Object.assign({},basic)
			let curinfo = stageList.find(i=>i.caseStageCode=== btnCode)
			// uni.setNavigationBarTitle({
			// 　　title:`编辑${curinfo.caseStage}`
			// })
        }).catch((err) => {
        });
    }
    const status_list = ref([]);
    const init = async() =>{
        const result = await dictStore.getDict('case_stage');
        status_list.value = result.map(item=>{
            return {label:item.dname,value:item.dval}
        })
        await dictStore.getDictBatch(['feedback_person_type','case_class','solution_type','factory_code','sheet_status','judge']);
        getInfo()
    }
	let isSubmit = false
    //审批用到
    const dialogShow = ref(false)
    const dialogTitle = ref('提示')
    const dialogConfirm = ()=>{
		if(isSubmit) return false
        if(operateType.value=='cancel'){
            popInfo.value.approvalStatus ='拒绝'
        }else if(operateType.value=='confirm'){
            popInfo.value.approvalStatus ='通过'
        }
		popInfo.value.caseStageCode = info.value.btnCode;
        if((!popInfo.value.approvalStatus || popInfo.value.approvalStatus=='')) return uni.showToast({
			title: '备注必须填写',
			duration: 1500,
			icon:'none'
		});
		popInfo.value.serviceType=info.value.basic.serviceType
		isSubmit = true
        caseDo(popInfo.value).then((res)=>{
			uni.showModal({
				title: '提示',
				content: res.msg || '操作成功',
				showCancel:false,
				success: function (res) {
					isSubmit = false
					if (res.confirm) {
						// router.replace({name: 'complaint'})
						router.back()
					} else if (res.cancel) {
						console.log('用户点击取消');
					}
				}
			});
        }).catch(() => {
        	isSubmit = false
        })
    }
    
	const popInfo = ref({
        id:routerQuery.id,
    });
    const operateType = ref(null)
    const operate = (type) => {
        if(type=='cancel'){
            dialogTitle.value='拒绝操作'
        }else if(type=='confirm'){
            dialogTitle.value='通过操作'
        }
        operateType.value = type;
        dialogShow.value=true
    }

    //提交操作/保存
    const requiredFild = [
        {key:'solutionType',message:'请选择处理类型',required: true,},
        {key:'solution',message:'请填写方案',required: true,},
        {key:'caseBtnStage',message:'caseBtnStage不能为空',required: true,},
    ]
	const imgFileFild = new Map([
	    ['feedback_img',{key:'feedback_img',message:'照片',maxLenth: 1,}],
	])
	
	
    const save = (caseStageCode) => {
		if(isSubmit) return false
		console.log(info.value.basic.caseStageCode)
        if(info.value.basic.caseStageCode=='GCYJ'){
            if(!model.value.factory_option || model.value.factory_option==''){
				return uni.showToast({
					title:'请填写工厂意见',
					duration: 1500,
					icon:'none'
				});
			}
        }
		//处理附件
		imgFileFild.forEach (function(fildInfo, key) {
			if(isArray(model.value[key])) {
				model.value[key].forEach((item,index) => {
				    model.value[`${key}${index+1}`] = item.url
				});
			}
		})
		let post =Object.assign({
			serviceType:info.value.basic.serviceType
		},model.value)
		imgFileFild.forEach (function(fildInfo, key) {
			delete post[key]
		})
        if(caseStageCode)post.caseStageCode = caseStageCode ;
		if(info.value.basic.qaNeed) post.qaNeed = info.value.basic.qaNeed ;
		isSubmit = true
        caseDo(post).then((res)=>{
            uni.showModal({
            	title: '提示',
            	content: res.msg || '操作成功',
            	showCancel:false,
            	success: function (res) {
					isSubmit = false
            		if (res.confirm) {
						router.back()
            		} else if (res.cancel) {
            			console.log('用户点击取消');
            		}
            	}
            });
        }).catch(() => {
			isSubmit = false
		})
    }
	
	
	const afterRead = async(event,type) =>{
		let files = [].concat(event.file)
		if(!model.value[type]) model.value[type] = [];
		let fileListLen =  model.value[type].length
		files.map((item) => {
			model.value[type].push({
				...item,
				status: 'uploading',
				message: '上传中'
			})
		})
		for (let i = 0; i < files.length; i++) {
			try {
				const {data} = await asyncUpload(files[i].url)
				let item = model.value[type][fileListLen];
				model.value[type].splice(fileListLen, 1, {
				  ...item,
				  status: 'success',
				  message: '',
				  url: data.url,
				});
				fileListLen++;
			} catch (e) {
				console.log(e)
				uni.showToast({
					title: "图片上传失败",
					icon: "none"
				})
			}
		}
	}
	
	const beforeDelete = ({file,index},type) =>{
		uni.showModal({
			title: '提示',
			content: '是否删除该附件？',
			success: function (res) {
				if (res.confirm) {
					model.value[type].splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
	
	
	const doClose = () =>{
		let post =Object.assign({},model.value)
		caseClose(post).then((res)=>{
		    uni.showModal({
		    	title: '提示',
		    	content: res.msg || '操作成功',
		    	showCancel:false,
		    	success: function (res) {
		    		if (res.confirm) {
						router.back()
		    		} else if (res.cancel) {
		    			console.log('用户点击取消');
		    		}
		    	}
		    });
		})
	}
	
	
	
	const findshow = computed(()=>(btnCode)=>{ 
		//阶段对应显示的
	    if(info.value.basic.isClosed=='1') return true;
	   let index = info.value.stageList.findIndex(i=> i.case_stage_code === btnCode)
	   if(index>-1){
		   const showArr = info.value.stageList.filter((item, i, arr) => i>index).map(item => item.case_stage_code);
		   return showArr.includes(info.value.basic.caseStageCode)>0
	   }else{
			return false;
	   }
	})
	
	const deleteFild = ['accName','createdStaffName','updateStaffName','factory_option_img','feedback_img','other_img','no_img','factory_option_img','break_img','closeStaffName'];
	const submit = (caseStageCode) => {
		if(isSubmit) return false
	    let postData = {}
		//处理附件
		if(info.value.basic.serviceType=='售后服务'){
			postData.detailList = info.value.orderDetailList.map(
				detail =>{
					delete detail.onSiteImg;
					delete detail.breakImg;
					delete detail.noImg;
					delete detail.otherImg;
					return detail;
				}
			);
		}
		
	    for(var key in info.value.basic){
	        if(!deleteFild.includes(key))
	            postData[key] = info.value.basic[key];
	    }
	    postData["caseStageCode"]= caseStageCode;
		isSubmit = true
	    saveEdit(postData).then((res)=>{
			uni.showModal({
				title: '提示',
				content: info.value.id ?'修改成功':'保存成功',
				showCancel:false,
				success: function (confirmres) {
					isSubmit = false
					if (confirmres.confirm) {
						if(prop.isComponent)emit('confirm', res)
						else{
							router.back()
						}
					} else if (confirmres.cancel) {
						uni.$emit("refresh", {refresh: true}); 
						console.log('用户点击取消');
					}
				}
			});
	    }).catch(() => {
	    	isSubmit = false
	    })
	}
	
	// 质量管理相关
	const qualityShow = ref(false);
	const qualityInfo = ref({})
	const qualityConfirm = (submitStatus)=>{
		if(isSubmit) return false
		qualityInfo.value.caseId=routerQuery.id
	    if(submitStatus) qualityInfo.value.submitStatus = submitStatus
		
	 //    if((!popInfo.value.approval_remark || popInfo.value.approval_remark=='')) return uni.showToast({
		// 	title: '备注必须填写',
		// 	duration: 1500,
		// 	icon:'none'
		// });
		isSubmit = true
	    saveOpinion(qualityInfo.value).then((res)=>{
			uni.showModal({
				title: '提示',
				content: res.msg || '操作成功',
				showCancel:false,
				success: function (res) {
					isSubmit = false
					if (res.confirm) {
						router.back()
					} else if (res.cancel) {
						console.log('用户点击取消');
					}
				}
			});
	    }).catch(() => {
	    	isSubmit = false
	    })
	}
	
	const calendarShow = ref(false)
	const calendarType = ref(null)
	const currentTime = ref(Date.now());
	const onCalendarShow  = (type='expectedCompletionDate') =>{
	    calendarType.value = type
	    calendarShow.value = true;
	}
	const onCalendarConfirm = ({mode,value}) => {
	    qualityInfo.value[calendarType.value] = `${dayjs(value).format('YYYY-MM-DD')}`;
	    // info.value[calendarType.value] = `${currentDate.value.join('-')} ${currentTime.value.join(':')}`
	    calendarShow.value = false
	};
	
	//服务单处理方案相关
	const solutionChange=(data)=>{
		model.value.solutionList = data
		popInfo.value.solutionList = data
		debugger
	}
	
	// 退回操作
	const returnPop = ref(false);
	const returnInfo = ref({
		caseId:routerQuery.id,
	})
	const doReturn = ()=>{
		uni.showModal({
			title: '提示',
			content: '是否退回该服务单？',
			success: function (unires) {
				if (unires.confirm) {
					
					caseReturn(returnInfo.value).then((res)=>{
						uni.showModal({
							title: '提示',
							content: res.msg || '操作成功',
							showCancel:false,
							success: function (caseRes) {
								if (caseRes.confirm) {
									router.back()
								} else if (caseRes.cancel) {
									console.log('用户点击取消');
								}
							}
						});
					})
					
				} else if (unires.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
	
	const submitToQa = ()=>{
	    submitQa({id:routerQuery.id}).then((res)=>{
			uni.showModal({
				title: '提示',
				content: res.msg || '操作成功',
				showCancel:false,
				success: function (res) {
					if (res.confirm) {
						router.back()
					} else if (res.cancel) {
						console.log('用户点击取消');
					}
				}
			});
	    })
	}
	
	const dom = (url)=>{
		uni.showLoading({
			title: '正在下载……'
		});
		uni.downloadFile({
			url: url, //仅为示例，并非真实的资源
			success: (res) => {
				if (res.statusCode === 200) {
					wx.getFileSystemManager().saveFile({
						tempFilePath: res.tempFilePath, //临时路径
						success: function(saveRes) {
							// console.log('打印res',res)
							uni.hideLoading();
							// uni.showToast({
							// 	icon: 'success',
							// 	mask: true,
							// 	// title: '文件已保存：' + res.savedFilePath, //保存路径
							// 	title: '下载成功' , 
							// 	duration: 2000,
							// });
							var filePath = saveRes.savedFilePath;
							uni.openDocument({  //新开页面打开文档，支持格式：doc, xls, ppt, pdf, docx, xlsx, pptx。
								 filePath: filePath,
								showMenu: true,
								success: function (res) {
									 console.log('打开文档成功');
								},
								fail: (err) => {
									console.log(err);
									uni.showToast({
										icon: 'none',
										mask: true,
										title: '文件格式不支持打开:[doc, xls, ppt, pdf, docx, xlsx, pptx]',
									});
								},
							});
							//自动打开文档查看
							// setTimeout(() => {
								
							// }, 2000)
						},
					})
				}		
			},
			fail: (err) => {
				console.log(err);
				uni.showToast({
					icon: 'none',
					mask: true,
					title: '文件下载失败',
				});
			},
		});
	}
	
	onShow(() => {
		uni.$once("refresh", (data) => {
			init()
		})
	})
    onMounted(() => {
        init()
    })
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
</style>
<style lang="scss" scoped>
    .page{
        --van-cell-group-inset-padding:0;
        --card-border-radius:5px;
        --card-padding:0 10px;
        --u-divider-line-height:1px;
        --u-divider-margin:0;
        --van-field-label-width:auto;
        --el-table-border:0;
        .addInfo{
			padding: 10px;
			position: relative;
			border-radius: var(--card-border-radius);
			line-height: 1.4;
			width: 100%;
			.card{
				background: none;
				padding: var(--card-padding);
				position: relative;
				border-radius: var(--card-border-radius);
				line-height: 1.4;
				.card_head{
					display: flex;
					align-items: center;
					justify-content: center;
					// border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
					padding: 15px 0 10px 0;
					&_tit{
						display: flex;
						align-items: center;
						font-size: 18px;
						font-weight: 600;
						color: #333333;
						span{
							font-weight: 700;
							font-size: 16px;
							color: #000000;
							margin-right: 5px;
						}
					}
				}
			}
			:deep(){
				.int{
					background: none !important;
					.van-field__value{
						background: #fff;
					}
					// &.van-cell:after{
					//     content: none;
					// }
				}
			}
		}
        
		:deep(){
            .int{
                background: none !important;
                .van-field__value{
                    background: #fff;
                }
                // &.van-cell:after{
                //     content: none;
                // }
            }
        }
        :deep(){
            .int{
                padding: 0;
                padding-top: 20px;
                &:first-child{
                    padding-top: 0px;
                }
                &:last-child{
                    padding-bottom: 20px;
                }
                &.pt0{
                    padding-top: 0px;
                }
                .van-field__value{
                    box-shadow: 0px 1px 4px 0px rgba(0,0,0,0.05);
                    border-radius: 4px;
                    opacity: 1;
                    border: 1px solid #E2E6F6;
                    padding: 8px;
                }
                &.van-cell:after{
                    content: none;
                }
            }
        }
        :deep(.el-select){
        width: 100%;
        line-height: normal;
        --el-disabled-bg-color:none;
        --el-text-color-placeholder:var(--van-field-placeholder-text-color);
        .el-input{
            --el-border:0;
            --el-component-size:24px;
            .el-input__wrapper{
                padding: 0;
                box-shadow:none;
                &.is-focus{
                    box-shadow:none !important;
                }
                .el-input__inner{
                    --el-input-inner-height:auto;
                }
            }
            &.is-focus{
                .el-input__wrapper{
                    padding: 0;
                    box-shadow:none !important;
                    
                }
            }
            &.is-disabled{
                .el-input__inner{
                    --el-text-color-placeholder:var(--van-field-placeholder-text-color);
                    --el-disabled-text-color:var(--van-field-placeholder-text-color);
                }
            }
        }
    }
    }
</style>