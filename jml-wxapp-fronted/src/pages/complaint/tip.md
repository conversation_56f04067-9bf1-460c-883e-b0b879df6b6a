
1.如果客诉明细返回的阶段为：新建，caseStageCode，前端需要加个按钮为：
【编辑】点击后直接跳转至编辑页面，【保存】调用:saveCase 接口
  另外一个按钮从明细中的btnName 值，点击调用caseDo接口：caseStageCode : 阶段code：取值btnCode

若服务类型【serviceType】不为：售后服务：
第一个流程操作：销售提交->反馈意见 ，销售界面操作
显示按钮：
1.当 btnCode为：FKYJ

传值以下 JSON：

{
  "id": "20240203104450517585123",
  "caseStageCode": "FKYJ",
}
字段说明：
id:记录Id
caseStageCode : 阶段code：取值btnCode

第二个流程操作：销售助理 -> 提交结案
2.当 btnCode为：CLOSE

传值以下 JSON：

{
  "id": "20240203104450517585123",
  "caseStageCode": "CLOSE",
  "feedbackRemark": "xxxxxx", 
}
字段说明：
id:记录Id
caseStageCode : 阶段code：取值btnCode
feedbackRemark**: 反馈意见备注**

---------------NB的分割线，至此第一种情况结束-------------------




若服务类型【service_type】为：售后服务：




第一个流程操作：销售提交->反馈意见 ，销售界面操作
1.当btnCode为：FKYJ
传值以下 JSON：

{
  "id": "20240203104450517585123",
  "caseStageCode": "FKYJ",
}
字段说明：
id:记录Id
caseStageCode : 阶段code：取值btnCode

第二个流程操作：销售助理提交->反馈意见 ，销售助理界面操作
2.当btnCode为：CLFA
传值以下 JSON：

{
  "id": "20240203104450517585123",
  "caseStageCode": "CLFA",
  "feedbackPersonType": "XXX",
  "feedbackType": "22",
  "feedbackClass": "123-456-7890",
  "factoryCode": "KML",
  "feedbackRemark": "xxxxxx",
}
字段说明：
id:记录Id
caseStageCode : 阶段code：取值btnCode
feedbackPersonType**: **反馈人类型，字典值 取值：feedback_person_type
feedbackType：反馈方式，字典值 取值：feedback_type
feedbackClass：反馈类型，字典值 取值：feedback_class
factoryCode：工厂code，字典取值:factory_code
feedbackRemark：反馈意见备注，文本区

第三个流程操作：销售总监提交->解决方案 ，销售总监界面操作
3.当btnCode为：SMT

传值以下 JSON：

{
  "id": "20240203091542535942080",
  "caseStageCode": "SMT",
  "solutionList": [
       {
        "caseId": "20240203091542535942080",
        "orderDetailId": "20240203091542787275176",
        "returnNum": 2,
        "exchangeNum": 1,
        "reworkNum": 0,
        "discountsAmount": 50,
        "claimantAmount": 100,
        "solutionRole": "Sales Director",
        "solutionType": "Return",
        "undoReason": "Not applicable"
      },
      {
        "caseId": "20240203091542535942080",
        "orderDetailId": "20240203091542787484861",
        "returnNum": 0,
        "exchangeNum": 3,
        "reworkNum": 1,
        "discountsAmount": 20,
        "claimantAmount": 75,
        "solutionRole": "Quality Department",
        "solutionType": "Exchange",
        "undoReason": "Incorrect item received"
      }
  ]
}
id:记录Id
caseStageCode : 阶段code：取值btnCode
caseId：记录Id
orderDetailId:订单明细Id

solutionType :字典值取值：solution_type
returnNum：退货数量 若solutionType 选择 ：退货，则该字段赋值，若不选择，则不传
exchangeNum：换货数量 若solutionType 选择 ：换货，则该字段赋值，若不选择，则不传
reworkNum：返工数量 若solutionType 选择 ：返工，则该字段赋值，若不选择，则不传
discountsAmount：折让金额 若solutionType 选择 ：折让，则该字段赋值，若不选择，则不传
claimantAmount：索赔金额 若solutionType 选择 ：索赔，则该字段赋值，若不选择，则不传
undoReason：为解决原因（文本区） 若solutionType 选择 ：不予解决，则该字段赋值，若不选择，则不传



第四个流程操作：质量部提交->二次审批 ，质量人员界面操作
3.当btnCode为：ECSP

传值以下 JSON：

{
  "id": "20240203091542535942080",
  "caseStageCode": "ECSP",
  "solutionList": [
       {
        "caseId": "20240203091542535942080",
        "orderDetailId": "20240203091542787275176",
        "returnNum": 2,
        "exchangeNum": 1,
        "reworkNum": 0,
        "discountsAmount": 50,
        "claimantAmount": 100,
        "solutionRole": "Sales Director",
        "solutionType": "Return",
        "undoReason": "Not applicable"
      },
      {
        "caseId": "20240203091542535942080",
        "orderDetailId": "20240203091542787484861",
        "returnNum": 0,
        "exchangeNum": 3,
        "reworkNum": 1,
        "discountsAmount": 20,
        "claimantAmount": 75,
        "solutionRole": "Quality Department",
        "solutionType": "Exchange",
        "undoReason": "Incorrect item received"
      }
  ]
}
id:记录Id
caseStageCode : 阶段code：取值btnCode
caseId：记录Id

orderDetailId:订单明细Id
solutionType :字典值取值：solution_type
returnNum：退货数量 若solutionType 选择 ：退货，则该字段赋值，若不选择，则不传
exchangeNum：换货数量 若solutionType 选择 ：换货，则该字段赋值，若不选择，则不传
reworkNum：返工数量 若solutionType 选择 ：返工，则该字段赋值，若不选择，则不传
discountsAmount：折让金额 若solutionType 选择 ：折让，则该字段赋值，若不选择，则不传
claimantAmount：索赔金额 若solutionType 选择 ：索赔，则该字段赋值，若不选择，则不传
undoReason：为解决原因（文本区） 若solutionType 选择 ：不予解决，则该字段赋值，若不选择，则不传



第五个流程操作：总经理提交->关闭 ，总经理人员界面操作
3.当btnCode为：CLSOE

传值以下 JSON：

{
  "id": "20240203091542535942080",
  "caseStageCode": "CLSOE",
  "approvalStatus": "通过",
  "approvalRemark": "备注",
  "solutionList": [
       {
        "caseId": "20240203091542535942080",
        "orderDetailId": "20240203091542787275176",
        "returnNum": 2,
        "exchangeNum": 1,
        "reworkNum": 0,
        "discountsAmount": 50,
        "claimantAmount": 100,
        "solutionRole": "Sales Director",
        "solutionType": "Return",
        "undoReason": "Not applicable"
      },
      {
        "caseId": "20240203091542535942080",
        "orderDetailId": "20240203091542787484861",
        "returnNum": 0,
        "exchangeNum": 3,
        "reworkNum": 1,
        "discountsAmount": 20,
        "claimantAmount": 75,
        "solutionRole": "Quality Department",
        "solutionType": "Exchange",
        "undoReason": "Incorrect item received"
      }
  ]
}
id:记录Id
caseStageCode : 阶段code：取值btnCode
approvalStatus : 审批状态：通过/拒绝
approvalRemark: 审批备注 ，approvalStatus 为拒绝时，必填

caseId：记录Id
orderDetailId:订单明细Id
solutionType :字典值取值：solution_type
returnNum：退货数量 若solutionType 选择 ：退货，则该字段赋值，若不选择，则不传
exchangeNum：换货数量 若solutionType 选择 ：换货，则该字段赋值，若不选择，则不传
reworkNum：返工数量 若solutionType 选择 ：返工，则该字段赋值，若不选择，则不传
discountsAmount：折让金额 若solutionType 选择 ：折让，则该字段赋值，若不选择，则不传
claimantAmount：索赔金额 若solutionType 选择 ：索赔，则该字段赋值，若不选择，则不传
undoReason：为解决原因（文本区） 若solutionType 选择 ：不予解决，则该字段赋值，若不选择，则不传

----------------第一版---------------------
1.如果客诉明细返回的阶段为：新建，case_stage，前端需要加个按钮为：
【编辑】点击后直接跳转至编辑页面，【保存】调用:saveCase 接口
  另外一个按钮从明细中的btnName 值，点击调用caseDo接口：case.case_stage_code : 阶段code：取值btnCode

若服务类型【service_type】不为：售后服务：
显示按钮：
1.当btnName为： 销售助理受理，且btnCode为：FKYJ
传值以下：
case.id:记录Id
case.case_stage_code : 阶段code：取值btnCode

2.当btnName为：结案，且btnCode为：CLOSE
传值以下：
case.id:记录Id
case.case_stage_code : 阶段code：取值btnCode
case.feedback_remark:反馈意见备注 文本区域

若服务类型【service_type】为：售后服务：
-内部人员提交明细界面：
1.当btnName为： 销售助理受理，且btnCode为：FKYJ
传值以下：
case.id:记录Id
case.case_stage_code : 阶段code：取值btnCode
case.feedback_person_type:反馈人类型，字典值
case.feedback_remark:反馈意见备注
case.feedback_img1/case.feedback_img2case./feedback_img3:反馈意见照片
case.case_class:事件分类 下拉选多选-字典

-销售助理提交明细界面：
2.当btnName为：销售总监受理，且btnCode为：CLFA
传值以下：
case.id:记录Id
case.case_stage_code : 阶段code：取值btnCode
case.solution_type: 处理方案 -多选-字典值
case.return_num - 退货（张数） 数字
case.exchange_num- 换货（张数） 数字
case.rework_num - 返工（张数）
case.discounts_amount - 折让（张数） 数字
case.claimant_amount -索赔（金额） 数字

-销售总监提交明细界面：
3.当btnName为：提交，且btnCode为：SMT
传值以下：
case.id:记录Id
case.case_stage_code : 阶段code：取值btnCode
case.solution_type: 处理方案 -多选-字典值
case.return_num - 退货（张数） 数字
case.exchange_num- 换货（张数） 数字
case.rework_num - 退货（张数） 数字
case.discounts_amount - 退货（张数） 数字
case.claimant_amount -索赔（金额） 数字

-工厂人员提交明细界面：
4.当btnName为：总经理审批，且btnCode为：ECSP
传值以下：
case.id:记录Id
case.case_stage_code : 阶段code：取值btnCode
case.factory_opino: 工厂意见 -文本区域
case.actory_option_img1- 工厂照片1
case.actory_option_img2- 工厂照片2
case.actory_option_img3- 工厂照片3

-总经理人员提交明细界面：
4.当客诉明细返回的阶段为：ECSP，则对应的按钮改为：
拒绝：
传值：case.case_stage 为 btnCode值
传值：case.approva_status 为 REJECT
通过：
传值：case.case_stage 为 btnCode值
传值：case.approva_status 为 REJECT
case.approval_remark- 审批备注
