<template>
    <div class="page pd100">
		<u--form labelPosition="top" labelWidth="auto" ref="form1" >
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">{{ info.serviceType || '客诉类型'  }}</div>
                </div>
				
				
				<u-form-item label="地区" prop="info.district" required>
					<zxz-uni-data-select clearable placeholder="请选择"  v-model="info.district" dataText="district" @change="areaChange" dataValue="district"  :localdata="userInfo.staffAreaList"></zxz-uni-data-select>
				</u-form-item>
				<u-form-item label="公司"  prop="accountId" required>
					<load-selector
					ref="accountDom"
					 v-model="info.accountId" filterable  @change="accountChange"  load_url="/api/account/queryListByStaff">
					 <template #empty>
					     <div @click="addShowPop('accountId')"  class="sel_add">
					         <u-icon name="plus" label="新增" bold/> 
					     </div>
					 </template>
					 </load-selector>
				</u-form-item>
				<!-- <u-form-item label="联络人"  prop="contactName" required>
					<u--input  v-model="info.contactName"    placeholder="联络人"  />
				</u-form-item> -->
				
				<u-form-item label="联系人"  prop="info.contactId">
					<zxz-uni-data-select @change="contactChange" :disabled="!info.accountId" 
						clearable filterable placeholder="请选择"  
						v-model="info.contactId" 
						dataText="name" dataValue="id"
						ref="contactDom"
						:localdata="contactList">
						<template #empty>
						    <div @click="addShowPop('contactId')"  class="sel_add">
						        <u-icon name="plus" label="新增" bold/> 
						    </div>
						</template>
					</zxz-uni-data-select>
				</u-form-item>
				
				<u-form-item label="电话"  prop="contactPhone" required>
					<u--input  v-model="info.contactPhone"    placeholder="电话"  />
				</u-form-item>
				<u-form-item label="销售人员"  prop="salesStaffId" required>
					<zxz-uni-data-select clearable filterable placeholder="请选择"  v-model="info.salesStaffId" dataText="name" dataValue="id"  :localdata="userList"></zxz-uni-data-select>
				</u-form-item>
				
				<u-form-item label="客服服务留言"  prop="createRemark">
					<u--textarea :count="!!info.createRemark"  maxlength="800"  v-model="info.createRemark"  placeholder="客服服务留言"  />
				</u-form-item>
				
				<template v-if="info.serviceType=='售后服务'">
					<u-form-item label="反馈方式" prop="info.feedbackType" required>
						<zxz-uni-data-select clearable  placeholder="请选择"  v-model="info.feedbackType"   :localdata="dict['feedback_type']"></zxz-uni-data-select>
					</u-form-item>
					<u-form-item label="反馈人类型" prop="info.feedbackPersonType" required>
						<zxz-uni-data-select clearable  placeholder="请选择"  v-model="info.feedbackPersonType"   :localdata="dict['feedback_person_type']"></zxz-uni-data-select>
					</u-form-item>
					<u-form-item label="板材状态" prop="info.sheetStatus" required>
						<zxz-uni-data-select clearable placeholder="请选择"  v-model="info.sheetStatus"   :localdata="dict['sheet_status']"></zxz-uni-data-select>
					</u-form-item>
					<u-form-item label="是否现场查看" labelPosition="left" prop="info.onsiteInspection" required>
						<view style="display: flex; justify-content: flex-end; ">
							<!-- <u-switch v-model="info.onsiteInspection" activeValue="是" inactiveValue="否" activeColor="#849EB2"></u-switch> -->
							<up-radio-group 
							    v-model="info.onsiteInspection"
								activeColor="#849EB2"
							    placement="row">
								<view style="display: flex; justify-content: flex-end; width: 100%;">
								<up-radio  label="是"  name="是"></up-radio>
								<up-radio  label="否" :customStyle="{marginLeft: '15px'}" name="否"></up-radio>
								</view>
							</up-radio-group>
						</view>
					</u-form-item>
					<ProductList @change="change" :dataList="detailList"></ProductList>
				</template>	
				
				<u-form-item label="上传文件"  prop="attachment">
					<view>
					<u-upload
						maxCount="1"
						@after-read="afterRead($event,'attachment')"
						:deletable="true"
						accept="all"
						uploadIcon="file-text"
						:sizeType="['compressed']"
						:before-delete="(file,detail)=>beforeDelete(file,detail)"
						@delete="beforeDelete($event,'attachment')"
						:fileList="info.attachment"
					>
						
					</u-upload>
					<view v-if="info.attachment">
						<view v-for="(item,index) in info.attachment" :key="index"  key="index"> {{item.name}} </view>
					</view>
					</view>
				</u-form-item>
			</div>
			
        </div>
		</u--form>
        <footer-btn  cancel_btn_text="保存草稿" :cancelBtnShow="isEdit=='1'" :confirm_btn_text="otherInfo.btnName || '提交受理'" @oncancel="save('NEW')" @onconfirm="save(otherInfo.btnCode?otherInfo.btnCode:'FKYJ')"></footer-btn>

        <floating-window></floating-window>
		
		<u-popup v-model:show="addShow" round="10" mode="bottom" zIndex="998" closeOnClickOverlay @close="addShow=false">
		    <div class="iframeAddInfo"  v-if="addShow" :key="contactPopKey+accountPopKey" :style="{ height: '80vh', position:'relative', }" > 
		        <company ref="accountPop" :key="accountPopKey" v-if="addInfo.field=='accountId'" @confirm="iframeConfirm($event,'accountPop','accountDom')" isComponent></company>
		        <contact ref="contactPop" :key="contactPopKey" :accountId="info.accountId" v-if="addInfo.field=='contactId'" @confirm="iframeConfirm($event,'contactPop','contactDom')" isComponent></contact>
		        <!-- <opportunity :accountId="info.account_id" v-if="addInfo.field=='opportunity_id'" @confirm="iframeConfirm($event,'opportunity')" isComponent></opportunity> -->
		    </div>
		</u-popup>
		
		<u-datetime-picker
			:show="calendarShow"
			v-model="currentTime"
			confirmColor="#849EB2"
			closeOnClickOverlay
			@confirm="onCalendarConfirm"
			@cancel="calendarShow = false"
			@close="calendarShow = false"
			mode="date"
		></u-datetime-picker>
	</div>
</template>
<script setup>
	import ProductList from './components/ProductList.vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import LoadSelector from '@/components/LoadSelector.vue';
import {querySalesLeadDetails,saveEdit,queryProblemClass}  from  '@/api/complaint'
import {queryUserList,upload,asyncUpload}  from  '@/api/common'
import {queryListByAccountId,queryListByStaff} from '@/api/company'
import company from '../company/Edit.vue';
// import opportunity from '../opportunity/Edit.vue';
import contact from '../contact/Edit.vue';
	const prop =  defineProps({
		id: String,
		isComponent: {
			type: Boolean,
			default: false,
		},
		accountId:{
			type: String,
			default: null,
		},
		serviceType:{
			type: String,
			default: null,
		},
	})
	const lodash = inject("lodash");
	const emit = defineEmits(['confirm'])
    const router = useRouter();
	const detailList = ref([])
    const dictStore = useDictStore()
    const companyList = ref([])
	const util = inject("util");
    const deleteFild = ['accName','createdStaffName','updateStaffName','factory_option_img','feedback_img','other_img','no_img','factory_option_img','break_img','closeStaffName'];
    let requiredFild = [
		{key:'district',message:'请选择地区',required: true,},
		{key:'accountId',message:'请选择公司',required: true,},
		{key:'serviceType',message:'请选择服务类型',required: true,},
		{key:'contactName',message:'请填写联络人',required: true,},
		{key:'contactPhone',message:'请填写电话',required: true,},
		
		// {key:'feedbackType',message:'请选择反馈方式',required: true,},
		// {key:'feedbackPersonType',message:'请选择反馈人类型',required: true,},
		// {key:'sheetStatus',message:'请选择板材状态',required: true,},
	]
	const { userInfo } = useUserStore();
	const info = ref({
	    serviceType:prop.serviceType,
		areaCode:userInfo.staffAreaList.length==1 ? userInfo.staffAreaList[0].areaCode : null,
		district:userInfo.staffAreaList.length==1 ? userInfo.staffAreaList[0].district : null ,
	});
	const otherInfo = ref({});
	watch(() => info.value.serviceType,(newValue, oldValue) => {
		if(newValue){
			//先注释
			// if(newValue=='售后服务'){
			// 	requiredFild = [
			// 	    {key:'subject',message:'请填写主题',required: true,},
			// 	    {key:'account_id',message:'请选择客户',required: true,},
			// 	    {key:'serviceType',message:'请选择服务类型',required: true,},
			// 	    {key:'contact_name',message:'请填写联络人',required: true,},
			// 		{key:'contact_phone',message:'请填写联络电话',required: true,},
			// 		{key:'feedback_person_type',message:'请选择反馈人类型',required: true,},
			// 		{key:'product_name',message:'请填写产品',required: true,},
			// 		{key:'product_qty',message:'请填写数量',required: true,},
			// 		{key:'order_no',message:'请填写订单号',required: true,},
			// 	]
			// }else{
			// 	requiredFild = [
			// 	    {key:'subject',message:'请填写主题',required: true,},
			// 	    {key:'account_id',message:'请选择客户',required: true,},
			// 	    {key:'serviceType',message:'请选择服务类型',required: true,},
			// 	    {key:'contact_name',message:'请填写联络人',required: true,},
			// 		{key:'contact_phone',message:'请填写联络电话',required: true,},
			// 	]
			// }
		}
	},{ immediate: true });
	
	
	//需要处理的上传文件
	const fileFild = new Map([
		['onSiteImg',{key:'onSiteImg',message:'请上传现场照片',maxLenth: 1,required: true}],
		['breakImg',{key:'breakImg',message:'请上传损坏的具体照片',maxLenth: 1,required: true}],
		// ['feedback_img',{key:'feedback_img',message:'请上传反馈意见照片',maxLenth: 3,}],
		['noImg',{key:'noImg',message:'请上传带有编号的照片',maxLenth: 1,required: true}],
		['otherImg',{key:'otherImg',message:'请上传带有编号的照片',maxLenth: 3,}],
		// ['factory_option_img',{key:'factory_option_img',message:'请上传工厂照片',maxLenth: 3,}],
	])
	
    const save = (caseStageCode) => {
        for(let index  in requiredFild){
            var item =  requiredFild[index]
            if(item.required && (!info.value[item.key] || info.value[item.key]=='')) return uni.showToast({
				title:item.message,
				duration: 1500,
				icon:'none'
			});
        };
        let postData = {}
		//处理子附件
		if(info.value.serviceType=='售后服务'){
			debugger
			info.value.detailList = detailList.value.map(
				detail =>{
					
					for (let [key, fildInfo] of fileFild) {
						let arr = Array.isArray(detail[key])?detail[key] : detail[key].split(',')
					    if(Array.isArray(arr)) {
					    	arr.forEach((item,index) => {
					    	    detail[`${key}${index+1}`] = item.url? item.url:item
					    	});
					    }
						delete detail[key];
					}
					debugger
					// delete detail.onSiteImg;
					// delete detail.breakImg;
					// delete detail.noImg;
					// delete detail.otherImg;
					return detail;
				}
			);
		}
		
		
        for(var key in info.value){
            if(!deleteFild.includes(key))
                postData[key] = info.value[key];
        }
		// 处理主附件
		if(postData.attachment && postData.attachment.length>0){
			let {url,name} = postData.attachment[0];
			postData.attachment = url;
			postData.attachmentName = name;
		}
        postData["caseStageCode"]= caseStageCode;
        saveEdit(postData).then((res)=>{
			uni.showModal({
				title: '提示',
				content: info.value.id ?'修改成功':'保存成功',
				showCancel:false,
				success: function (confirmres) {
					if (confirmres.confirm) {
						if(prop.isComponent)emit('confirm', res)
						else{
							// uni.$emit("refresh", {refresh: true}); 
							// router.back();
							router.replace({name: 'complaint'})
						}
					} else if (confirmres.cancel) {
						// uni.$emit("refresh", {refresh: true}); 
						router.replace({name: 'complaint'})
					}
				}
			});
        })
    }
    const isEdit=ref(prop.id?0:1);
	const getInfo = () =>{
        querySalesLeadDetails(prop.id).then((res) => {
			var {btnCode,btnName} = res.data
			otherInfo.value = {btnCode,btnName}
			isEdit.value = res.data?.isEdit
			let { basic,orderDetailList } = res.data
			//上传文件的处理
			
			if(res.data.basic.attachment && res.data.basic.attachment!=''){
			    basic.attachment = [{
					url: basic.attachment , name:basic.attachmentName
				}]
			}else{
			    basic.attachment = []
			}
			
			info.value = basic;// Object.assign({},basic)
			detailList.value = orderDetailList
			if(res.data.contactList) contactList.value = res.data.contactList
        }).catch((err) => {
        });
    }
    
	const change =(data) =>{
		detailList.value = data;
	}
	
    const userList =ref([])
    const init = async() =>{
        await dictStore.getDictBatch(['service_type','feedback_person_type','feedback_type','feedback_class','sheet_status','case_class']);
        (!userList.value || userList.value.length==0) && getUserList()
        if(!prop.account_id){
            let {data} = await queryListByStaff()
            companyList.value = data
        }
        if(prop.id){
            getInfo()
        }
    }
    const getUserList = ()=>{
        queryUserList().then((res) => {
            userList.value = res.data
        }).catch((err) => {
        });
    }
    const problemClassList =ref([])
    watch([()=>info.value.factory,()=>info.value.problem_type],(newvalue,oldvalue)=>{
        const factory = newvalue[0];
        const problem_type = newvalue[1];
        if(factory && problem_type){
            queryProblemClass(factory,problem_type).then((res) => {
                problemClassList.value = res.data
            }).catch((err) => {
            });
        }
    })

	
	const calendarShow = ref(false)
	const calendarType = ref(null)
	const currentTime = ref(Date.now());
	const onCalendarShow  = (type,check = false) =>{
	    calendarType.value = type
	    calendarShow.value = true;
	}
	const onCalendarConfirm = ({mode,value}) => {
	    // popInfo.value[calendarType.value] = `${dayjs(value).format('YYYY-MM-DD')}`;
	    // info.value[calendarType.value] = `${currentDate.value.join('-')} ${currentTime.value.join(':')}`
	    calendarShow.value = false
	};
	
	const afterRead = async(event,type,fileNameKEY) =>{
		let files = [].concat(event.file)
		if(!info.value[type]) info.value[type] = [];
		let fileListLen =  info.value[type].length
		files.map((item) => {
			info.value[type].push({
				...item,
				status: 'uploading',
				message: '上传中'
			})
		})
		for (let i = 0; i < files.length; i++) {
			try {
				const {data} = await asyncUpload(files[i].url)
				let item = info.value[type][fileListLen];
				info.value[type].splice(fileListLen, 1, {
				  ...item,
				  status: 'success',
				  message: '',
				  url: data.url,
				});
				fileListLen++;
			} catch (e) {
				console.log(e)
				uni.showToast({
					title: "上传失败",
					icon: "none"
				})
			}
		}
	}
	
	const beforeDelete = ({file,index},type) =>{
		uni.showModal({
			title: '提示',
			content: '是否删除该附件？',
			success: function (res) {
				if (res.confirm) {
					info.value[type].splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
	const areaChange =(value)=>{
		info.value.areaCode = userInfo.staffAreaList.find(i=>i.district===value)?.areaCode;
	}
	const contactList =ref([])
	const oppList =ref([])
	const accountChange = (id,remove = true,callback) =>{
		debugger
	    if(remove || !id){
	        info.value.contactId = null;
			info.value.contactName = null
			info.value.contactPhone = null
	    }
		if(!id) return;
	    queryListByAccountId(id).then((res) => {
	        contactList.value = res.data.contactList
	        oppList.value = res.data.oppList
			typeof callback == 'function' && callback()
	    })
	}
	const addShow = ref(false)
	const addInfo = reactive({
	    field:'accountId',
	})
	const _this = getCurrentInstance()
	const addShowPop =  (field,refdom) =>{
	    // _this?.refs[refdom]?.blur()
	    addInfo.field = field
	    addShow.value = true
	}
	
	const accountPop = ref()
	const accountDom = ref()
	const contactPop = ref()
	const contactDom = ref()
	const accountPopKey = ref('accountPopKey')
	const contactPopKey = ref('contactPopKey')
	let valueId = null;
	const iframeConfirm = (res,refdom,selDom) =>{
		_this?.refs[selDom]?.toggleSelector()
		valueId = res.data
		info.value[addInfo.field] = valueId
		if(refdom=='accountPop'){
			_this?.refs[selDom]?.getDataPage();
			accountChange(valueId,true)
			accountPopKey.value = `accountPopKey_${valueId}`
		}else if(refdom=='contactPop'){
			contactPopKey.value = `contactPopKey_${valueId}`
			accountChange(info.value.accountId,false,()=>{contactChange(valueId)})//查出待选列表
		}
	    addShow.value = false
	}
	
	const contactChange = (e)=>{
		debugger
		if(!e){
			info.value.contactName = null
			info.value.contactPhone = null
			return
		}
		let contactInfo = contactList.value.find(i=>i.id==e)
		info.value.contactName = contactInfo.name
		info.value.contactPhone = contactInfo.mobilePhone
	}
	
    onMounted(() => {
        init()
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
	.addInfo{
		position: relative;
		width: 100%;
		height: calc(100vh - 240px);
		overflow-y: scroll;
		transform:1;
	}
</style>