
<template>
    <!-- 状态更改组件 -->
    <div class="approve_box">
        <div class="tit">{{title}}</div>
        <div class="list">
            <div v-for="(item,index) in list" :key="index" class="item">
                <div class="title">时间：{{ item.created_time }}</div>
                <div class="content">
					<div class="txt">字段：{{ item.field_name }}</div>
                    <div class="txt">新值：{{ item.new_val }}</div>
                    <div class="txt">旧值：{{ item.old_val }}</div>
                    <div class="txt">用户：{{ item.createdStaffName }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
	
const util = inject("util");
const prop =  defineProps({
    data:{
        type:[Object,Number,String,Array],
        default: null
    },
    title: {
        type:String,
        default: '修改历史'
    },
    list:{
        type: [Array],
        default:[
        ]
    },
})

onMounted(()=>{
})
</script>

<style scoped lang="scss">
    .approve_box{
        // padding: 10px;
		margin-top: 30px;
        .tit{
            font-size: 18px;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
            color: #333333;
            line-height: 25px;
        }
        .list{
            border-radius: 6px;
            background: #fff;
            padding:0 10px;
            .item{
                font-size: 14px;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: rgba(0,0,0,0.9);
                line-height: 22px;
                padding: 10px;
                margin-bottom: 5px;
				display: block;
                .title{
                    display: flex;
                    // justify-content: center;
                    align-items: center;
                    &::before{
                        content: ' ';
                        width: 8px;
                        height: 8px;
                        display: inline-block;
                        background: #4085CB;
                        border-radius: 999px 999px 999px 999px;
                        margin-right: 12px;
                    }
                }
                .content{
                    margin: 0 4px;
                    border-left: 1px solid #4085CB;
                    font-size: 12px;
                    font-family: PingFang SC-Regular, PingFang SC;
                    font-weight: 400;
                    color: rgba(0,0,0,0.4);
                    // margin-bottom: 20px;
                    .txt{
                        margin-left: 15px;
                    }
                }
            }
        }
    }
</style>
