<template>
    <view class="page">
        <view class="head">
            <view class="tool flex">
                <view @click="showTypePickerFun('serviceType','service_type')"  class="item">
                    <u-icon size="6" labelSize="12" labelPos="left" :label="searchQuery.serviceType ? searchQuery.serviceType :'类型'"  name="/static/icon/sel_icon.png" />
                </view>
                <view @click="showTypePickerFun('caseStageCode','case_stage_code')"  class="item">
                    <u-icon size="6" labelSize="12" labelPos="left" :label="searchQuery.caseStageCode ? searchQuery.caseStageCode_text :'阶段'"  name="/static/icon/sel_icon.png" />
                </view>
            </view>
            <view class="search">
                <u-search
                    v-model="searchQuery.multiFiled"
                    placeholder="客户名字/电话/客服服务编号"
                    shape="square"
                    @change="util.debounce(onSearch)"
                	@custom="util.debounce(onSearch)"
                >
                </u-search>
            </view>
            <!-- <view class="search_btn">{{}}</view> -->
        </view>
        <view class="card_list">
            <view class="card_tool">
                <view class="tool">
                    <view @click="reset" v-if="showReset" style="position: absolute; left: 0;  display: flex; align-items: center;">
                        <u-icon size="16" name="close-circle-fill" color="#849EB2" />
                        <span class="" style=" margin-left: 5px; font-weight: normal;">清除筛选</span>
                    </view>
                    <!-- <view @click="showTypePickerFun('casePriority','case_priority')"  class="item">
						<u-icon size="6" labelSize="12" labelPos="left" :label="searchQuery.casePriority ? searchQuery.casePriority :'优先级'"  name="/static/icon/sel_icon.png" />
                    </view> -->
                    <view @click="showPickerFun"  class="item">
                        <u-icon size="6" labelSize="12" labelPos="left" :label="selectedUser && selectedUser.name?selectedUser.name:'员工'" name="/static/icon/sel_icon.png" />
                    </view>
                    <view @click="calendarShow= true" class="item">
                        <u-icon size="6" labelSize="12" labelPos="left" :label="searchQuery.startDate ? `${searchQuery.startDate} - ${searchQuery.endDate}` : '日期'" name="/static/icon/sel_icon.png" />
                    </view>
                </view>
            </view>
            <view class="swiper-item" style="height: calc(100% - 30px )">
				<scroll-view
					scroll-y enable-flex refresher-enabled	
					@scrolltolower="getDataPage()" 
					@refresherrefresh="getFresh"
					:refresher-triggered="refresherTriggered"
					class="scrollHeight"
					:enable-flex="true"
					style="height: 100%;">
            		<uni-swipe-action>
            		<div v-for="(item,index) in data.list" :key="index" class="van-swipe-cell">
            			<uni-swipe-action-item >
							<template  #left>
								<div v-if="item.caseStageCode=='NEW' && item.isEdit == 1"  @click.stop="deleteRecord(item.id)" class="card_left">
									<view class="card_left_btn">
										<u-icon size="20" color="#fff" name="trash" />
										<view class="txt">删除</view>
									</view>
								</div>
							</template>
							<view class="card"  @click="handDetail(item)">
								<view class="card_head">
									<view class="card_head_tit">
										客服服务编号:{{ item.name }}
									</view>
									<view class="head_status bg">
										{{ item.serviceType }}
									</view>
								</view>
								<u-divider></u-divider>
								<view class="card_main">
									<view v-if="item.approvalStatus" class="item">
										<view class="left">审批状态</view>
										<view class="rigth">
											<u-tag v-if="item.approvalStatus=='通过'" bgColor="#88D39B" size="mini" style="display: inline-flex;" borderColor="#88D39B" text="通过" />
											<u-tag v-if="item.approvalStatus=='拒绝'" bgColor="#EB7155" size="mini" style="display: inline-flex;" borderColor="#EB7155" text="拒绝" />
										</view>
									</view>
									<view v-if="item.accName" class="item">
										<view class="left">客户</view>
										<view class="rigth">{{item.accName}}</view>
									</view>
									<view v-if="item.contactName" class="item">
										<view class="left">联络人</view>
										<view class="rigth">{{item.contactName}}</view>
									</view>
									<view v-if="item.contactPhone" class="item">
										<view class="left">电话</view>
										<view class="rigth">{{item.contactPhone}}</view>
									</view>
									<view v-if="item.qaStatus" class="item">
										<view class="left">质量部受理状态</view>
										<view class="rigth">
											<u-tag :bgColor="item.qaStatus=='已受理'?'#88D39B':'#EB7155'" size="mini" style="display: inline-flex;" :borderColor="item.qaStatus=='已受理'?'#88D39B':'#EB7155'" :text="item.qaStatus" />
										</view>
									</view>
									<view v-if="item.factory" class="item">
										<view class="left">工厂</view>
										<view class="rigth">{{item.factory}}</view>
									</view>
									<view class="card_footer">
										<view class="item">
											<view class="left">创建时间</view>
											<view class="rigth">{{item.createdTime}} <span class="link">查看 <u-icon name="arrow-right" size="12" /></span> </view>
										</view>
										<view class="item">
											<view class="left">创建员工</view>
											<view class="rigth">{{item.createdStaffName}}</view>
										</view>
									</view>
								</view>
								<u-divider></u-divider>
								<progress-status :data="item" readonly :status_list="item.stageList" :active="item.caseStageCode" :isClosed="item.isClosed"></progress-status>
							</view>
							<template  #right>
								<view v-if="urlLink==='pages/complaintApprove/Index' && item.isEdit == 1 " class="card_approve">
									<view @click.stop="operate('cancel',item.id)" class="card_right_btn refuse">
										<u-icon size="20" name="/static/icon/refuse.png" />
										<view class="txt">拒绝</view>
									</view>
									<view @click.stop="operate('confirm',item.id)" class="card_right_btn agree">
										<u-icon size="20" name="/static/icon/agree.png" />
										
										<view class="txt">同意</view>
									</view>
								</view>
								<view class="card_right" v-if="urlLink=='pages/complaint/Index' && item.caseStageCode =='NEW' && item.isEdit == 1">
									<view @click.stop="router.push({name: 'complaintEdit',params: {id:item.id}})" class="card_right_btn">
										<u-icon size="20" name="/static/icon/edit.png" />
										<view class="txt">编辑</view>
									</view>
								</view>
							</template>
					
						</uni-swipe-action-item>
					</div>
					</uni-swipe-action>
					<view class="empty_box" v-if="!data.loading && (!data.list ||  data.list.length<=0)">
						<u-empty v-if="!data.list || data.list.length==0" text="未找到信息" :icon="`${config.static}images/empty/data.png`" ></u-empty>
						<view  @click="showAddPop()" v-if="hasPermission('Complaint','C') && urlLink=='pages/complaint/Index'"
							class="btn_add"><u-icon size="20" name="/static/icon/btn_add_icon.png" />  <text class="span">新建 </text></view>
					</view>
               </scroll-view>
           </view>
        </view>

		<u-calendar v-model:show="calendarShow" color="#849EB2"
			monthNum = "24"
			:minDate="dayjs().add(-1, 'year').format('YYYY-MM-DD')"
			:maxDate="dayjs().add(1, 'days').format('YYYY-MM-DD')"
			confirm-disabled-text="请选择结束时间" mode="range"  round="10" 
			@confirm="onCalendarConfirm"
			closeOnClickOverlay
			@close="calendarShow=false" 
		/>
	   <u-picker
			title="创建员工"
			v-model:show="showPicker"
			:columns="[userList]"
			@confirm="onUserConfirm"
			@cancel="showPicker = false"
			keyName="name"
			closeOnClickOverlay
	   />
		
		<u-picker
		    title="请选择"
			v-model:show="showTypePicker"
		    :columns="columns"
		    @confirm="onTypeConfirm"
		    @cancel="showTypePicker = false"
			keyName="dname"
			closeOnClickOverlay
		/>

        <u-modal v-model:show="dialogShow" @confirm="dialogConfirm" :title="dialogTitle" @close="dialogShow = false" @cancel="dialogShow = false" confirmColor="#849EB2" show-cancel-button>
            <view class="addInfo">
                <view class="card">
					<u--form labelPosition="top" labelWidth="auto" ref="form1" >
						<u-form-item label="备注" prop="info.name" required>
							<u--textarea count v-model="popInfo.remark"  class="int"   placeholder="备注"  />
						</u-form-item>
					</u--form>	
                </view>
            </view>
        </u-modal>

        <!-- 选择添加服务类型 -->
        <u-modal v-model:show="addDialogShow" :before-close="beforeClose" @confirm="addConfirm" @close="addDialogShow = false" @cancel="addDialogShow = false" title="选择服务类型" show-cancel-button confirmColor="#849EB2" confirm-button-text="提交">
            <view class="addInfo">
                <view class="card">
                    <u-radio-group v-model="addType" activeColor="#849EB2" iconPlacement="right" placement="column">
						<template v-for="item in dict['service_type']" :key="item.dval">
                        <u-radio :customStyle="{padding:'5px 10px'}"   :label="item.dname" :name="item.dval" />
						</template>
                    </u-radio-group>
					
                </view>
            </view>
        </u-modal>
        <floating-window>
            <view
                v-if="hasPermission('Complaint','C') && urlLink=='pages/complaint/Index'"
                @click="showAddPop()"
                class="item add_btn"
            >
               <u-icon size="20"  name="/static/icon/add.png" />
            </view>
        </floating-window>
    </view>
</template>
<script setup>
	import FloatingWindow from '@/components/FloatingWindow.vue';
	import ProgressStatus from '@/components/ProgressStatus.vue';
	import {queryPageData,deleteReport,caseDo}  from  '@/api/complaint'
	import {queryUserList}  from  '@/api/common'
	import dayjs from "dayjs";
	
    const router = useRouter();
	const util = inject("util");
    const {data} = useDataList()
    const dictStore = useDictStore()
    const finishedText = computed(() => {
        return  data.list.length>0?'没有更多了':'';
    })
    const onSearch = () => {
        initEmpty()
    }
    const routerQuery =  defineProps({});
	var pages = getCurrentPages();
	//获取当前页面
	var currentPage = pages[pages.length - 1];
	const urlLink = ref(currentPage.route)
	console.log(currentPage, '打印当前页路由')
	
    const searchQuery = reactive({
        multiFiled:'',
        caseStageCode:'',
        flag: urlLink.value=='pages/complaint/ApproveList'?'1':'',
        serviceType:'',
        startDate: routerQuery.startDate || '',
        endDate: routerQuery.endDate || '',
        createdStaffId:'',
        pageNo:data.pageNo,
        pageSize:data.pageSize,
    })
	console.log(searchQuery)
	// 下拉刷新
	const refresherTriggered = ref(false);
	const getFresh=()=> {
		console.log('refresh');
		refresherTriggered.value = 'restore'
		initEmpty()
	}
    const getDataPage = (pageNo) =>{
		// debugger
    	if (data.finished || data.loading)return;
        searchQuery.pageNo = pageNo || data.pageNo;
		data.loading = true;
		if(searchQuery.pageNo<=1) data.initEmpty()
        queryPageData(searchQuery).then((res) => {
			if(refresherTriggered.value !== false) refresherTriggered.value = false
            data.list = [...data.list, ...res.data.list]
			data.totalRow = res.data.totalRow
			data.pageNo = data.pageNo + 1 ;
			data.loading = false;
			data.finished = res.data.lastPage;
        }).catch((err) => {
            data.finished = true;
        });
    }

    const deleteRecord = (id)=>{
		uni.showModal({
		    title: '提示',
		    content: '是否删除该记录？',
			success: function (unires) {
				if (unires.confirm) {
					deleteReport(id).then((res) => {
					    uni.showModal({
					        title:'提示',
					        showCancel:false,
					        content: '操作成功',
					    }).then((aa) => {
					        initEmpty()
					    })
					}).catch((err) => {
					});
				}
			}
		})
    }

    const status_list = ref([]);
    const init = async() =>{
        const result = await dictStore.getDict('case_stage');
        status_list.value = result.map(item=>{
            return {label:item.dname,value:item.dval}
        })
		data.finished && (data.finished = false)
        getDataPage()
    }
    const initEmpty =() =>{
        data.initEmpty()
		getDataPage(1)
    }
	onShow(()=>{
		uni.$once("refresh", (data) => {

			uni.$off('refresh');

			initEmpty()

		})
	})
    onMounted(() => {
		init()
    })
    //点击明细
    const handDetail =(item)=>{
        router.push({name: 'complaintInfo',params: {id: item.id,type:urlLink.value}})
    }
    //审批用到
    const dialogShow = ref(false)
    const dialogTitle = ref('提示')
    const dialogConfirm = ()=>{
        if(operateType.value=='cancel'){
            popInfo.value.caseBtnStage='拒绝'
        }else if(operateType.value=='confirm'){
            popInfo.value.caseBtnStage='通过'
        }
        if((!popInfo.value.remark || popInfo.value.remark=='')) return uni.showToast({
			title: '备注必须填写',
			duration: 1500,
			icon:'none'
		});
        caseDo(popInfo.value).then((res)=>{
			uni.showModal({
			    title:'提示',
			    showCancel:false,
			    content: res.msg || '操作成功',
			}).then((aa) => {
			    router.back()
			})
        })
    }
    const popInfo = ref({
        id:null
    });
    const operateType = ref(null)
    const operate = (type,id) => {
        if(type=='cancel'){
            dialogTitle.value='拒绝操作'
        }else if(type=='confirm'){
            dialogTitle.value='通过操作'
        }
        operateType.value = type;
        popInfo.value.id=id;
        dialogShow.value=true
    }

    // 添加相关
    const addDialogShow = ref(false)
    const addType = ref(null)
    const showAddPop = async()=>{
        !dictStore.hasDict('service_type') && await dictStore.getDictBatch(['service_type']);
        addDialogShow.value = true;
    }
    const addConfirm = ()=>{
        if(!addType.value)  return uni.showToast({
			title: '请选择类型',
			duration: 1500,
			icon:'none'
		});
		addDialogShow.value = false;
        router.push({name: 'complaintEdit',params: {serviceType:addType.value}})
    }
    const beforeClose = (action) =>{
        return action !== 'confirm' || (addType.value)
    }

    /**
     *  搜索相关 开始
     */
    const calendarShow = ref(false)
    const onCalendarConfirm = (values) => {
    	const start= values[0],end = values[values.length - 1];
        calendarShow.value = false;
        searchQuery.startDate = `${dayjs(start).format('YYYY-MM-DD')}`;
        searchQuery.endDate = `${dayjs(end).format('YYYY-MM-DD')}`;
        initEmpty()
    };
    
    // 是否显示清除图标
    const showReset = computed(()=>{
        return (searchQuery.startDate != '' ||
        searchQuery.endDate != '' ||
        // searchQuery.casePriority != '' ||
        searchQuery.serviceType != '' ||
		searchQuery.caseStageCode != '' ||
        searchQuery.createdStaffId != '' ||
        selectedUser.value != null)
    });
    //清空搜索条件
    const reset = () =>{
        searchQuery.startDate = ''
        searchQuery.endDate = ''
        searchQuery.serviceType= ''
		searchQuery.caseStageCode= ''
        searchQuery.createdStaffId= ''
        // searchQuery.casePriority=''
        selectedUser.value = null
        initEmpty()
    }
    const showPicker = ref(false)
    const showTypePicker = ref(false)
    const pickerType = ref(null)
    const userList =ref([])
    const userLoad = ref(true)
    const getUserList = ()=>{
        queryUserList().then((res) => {
            userList.value = res.data
            userList.value.unshift({name:'全部',id:''})
            userLoad.value = false
            if(prop.createdStaffId) selectedUser.value = userList.value.find(i=>i.id==prop.createdStaffId)
        }).catch((err) => {
        });
    }
    const showPickerFun = ()=>{
        (!userList.value || userList.value.length==0) && getUserList()
        showPicker.value = true
    }
    const selectedUser = ref(null)
	const onUserConfirm = ({value,indexs}) => {
		console.log('onUserConfirm',value,indexs)
		let select = value[0];
		if(select){
			selectedUser.value = select
			searchQuery.createdStaffId = select.id
			initEmpty()
	    }
		showPicker.value = false
	}
	
	const columns = ref([]);
	const showTypePickerFun = async(type,key)=>{
	    pickerType.value = {type,key}
	    columns.value[0] = await dictStore.getDict(key);
		// debugger
	    showTypePicker.value = true
	}
	const onTypeConfirm = ({value,indexs}) =>{
		console.log('onTypeConfirm',value,indexs)
		let select = value[0];
		if(select){
			searchQuery[pickerType.value.type] = select.dval
            searchQuery[`${pickerType.value.type}_text`] = select.dname
			initEmpty()
		}
		showTypePicker.value = false
	}
	onShow(() => {
		// uni.$once("refresh", (data) => {
		// 	setTimeout(function(){
		// 		if(data.refresh){
		// 			addType.value = null
		// 			initEmpty()
		// 			uni.$emit("refresh", {refresh: false}); 
		// 		}
		// 	},120)
		// })
	})
</script>
<style lang="scss" scoped>
    @import '@/styles/list.scss';
    
    .page{
		.search{
			margin-left: 10upx;
		}
        .addInfo{
			padding: 10px;
			position: relative;
			border-radius: var(--card-border-radius);
			line-height: 1.4;
			width: 100%;
			.u-icon{
				padding: 10px 15px;
			}
			.card{
				background: none;
				padding: var(--card-padding);
				position: relative;
				border-radius: var(--card-border-radius);
				line-height: 1.4;
				.card_head{
					display: flex;
					align-items: center;
					justify-content: center;
					// border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
					padding: 15px 0 10px 0;
					&_tit{
						display: flex;
						align-items: center;
						font-size: 18px;
						font-weight: 600;
						color: #333333;
						span{
							font-weight: 700;
							font-size: 16px;
							color: #000000;
							margin-right: 5px;
						}
					}
				}
			}
			
		}

	}
</style>