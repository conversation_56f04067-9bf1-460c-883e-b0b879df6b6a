
<template>
	<view>
		
	
	<view class="solution_list">
		<div class="card_head">
		    <div class="card_head_tit">{{title}}</div>
		</div>
		<template v-if="solutionList.length>0">
			<view class="old_list">
				
				<radio-group @change="radioChange">
				
				<view v-for="(solution,s) in solutionList" class="old_list_itemm" :key="s">
					<view class="item_tit">
						<span>{{solution.solBlockName}}</span>
						
						<label v-if="caseStageCode!='CLOSE' 
							&&  (
								(type!='pages/complaint/ApproveList' &&  showBtn=='1' && caseStageCode!='ECSP' ) || (type=='pages/complaint/ApproveList')
							) "  class="selected_btn">
							<radio style="transform:scale(0.7)" color="#849EB2" :value="s"></radio>
						</label >
					</view>
					<uni-table border stripe emptyText="暂无更多数据" >
						<!-- 表头行 -->
						<uni-tr  >
							<template v-for="(item,index) in detailFild"  :key="index"  >
								<uni-th v-if="item.showTable" :width="item.width" align="center">{{item.label}}</uni-th>
							</template>
							<uni-th width="80" operate align="center">操作</uni-th>
						</uni-tr>
						<!-- 表格数据行 -->
						<uni-tr v-for="(detail,i) in solution.solutionInfo" :key="i">
							<template v-for="(item,index) in detailFild" :key="index" >
								<uni-td v-if="item.showTable" align="center"  >
									<view v-if="item.isImg" >
										<!-- <image v-if="detail[item.field]" @click="showImagePreview([detail[item.field]],index)" width="50"  mode="widthFix" :src="imgBase+detail[item.field]" /> -->
									</view>
									<view v-else >{{detail[item.field]}}</view>
								</uni-td>
							</template>
							<uni-td  operate align="center">
								<view class="uni-group">
									<u-button class="uni-button" @click="handleShow(detail,i)" color="#849EB2" :plain="true" :hairline="true" size="mini" type="primary">查看</u-button>
								</view>
							</uni-td>
						</uni-tr>
					</uni-table>
				</view>
				
				</radio-group>
			</view>
		</template>
		
		<template v-if="caseStageCode!='CLOSE'">
		<view class="item_tit" style="line-height: 1.5; margin-bottom: 10px;">当前方案</view>
		<uni-table border stripe :emptyText="solutionList.length>0?'请选择历史方案':'暂无更多数据'" >
			<!-- 表头行 -->
			<uni-tr  >
				<template v-for="(item,index) in detailFild"  :key="index"  >
					<uni-th v-if="item.showTable" :width="item.width" align="center">{{item.label}}</uni-th>
				</template>
				<uni-th width="80" operate align="center">操作</uni-th>
			</uni-tr>
			<!-- 表格数据行 -->
			<uni-tr v-for="(detail,i) in detailList" :key="i">
				<template v-for="(item,index) in detailFild" :key="index" >
					<uni-td v-if="item.showTable" align="center"  >
						<view v-if="item.isImg" >
							<!-- <image v-if="detail[item.field]" @click="showImagePreview([detail[item.field]],index)" width="50"  mode="widthFix" :src="imgBase+detail[item.field]" /> -->
						</view>
						<view v-else > {{Array.isArray(detail[item.field])?detail[item.field].join(','):detail[item.field]}}</view>
					</uni-td>
				</template>
				<uni-td  operate align="center">
					<view v-if="!readOnly" class="uni-group">
						<u-button class="uni-button" @click="handleShow(detail,i,'eidt')" color="#849EB2" :plain="true" :hairline="true" size="mini" type="primary">修改</u-button>
						<!-- <u-button class="uni-button" @click="deleteRow(i)" :plain="true"  :hairline="true" size="mini" type="error">删除</u-button> -->
					</view>
					<view v-else class="uni-group">
						<u-button class="uni-button" @click="handleShow(detail,i)" color="#849EB2" :plain="true" :hairline="true" size="mini" type="primary">查看</u-button>
					</view>
				</uni-td>
			</uni-tr>
		</uni-table>
		<div @click="generate" v-if="!readOnly && detailList.length==0 && solutionList.length==0 && showBtn=='1'"  class="detail_add"><u-icon bold name="plus" label="生成解决方案" /> </div>
		</template>
		
		<u-modal v-model:show="showAddPop" @confirm="handconfirm"  @close="showAddPop = false" @cancel="showAddPop = false" title="方案明细" show-cancel-button confirmColor="#849EB2">
			<div class="addInfo">
				<u--form labelPosition="top" labelWidth="auto">
				<div class="card" style="height: auto; padding-bottom: 250px;">									
					
					<u-form-item label="问题分类" prop="problemType">
						<zxz-uni-data-select
							clearable  placeholder="请选择"  
							v-model="popInfo.problemType"
							multiple
							:localdata="dict['problem_type']">
						</zxz-uni-data-select>
					</u-form-item>
					<template v-if="caseStageCode=='ZLYJ'">
						<u-form-item label="判断" prop="judge"  required>
							<zxz-uni-data-select
								clearable  placeholder="请选择"  
								v-model="popInfo.judge" 
								:localdata="dict['judge']">
							</zxz-uni-data-select>
						</u-form-item>
						<u-form-item label="判定描述" prop="judgeDesc" >
							<u--textarea :count="!!popInfo.judgeDesc"   v-model="popInfo.judgeDesc"  class="int" maxlength="800"  placeholder="判定描述"  />
						</u-form-item>
					</template>
					<u-form-item label="处理方案" prop="solutionType" @click="()=>{if(popInfo.judge!='不予受理')showpicker= true}" required>
						<u--input
							v-model="popInfo.solutionType"
							disabled
							disabledColor="#ffffff"
							placeholder="请选择"
							border="none"
					></u--input>
						<u-picker :show="showpicker" :columns="columns" @confirm="pickerConfirm" @cancel="showpicker=false" keyName="dname"></u-picker>
						<template #right>
							<u-icon	name="arrow-right"></u-icon>
						</template>
						<!-- <zxz-uni-data-select
							clearable  placeholder="请选择"  
							v-model="popInfo.solutionType"
							:disabled = "popInfo.judge=='不予受理'"
							:localdata="dict['solution_type']">
						</zxz-uni-data-select> -->
					</u-form-item>
					
					
					<u-form-item v-if="popInfo.solutionType  && popInfo.solutionType.includes('退货')" label="退货（张数）" prop="returnNum" required>
						<u--input  v-model="popInfo.returnNum" type="number" class="int"   placeholder="退货（张数）"  />
					</u-form-item>
					<u-form-item label="换货（张数）" v-if="popInfo.solutionType  && popInfo.solutionType.includes('换货')" prop="exchangeNum" required>
						<u--input  v-model="popInfo.exchangeNum" type="number" class="int"   placeholder="换货（张数）"  />
					</u-form-item>
					<u-form-item label="返工（张数）" v-if="popInfo.solutionType  && popInfo.solutionType.includes('返工')" prop="reworkNum" required>
						<u--input  v-model="popInfo.reworkNum" type="number" class="int"   placeholder="返工（张数）"  />
					</u-form-item>
					<u-form-item label="折让（金额）" v-if="popInfo.solutionType  && popInfo.solutionType.includes('折让')" prop="discountsAmount" required>
						<u--input  v-model="popInfo.discountsAmount" type="number" class="int"   placeholder="折让（金额）"  />
					</u-form-item>
					<u-form-item label="索赔（金额）" v-if="popInfo.solutionType  && popInfo.solutionType.includes('索赔')" prop="claimantAmount " required >
						<u--input  v-model="popInfo.claimantAmount" type="number" class="int"   placeholder="索赔（金额）"  />
					</u-form-item>
					<u-form-item label="不予受理原因" v-if="popInfo.solutionType  && popInfo.solutionType.includes('不予受理')" prop="undoReason " required >
						<u--textarea :count="!!popInfo.undoReason"   v-model="popInfo.undoReason"  class="int" maxlength="800"  placeholder="请填写"  />
					</u-form-item>		
				</div>
				</u--form>
			</div>
		</u-modal>
		<u-datetime-picker
			:show="calendarShow"
			v-model="currentTime"
			confirmColor="#849EB2"
			closeOnClickOverlay
			@confirm="onCalendarConfirm"
			@cancel="calendarShow = false"
			@close="calendarShow = false"
			mode="date"
		></u-datetime-picker>
	</view>
	
	<u-popup :show="showInfo" :round="10" closeable mode="bottom" @close="showInfo = false">
		<div class="pop_info">
			<div class="pop_info_tit">方案详情</div>
			<div class="card_info">
				<div class="card">
					<div class="card_main">
						<template v-for="(fildInfo,index) in detailFild" :key="index">
						<div  v-if="popInfo[fildInfo.field]"  class="item">
							<div class="left">{{fildInfo.label}}</div>							
							<view v-if="fildInfo.isImg && Array.isArray(popInfo[fildInfo.field])" class="rigth">
							
								<template  v-for="(item,index) in popInfo[fildInfo.field]" :key="index" >
									<image   @click="showImagePreview(popInfo[fildInfo.field],index)" style="width: 60px;" mode="widthFix"  :src="item" />
								</template>
								
							</view>
							<view v-else class="rigth">  {{Array.isArray(popInfo[fildInfo.field])?popInfo[fildInfo.field].join(','):popInfo[fildInfo.field]}} </view>
						</div>
						</template>
					</div>
				</div>
			</div>
		</div>
	</u-popup>
	</view>
</template>
<script>
	export default {
		options: {
			// #ifdef MP-WEIXIN
			// 微信小程序中 options 选项
			multipleSlots: true, //  在组件定义时的选项中启动多slot支持，默认启用
			styleIsolation: "shared", //  启动样式隔离。当使用页面自定义组件，希望父组件影响子组件样式时可能需要配置。具体配置选项参见：微信小程序自定义组件的样式
			addGlobalClass: true, //  表示页面样式将影响到自定义组件，但自定义组件中指定的样式不会影响页面。这个选项等价于设置 styleIsolation: apply-shared
			virtualHost: true, //  将自定义节点设置成虚拟的，更加接近Vue组件的表现。我们不希望自定义组件的这个节点本身可以设置样式、响应 flex 布局等，而是希望自定义组件内部的第一层节点能够响应 flex 布局或者样式由自定义组件本身完全决定
			// #endif
		},
	}
</script>
<script setup>
	import {deleteCaseDetail}  from  '@/api/complaint'
	import {queryUserList,upload,asyncUpload}  from  '@/api/common'
	import dayjs from "dayjs";
	const lodash = inject("lodash");
	const router = useRouter();
	const prop =  defineProps({
		title: {
			type:String,
			default: '服务单处理方案',
		},
		detailFild:{
			type: [Array],
			default:[
				{field:'problemType',label:'问题分类',showTable: false,width:'100'},
				{field:'judge',label:'判断',showTable: false,width:'100'},
				{field:'judgeDesc',label:'判定描述',showTable: false,width:'100'},
				{field:'productName',label:'产品名称',showTable: true,width:'100'},
				{field:'orderNo',label:'订单号',showTable: true,width:'100'},
				{field:'solutionType',label:'处理方案',showTable: true,width:'100'},
				{field:'returnNum',label:'退货数量',showTable: false,width:'100'},
				{field:'exchangeNum',label:'换货数量 ',showTable: false,width:'100'},
				{field:'reworkNum',label:'返工数量',showTable: false,width:'100'},
				{field:'discountsAmount',label:'折让金额',showTable: false,width:'100'},
				{field:'claimantAmount',label:'索赔金额',showTable: false,width:'100'},
				{field:'undoReason',label:'解决原因',showTable: false,width:'100'},
				// {field:'orderDetailId',label:'订单明细编号',showTable: false},
				{field:'solutionRole',label:'方案角色',showTable: false},
				{field:'createdTime',label:'方案创建时间',showTable: false},
				{field:'createdStaffName',label:'方案创建人',showTable: false},
			]
		},
		// 订单产品列表
		orderDetailList:{
			type: [Array],
			default:[]
		},
		//方案列表
		solutionList:{
			type: [Array],
			default:[]
		},
		showAddBtn: {
		    type: Boolean,
		    default:true,
		},
		operate: {
		    type: Boolean,
		    default:true,
		},
		readOnly: {
		    type: Boolean,
		    default:false,
		},
		caseStageCode:{
			type:String,
			default: null
		},
		caseId: {
			type:String,
			default: null
		},
		showBtn:{
			type:String,
			default: null
		},
		type:{
			type:String,
			default: 'pages/complaint/Index'
		},
	})
	const emit = defineEmits(['change','cancel'])
	const dictStore = useDictStore()
	const detailList = ref([]);
	// watch(() => prop.orderDetailList,orderDetailList => {
	// 		// if(!detailList.value || detailList.value.length==0) 
	// 		detailList.value = orderDetailList.map(i=>{
	// 			return {orderDetailId:i.id,caseId:prop.caseId}
	// 		});
	// 	},
	// 	{ immediate: true }
	// )
	const showAddPop = ref(false)
	const showInfo = ref(false)
	const popInfo = ref({});
	//需要处理的上传文件
	const fileFild = new Map([
		['onSiteImg',{key:'onSiteImg',message:'请上传现场照片',maxLenth: 1,required: true}],
		['breakImg',{key:'breakImg',message:'请上传损坏的具体照片',maxLenth: 1,required: true}],
		// ['feedback_img',{key:'feedback_img',message:'请上传反馈意见照片',maxLenth: 3,}],
		['noImg',{key:'noImg',message:'请上传带有编号的照片',maxLenth: 1,required: true}],
		['otherImg',{key:'otherImg',message:'请上传带有编号的照片',maxLenth: 3,}],
		// ['factory_option_img',{key:'factory_option_img',message:'请上传工厂照片',maxLenth: 3,}],
	])
	const afterRead = async(event,type) =>{
		let files = [].concat(event.file)
		if(!popInfo.value[type]) popInfo.value[type] = [];
		let fileListLen =  popInfo.value[type]?popInfo.value[type].length:0
		files.map((item) => {
			popInfo.value[type].push({
				...item,
				status: 'uploading',
				message: '上传中'
			})
		})
		for (let i = 0; i < files.length; i++) {
			try {
				const {data} = await asyncUpload(files[i].url)
				let item = popInfo.value[type][fileListLen];
				popInfo.value[type].splice(fileListLen, 1, {
				  ...item,
				  status: 'success',
				  message: '',
				  url: data.url,
				});
				fileListLen++;
			} catch (e) {
				console.log(e)
				uni.showToast({
					title: "图片上传失败",
					icon: "none"
				})
			}
		}
	}

	const beforeDelete = ({file,index},type) =>{
		uni.showModal({
			title: '提示',
			content: '是否删除该附件？',
			success: function (res) {
				if (res.confirm) {
					popInfo.value[type].splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
		
	watch([()=>popInfo.value.buyQty,()=>popInfo.value.unitPrice],([buyQty,unitPrice]) => {
		if(buyQty && unitPrice){
			popInfo.value.totalPrice = lodash.round(lodash.add(parseFloat(buyQty) * parseFloat(unitPrice)),2);
		}
	});
	
	watch(() => popInfo.value.judge,judge => {
			if(showAddPop.value){
				if(judge=='不予受理'){
					popInfo.value.solutionType = '不予受理';
				}else{
					popInfo.value.solutionType = null;
				}
			}
		},
	)
	watch(() => popInfo.value.solutionType,solutionType => {
			if(showAddPop.value){
				popInfo.value.exchangeNum = null;
				popInfo.value.reworkNum = null;
				popInfo.value.discountsAmount = null;
				popInfo.value.claimantAmount = null;
				popInfo.value.undoReason = null;
			}
		},
	)
	
	const selectorChange = (data,info) =>{
		console.log('selector',data,info)
	}
	
	
	const deleteRow = (index) => {
		uni.showModal({
		    title: '提示',
		    content: '是否删除该记录？',
			success: function (unires) {
				if (unires.confirm) {
					if(detailList.value[index] && detailList.value[index].id){
						deleteCaseDetail(detailList.value[index].id).then((res) => {
							detailList.value.splice(index, 1);
							emit('change',detailList.value);
						}).catch((err) => {
						});
					}else{
						detailList.value.splice(index, 1);
						emit('change',detailList.value);
					}
				}
			}
		})
	}
	let showindex = null;
	
	const handconfirm = () =>{
		// debugger
		if(!popInfo.value.solutionType){
			return uni.showToast({
				title:'请选择处理方案',
				duration: 1500,
				icon:'none'
			}); 
		}
		debugger
		if(popInfo.value.solutionType=='不予受理' && !popInfo.value.undoReason){
			return uni.showToast({
				title:'请填写不予处理原因',
				duration: 1500,
				icon:'none'
			}); 
		}else if(popInfo.value.solutionType!='不予受理' && !popInfo.value.returnNum && !popInfo.value.exchangeNum && !popInfo.value.reworkNum && !popInfo.value.discountsAmount && !popInfo.value.claimantAmount){
			return uni.showToast({
				title:'请填写相关方案详情',
				duration: 1500,
				icon:'none'
			}); 
		}
		if(prop.caseStageCode=='ZLYJ' && !popInfo.value.judge){
			return uni.showToast({
				title:'请选择判断',
				duration: 1500,
				icon:'none'
			}); 
		}
		for (let [key, fildInfo] of fileFild) {
		    if(Array.isArray(popInfo.value[key])) {
		    	popInfo.value[key].forEach((item,index) => {
		    	    popInfo.value[`${key}${index+1}`] = item.url
		    	});
		    }
			delete popInfo.value[key];
		}
		if(popInfo.value.problemType && Array.isArray(popInfo.value.problemType)) popInfo.value.problemType = popInfo.value.problemType.join(';')
		if(showindex || showindex>=0){
			detailList.value[showindex] = popInfo.value;
		}else{
			detailList.value.push(popInfo.value)
		}
		showAddPop.value = false
		popInfo.value = {};
		emit('change',detailList.value);
	}
	const handleShow = (data,index,type) =>{
		fileFild.forEach (function(value, key) {
			if(!Array.isArray(data[key])) {
				data[key] =  data[key]?data[key].split(';').map(i=>{
					// return{url:import.meta.env.VUE_APP_BASE_IMG + i}
					return	type=='eidt' ?  {url:import.meta.env.VUE_APP_BASE_IMG + i} : import.meta.env.VUE_APP_BASE_IMG + i
				}):[] ;
			}
		})
		debugger
		if(data.problemType  && !Array.isArray(data.problemType))data.problemType = data.problemType.split(';')
		popInfo.value = data;
		showindex = index
		if(type=='eidt'){
			showAddPop.value = true
		}else{
			showInfo.value = true;
		}
	}
	
	const calendarShow = ref(false)
	const calendarType = ref(null)
	const currentTime = ref(Date.now());
	const onCalendarShow  = (type,check = false) =>{
	    calendarType.value = type
	    calendarShow.value = true;
	}
	const onCalendarConfirm = ({mode,value}) => {
	    popInfo.value[calendarType.value] = `${dayjs(value).format('YYYY-MM-DD')}`;
	    // info.value[calendarType.value] = `${currentDate.value.join('-')} ${currentTime.value.join(':')}`
	    calendarShow.value = false
	};
	const generate = ()=>{
		detailList.value = prop.orderDetailList.map(i=>{
			return {orderDetailId:i.id,caseId:prop.caseId,productName:i.productName,orderNo:i.orderNo}
		});
		emit('change',detailList.value);
	}
	
	const radioChange = ({detail})=>{
		let idx = detail.value;
		let selected = prop.solutionList[idx];
		detailList.value = selected.solutionInfo.map(i=>{
			return {
				caseId:prop.caseId,
				orderDetailId:i.orderDetailId,
				returnNum:i.returnNum,
				exchangeNum:i.exchangeNum,
				reworkNum:i.reworkNum,
				discountsAmount:i.discountsAmount,
				claimantAmount:i.claimantAmount,
				// solutionRole:i.solutionRole,
				solutionType:i.solutionType,
				undoReason:i.undoReason,
				productName:i.productName,
				orderNo:i.orderNo,
				// judge:i.judge ,
				// judgeDesc :i.judgeDesc ,
				// problemType :i.problemType ,
			}
		});
		emit('change',detailList.value);
	}
	
	const reset=()=>{
		recordData.value = {}
		current.value = {}
		showLeft.value = false;
	}
	const showpicker = ref(false);
	const columns = ref([]);
	const pickerConfirm = ({value})=>{
		popInfo.value.solutionType = value[0].dval
		showpicker.value=false
	}
	onMounted(async()=>{
		await dictStore.getDictBatch(['service_type','feedback_person_type','feedback_type','feedback_class','sheet_status','case_class','problem_type','judge']);
		columns.value[0] = await dictStore.getDict('solution_type')
	})
	defineExpose({
		reset
	})
</script>

<style lang="scss" scoped>
	.solution_list{
		padding-bottom: 20px;
		.item_tit{
			display: flex;
			align-items: center;
			line-height: 1.5; margin-bottom: 5px;
			font-size: 14px;
			.selected_btn{
				margin-left: auto;
			}
		}
		.old_list_itemm{
			margin-bottom: 20px;
		}
	}
	.uni-table-td{
		font-size: 12px;
		padding: 4px 5px;
	}
	.detail_add{
	    color: #849EB2;
	    text-align: center;
	    margin-top: 10px;
		width: 100%; padding: 10px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.uni-group{
		.u-button{
			font-size: 12px;
			margin-top:10px ;
			padding: 12px 0;
			&:first-child{
				margin-top:0px ;
			}
		}
	}
	.card_head{
	    display: flex;
	    align-items: center;
	    // justify-content: center;
	    // border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
	    padding: 15px 0 10px 0;
	    &_tit{
	        display: flex;
	        align-items: center;
	        font-size: 18px;
	        font-weight: 600;
	        color: #333333;
	        span{
	            font-weight: 700;
	            font-size: 16px;
	            color: #000000;
	            margin-right: 5px;
	        }
	    }
	}
	.addInfo{
	    padding: 10px 0;
	    position: relative;
	    border-radius: var(--card-border-radius);
	    line-height: 1.4;
		position: relative;
		width: 100%;
		height: calc(100vh - 240px);
		overflow-y: scroll;
		overscroll-behavior: contain;
		// height: 60vh;
		transform:1;
	    .card{
	        padding: 0 15px;
	        background: none;
	        // padding: var(--card-padding);
	        position: relative;
	        border-radius: var(--card-border-radius);
	        line-height: 1.4;
	        // max-height: calc(100vh - 100px);
	        // overflow-y: scroll;
	        
	        .info{
	            font-size: 14px;
	            // margin: 5px 0;
	            display: flex;
	            flex-direction:row;
	            color: #333333;
	            width: 100%;
	            align-items: center;
	            line-height: 1.5;
	            .left{
	                // width: 60px;
	                color: var(--van-field-label-color);
	                &.required{
	                    &:before {
	                        margin-right: 2px;
	                        color: var(--van-field-required-mark-color);
	                        content: "*";
	                    }
	                }
	            }
	            .rigth{
	                margin-left: auto;
	                flex: 1;
	            }
	        }
	    }
		.u-form-item,.u-form-item__body__left__content__label,.uni-input-input,.uni-select,.u-input__content__field-wrapper__field{
			font-size: 12px !important;
		}
		.u-form-item__body{
			padding: 5px 0;
		}
		.u-form-item__body__right{
			padding: 4px;
		}
	}
	.pop_info{
		.card_info{
		    max-width: 100%;
		    padding: 10px;
			max-height: calc(100vh - 180px);
			overflow-y: scroll;
			.item{
				font-size: 14px;
				margin: 5px 0;
				display: flex;
				flex-direction:row;
				color: #333333;
				width: 100%;
				align-items: center;
				line-height: 1.5;
				.left{
					// width: 140px;
					color: #999999;
					flex: 1;
				}
				.rigth{
					margin-left: auto;
					flex: 1.5;
				}
			}
		}
	}
</style>