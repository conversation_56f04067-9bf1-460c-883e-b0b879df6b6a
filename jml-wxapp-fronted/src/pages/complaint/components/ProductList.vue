
<template>
	<view>
	<view class="product_list">
		<div class="card_head">
		    <div class="card_head_tit">{{title}}</div>
		</div>
		<uni-table border stripe emptyText="暂无更多数据"  v-if="hasPermission('SalesCasePOList','R')">
			<!-- 表头行 -->
			<uni-tr>
				<template v-for="(item,index) in detailFildBySales"  :key="index"  >
					<uni-th v-if="item.showTable" :width="item.width" align="center">{{item.label}}</uni-th>
				</template>
				<uni-th width="80" operate align="center">操作</uni-th>
			</uni-tr>
			<!-- 表格数据行 -->
			<uni-tr v-for="(detail,i) in detailList" :key="i">
				<template v-for="(item,index) in detailFildBySales" :key="index" >
					<uni-td v-if="item.showTable" align="center"  >
						<view v-if="item.isImg" >
							<!-- <image v-if="detail[item.field]" @click="showImagePreview([detail[item.field]],index)" width="50"  mode="widthFix" :src="imgBase+detail[item.field]" /> -->
						</view>
						<view v-else >{{detail[item.field]}}</view>
					</uni-td>
				</template>
				<uni-td  operate align="center">
					<view v-if="!readOnly" class="uni-group">
						<u-button class="uni-button" @click="handleShow(detail,i)" color="#849EB2" :plain="true" :hairline="true" size="mini" type="primary">修改</u-button>
						<u-button class="uni-button" @click="deleteRow(i)" :plain="true"  :hairline="true" size="mini" type="error">删除</u-button>
					</view>
					<view v-else class="uni-group">
						<u-button v-if="caseStageCode=='FKYJ'" class="uni-button" @click="handleShow(detail,i)" color="#849EB2" :plain="true" :hairline="true" size="mini" type="primary">修改</u-button>
						<u-button v-else class="uni-button" @click="handleShow(detail,i)" color="#849EB2" :plain="true" :hairline="true" size="mini" type="primary">查看</u-button>
					</view>
				</uni-td>
			</uni-tr>
		</uni-table>
		
		<uni-table border stripe emptyText="暂无更多数据"  v-else>
			<!-- 表头行 -->
			<uni-tr>
				<template v-for="(item,index) in detailFild"  :key="index"  >
					<uni-th v-if="item.showTable" :width="item.width" align="center">{{item.label}}</uni-th>
				</template>
				<uni-th width="80" operate align="center">操作</uni-th>
			</uni-tr>
			<!-- 表格数据行 -->
			<uni-tr v-for="(detail,i) in detailList" :key="i">
				<template v-for="(item,index) in detailFild" :key="index" >
					<uni-td v-if="item.showTable" align="center"  >
						<view v-if="item.isImg" >
							<!-- <image v-if="detail[item.field]" @click="showImagePreview([detail[item.field]],index)" width="50"  mode="widthFix" :src="imgBase+detail[item.field]" /> -->
						</view>
						<view v-else >{{detail[item.field]}}</view>
					</uni-td>
				</template>
				<uni-td  operate align="center">
					<view v-if="!readOnly" class="uni-group">
						<u-button class="uni-button" @click="handleShow(detail,i)" color="#849EB2" :plain="true" :hairline="true" size="mini" type="primary">修改</u-button>
						<u-button class="uni-button" @click="deleteRow(i)" :plain="true"  :hairline="true" size="mini" type="error">删除</u-button>
					</view>
					<view v-else class="uni-group">
						<u-button v-if="caseStageCode=='FKYJ'" class="uni-button" @click="handleShow(detail,i)" color="#849EB2" :plain="true" :hairline="true" size="mini" type="primary">修改</u-button>
						<u-button v-else class="uni-button" @click="handleShow(detail,i)" color="#849EB2" :plain="true" :hairline="true" size="mini" type="primary">查看</u-button>
					</view>
				</uni-td>
			</uni-tr>
		</uni-table>
		
		<div @click="handleShow({},detailList.length)" v-if="!readOnly"  class="detail_add"><u-icon bold name="plus" label="添加一栏" /> </div>
		<u-modal v-model:show="showAddPop" @confirm="handconfirm" overlayStyle="{'touch-action':'none'}" @close="showAddPop = false" @cancel="showAddPop = false" title="产品明细" show-cancel-button confirmColor="#849EB2">
			<div class="addInfo">
				<u--form labelPosition="top" labelWidth="auto">
				<div class="card" style="height: auto;">
					<div class="card_head">
					    <div class="card_head_tit">销售填写信息</div>
					</div>
					<u-form-item label="产品型号、规格" prop="productName" required >
						<u--input  v-model="popInfo.productName"   placeholder="产品型号、规格"  />
					</u-form-item>
					<u-form-item label="销售描述问题" prop="salesDesc" required >
						<u--textarea :count="!!popInfo.salesDesc"   v-model="popInfo.salesDesc"  class="int" maxlength="800"  placeholder="请在这里输入您的描述"  />
					</u-form-item>
					<u-form-item label="客户诉求" prop="demands" required >
						<u--textarea :count="!!popInfo.demands"   v-model="popInfo.demands"  class="int" maxlength="800"  placeholder="客户诉求"  />
					</u-form-item>
					<u-form-item label="侧边喷码" prop="sideCode" required >
						<u--input  v-model="popInfo.sideCode"   placeholder="侧边喷码"  />
					</u-form-item>
					<u-form-item label="现场照片" prop="onSiteImg" required >
						<u-upload
							:max-count="1"
							multiple
							@after-read="afterRead($event,'onSiteImg')"
							:deletable="true"
							:sizeType="['compressed']"
							:before-delete="(file,detail)=>beforeDelete(file,detail)"
							@delete="beforeDelete($event,'onSiteImg')"
							:fileList="popInfo.onSiteImg"
						>
						</u-upload>
					</u-form-item>
					<u-form-item label="侧边喷码（批号）" prop="noImg" required >
						<u-upload
							:max-count="1"
							multiple
							@after-read="afterRead($event,'noImg')"
							:deletable="true"
							:sizeType="['compressed']"
							:before-delete="(file,detail)=>beforeDelete(file,detail)"
							@delete="beforeDelete($event,'noImg')"
							:fileList="popInfo.noImg"
						>
						</u-upload>
					</u-form-item>
					<u-form-item label="损坏的具体照片" prop="breakImg" required >
						<u-upload
							:max-count="1"
							multiple
							@after-read="afterRead($event,'breakImg')"
							:deletable="true"
							:sizeType="['compressed']"
							:before-delete="(file,detail)=>beforeDelete(file,detail)"
							@delete="beforeDelete($event,'breakImg')"
							:fileList="popInfo.breakImg"
						>
						</u-upload>
					</u-form-item>
					<u-form-item label="其他照片" prop="otherImg"  >
						<u-upload
							:max-count="3"
							multiple
							@after-read="afterRead($event,'otherImg')"
							:deletable="true"
							:sizeType="['compressed']"
							:before-delete="(file,detail)=>beforeDelete(file,detail)"
							@delete="beforeDelete($event,'otherImg')"
							:fileList="popInfo.otherImg"
						>
						</u-upload>
					</u-form-item>
					
					<template v-if="!hasPermission('SalesCasePOList','R') && caseStageCode=='FKYJ'">
					<div class="card_head">
					    <div class="card_head_tit">助理填写部分</div>
					</div>
					
					<u-form-item label="问题分类" prop="caseClass" required>
						<zxz-uni-data-select clearable multiple filterable placeholder="请选择"  v-model="popInfo.caseClass"   :localdata="dict['case_class']"></zxz-uni-data-select>
					</u-form-item>
					<u-form-item label="存货编码" prop="inventoryCode" required >
						<u--input  v-model="popInfo.inventoryCode"   placeholder="存货编码"  />
					</u-form-item> 
					<u-form-item label="订单号" prop="orderNo" required >
						<u--input  v-model="popInfo.orderNo"   placeholder="订单号"  />
					</u-form-item> 					
					<u-form-item label="购买日期"  prop="buyDate">
						<template>
							<view @click="onCalendarShow('buyDate')">
							<u--input
								v-model="popInfo.buyDate"
								class="int"
								placeholder="购买日期"
								readonly
								disabled
								disabledColor="#ffffff"
								border="none"
								style="width: 100%;pointer-events: none;"
							/>
							</view>
						</template>
						<template #right><u-icon name="calendar" size="22" @click="onCalendarShow('buyDate')"></u-icon></template>
					</u-form-item>
					
					<!-- <u-form-item label="发货仓库" prop="deliveryWarehouse">
						<u--input  v-model="popInfo.deliveryWarehouse"   placeholder="发货仓库"  />
					</u-form-item> -->
					
					<!-- <u-form-item label="产品规格" prop="spec"  >
						<u--input  v-model="popInfo.spec"   placeholder="产品规格"  />
					</u-form-item> -->
					<u-form-item label="单价" prop="unitPrice"  >
						<u--input  v-model="popInfo.unitPrice" type="digit"   placeholder="单价"  />
					</u-form-item>
					<u-form-item label="购买数量" prop="buyQty"  >
						<u--input  v-model="popInfo.buyQty" type="number"   placeholder="购买数量"  />
					</u-form-item>
					<u-form-item label="发现不良品数" prop="badQty" required >
						<u--input  v-model="popInfo.badQty" type="number"  placeholder="发现不良品数"  />
					</u-form-item>
					<u-form-item label="不良品描述" prop="badRemark" required >
						<u--textarea :count="!!popInfo.badRemark"   v-model="popInfo.badRemark"  class="int" maxlength="800"  placeholder="不良品描述"  />
					</u-form-item>
					
					</template>
				</div>
				</u--form>
			</div>
		</u-modal>
		<u-datetime-picker
			:show="calendarShow"
			v-model="currentTime"
			confirmColor="#849EB2"
			closeOnClickOverlay
			@confirm="onCalendarConfirm"
			@cancel="calendarShow = false"
			@close="calendarShow = false"
			mode="date"
		></u-datetime-picker>
	</view>
	
	<u-popup :show="showInfo" :round="10" closeable mode="bottom" @close="showInfo = false">
		<div class="product_info" >
			<div class="pop_info_tit">产品详情</div>
			<div class="card_info">
				<div class="card">
					<div class="card_main">
						<template v-if="hasPermission('SalesCasePOList','R')">
							<div  v-for="(fildInfo,index) in detailFildBySales" :key="index" class="item">
								<div class="left">{{fildInfo.label}}</div>							
								<view v-if="fildInfo.isImg && Array.isArray(popInfo[fildInfo.field])" class="rigth">
									<template  v-for="(item,index) in popInfo[fildInfo.field]" :key="index" >
										<image   @click="showImagePreview(popInfo[fildInfo.field],index)" style="width: 60px;" mode="widthFix"  :src="item" />
									</template>
								</view>
								<view v-else class="rigth">{{Array.isArray(popInfo[fildInfo.field])?popInfo[fildInfo.field].join(','):popInfo[fildInfo.field]}}</view>
							</div>
						</template>
						<template v-else>
							<div  v-for="(fildInfo,index) in detailFild" :key="index" class="item">
								<div class="left">{{fildInfo.label}}</div>							
								<view v-if="fildInfo.isImg && Array.isArray(popInfo[fildInfo.field])" class="rigth">
									<template  v-for="(item,index) in popInfo[fildInfo.field]" :key="index" >
										<image   @click="showImagePreview(popInfo[fildInfo.field],index)" style="width: 60px;" mode="widthFix"  :src="item" />
									</template>
								</view>
								<view v-else class="rigth">{{Array.isArray(popInfo[fildInfo.field])?popInfo[fildInfo.field].join(','):popInfo[fildInfo.field]}}</view>
							</div>
						</template>

						<detail-history v-if="detailHistory && detailHistory.length>0 "  :list="detailHistory"></detail-history>
					</div>
				</div>
			</div>
		</div>
	</u-popup>
	</view>
</template>
<script>
	export default {
		options: {
			// #ifdef MP-WEIXIN
			// 微信小程序中 options 选项
			multipleSlots: true, //  在组件定义时的选项中启动多slot支持，默认启用
			styleIsolation: "shared", //  启动样式隔离。当使用页面自定义组件，希望父组件影响子组件样式时可能需要配置。具体配置选项参见：微信小程序自定义组件的样式
			addGlobalClass: true, //  表示页面样式将影响到自定义组件，但自定义组件中指定的样式不会影响页面。这个选项等价于设置 styleIsolation: apply-shared
			virtualHost: true, //  将自定义节点设置成虚拟的，更加接近Vue组件的表现。我们不希望自定义组件的这个节点本身可以设置样式、响应 flex 布局等，而是希望自定义组件内部的第一层节点能够响应 flex 布局或者样式由自定义组件本身完全决定
			// #endif
		},
	}
</script>
<script setup>
	import DetailHistory from './DetailHistory.vue';
	import {deleteCaseDetail,updateOrderDetail,queryOrderDetailHistory}  from  '@/api/complaint'
	import {queryUserList,upload,asyncUpload}  from  '@/api/common'
	import dayjs from "dayjs";
	const lodash = inject("lodash");
	const router = useRouter();
	const prop =  defineProps({
		title: {
			type:String,
			default: '产品明细'
		},
		detailFild:{
			type: [Array],
			default:[
				{field:'productName',label:'产品型号、规格',showTable: true,width:'100'},
				{field:'inventoryCode',label:'存货编码',showTable: true,isImg:false,width:'100'},
				{field:'salesDesc',label:'销售描述问题',showTable: false},
				{field:'caseClass',label:'问题分类',showTable: false,width:'100'},
				{field:'orderNo',label:'订单号',showTable: true,width:'100'},
				{field:'sideCode',label:'侧边喷码',showTable: false,width:'100'},
				{field:'buyQty',label:'购买数量',showTable: true,width:'100'},
				{field:'unitPrice',label:'单价',showTable: false,width:'100'},
				{field:'totalPrice',label:'总价',showTable: true,width:'100'},
				{field:'buyDate',label:'购买日期',showTable: true,width:'100'},
				// {field:'deliveryWarehouse',label:'发货仓库',showTable: false,width:'100'},
				{field:'badQty',label:'发现不良品数',showTable: true,width:'100'},
				{field:'badRemark',label:'不良品描述',showTable: false,width:'100'},
				{field:'demands',label:'客户诉求',showTable: false,width:'100'},
				{field:'onSiteImg',label:'现场照片',showTable: false,isImg:true,width:'100'},
				{field:'noImg',label:'侧边喷码（批号）',showTable: false,isImg:true,width:'100'},
				{field:'breakImg',label:'损坏的具体照片',showTable: false,isImg:true,width:'100'},
				{field:'otherImg',label:'其他照片',showTable: false,isImg:true,width:'100'},
				{field:'createdStaffName',label:'创建人',showTable: true,isImg:false,width:'100'},
				{field:'createdTime',label:'创建时间',showTable: false,isImg:false,width:'100'},
				{field:'updateStaffName',label:'修改人',showTable: false,isImg:false,width:'100'},
				{field:'updateTime',label:'修改时间',showTable: false,isImg:false,width:'100'},
			]
		},
		dataList:{
			type: [Array],
			default:[]
		},
		showAddBtn: {
		    type: Boolean,
		    default:true,
		},
		operate: {
		    type: Boolean,
		    default:true,
		},
		readOnly: {
		    type: Boolean,
		    default:false,
		},
		caseStageCode:{
			type:String,
			default: null
		},
		caseId: {
			type:String,
			default: null
		},
	})
	const emit = defineEmits(['change','cancel'])
	const dictStore = useDictStore()
	const detailList = ref([]);
	watch(() => prop.dataList,dataList => {
			// if(!detailList.value || detailList.value.length==0) 
			detailList.value = dataList;
		},
		{ immediate: true }
	)
	const showAddPop = ref(false)
	const showInfo = ref(false)
	
	const popInfo = ref({});
	//需要处理的上传文件
	const fileFild = new Map([
		['onSiteImg',{key:'onSiteImg',message:'请上传现场照片',maxLenth: 1,required: true}],
		['breakImg',{key:'breakImg',message:'请上传损坏的具体照片',maxLenth: 1,required: true}],
		// ['feedback_img',{key:'feedback_img',message:'请上传反馈意见照片',maxLenth: 3,}],
		['noImg',{key:'noImg',message:'请上传侧边喷码（批号）',maxLenth: 1,required: true}],
		['otherImg',{key:'otherImg',message:'请上传其他照片',maxLenth: 3,}],
		// ['factory_option_img',{key:'factory_option_img',message:'请上传工厂照片',maxLenth: 3,}],
	])

	//销售助理	
	const detailFildBySales = ref(
		[
			{field:'salesDesc',label:'销售描述问题',showTable: true},
			{field:'inventoryCode',label:'存货编码',showTable: true,isImg:false,width:'100'},
			{field:'demands',label:'客户诉求',showTable: false,width:'100'},
			{field:'sideCode',label:'侧边喷码',showTable: false,width:'100'},
			{field:'onSiteImg',label:'现场照片',showTable: false,isImg:true,width:'100'},
			{field:'noImg',label:'侧边喷码（批号）',showTable: false,isImg:true,width:'100'},
			{field:'breakImg',label:'损坏的具体照片',showTable: false,isImg:true,width:'100'},
			{field:'otherImg',label:'其他照片',showTable: false,isImg:true,width:'100'},
			{field:'createdStaffName',label:'创建人',showTable: false,isImg:false,width:'100'},
			{field:'createdTime',label:'创建时间',showTable: false,isImg:false,width:'100'},
			{field:'updateStaffName',label:'修改人',showTable: false,isImg:false,width:'100'},
			{field:'updateTime',label:'修改时间',showTable: false,isImg:false,width:'100'},
		]
	)
	
	const afterRead = async(event,type) =>{
		let files = [].concat(event.file)
		if(!popInfo.value[type]) popInfo.value[type] = [];
		let fileListLen =  popInfo.value[type]?popInfo.value[type].length:0
		files.map((item) => {
			popInfo.value[type].push({
				...item,
				status: 'uploading',
				message: '上传中'
			})
		})
		for (let i = 0; i < files.length; i++) {
			try {
				const {data} = await asyncUpload(files[i].url)
				let item = popInfo.value[type][fileListLen];
				popInfo.value[type].splice(fileListLen, 1, {
				  ...item,
				  status: 'success',
				  message: '',
				  url: data.url,
				});
				fileListLen++;
			} catch (e) {
				console.log(e)
				uni.showToast({
					title: "图片上传失败",
					icon: "none"
				})
			}
		}
	}

	const beforeDelete = ({file,index},type) =>{
		uni.showModal({
			title: '提示',
			content: '是否删除该附件？',
			success: function (res) {
				if (res.confirm) {
					popInfo.value[type].splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
		
	watch([()=>popInfo.value.buyQty,()=>popInfo.value.unitPrice],([buyQty,unitPrice]) => {
		if(buyQty && unitPrice){
			popInfo.value.totalPrice = lodash.round(lodash.add(parseFloat(buyQty) * parseFloat(unitPrice)),2);
		}
	});
	const selectorChange = (data,info) =>{
		console.log('selector',data,info)
	}
	
	
	const deleteRow = (index) => {
		uni.showModal({
		    title: '提示',
		    content: '是否删除该记录？',
			success: function (unires) {
				if (unires.confirm) {
					if(detailList.value[index] && detailList.value[index].id){
						deleteCaseDetail(detailList.value[index].id).then((res) => {
							detailList.value.splice(index, 1);
							emit('change',detailList.value);
						}).catch((err) => {
						});
					}else{
						detailList.value.splice(index, 1);
						emit('change',detailList.value);
					}
				}
			}
		})
	}
	let showindex = null;
	let requiredFild = [
		{key:'productName',message:'请填写产品型号、规格',required: true,},
		{key:'salesDesc',message:'请填写销售描述问题',required: true,},
		{key:'demands',message:'请填写客户诉求',required: true,},
		{key:'sideCode',message:'请填写侧边喷码',required: true,},
		{key:'onSiteImg',message:'请上传现场照片',required: true,},
		{key:'noImg',message:'请上传侧边喷码（批号）',required: true,},
		{key:'breakImg',message:'请上传损坏的具体照片',required: true,},
	]
	let requiredFild_FKYJ = [
		{key:'salesDesc',message:'请填写销售描述问题',required: true,},
		{key:'onSiteImg',message:'请上传现场照片',required: true,},
		{key:'noImg',message:'请上传侧边喷码（批号）',required: true,},
		{key:'breakImg',message:'请上传损坏的具体照片',required: true,},
		
		
		{key:'inventoryCode',message:'请填写存货编码',required: true,},
		{key:'caseClass',message:'请选择问题分类',required: true,},
		{key:'orderNo',message:'请填写订单号',required: true,},
		{key:'badQty',message:'请填写发现不良品数',required: true,},
		{key:'badRemark',message:'请填写不良品描述',required: true,},
	]
	
	const handconfirm = async() =>{
		// debugger
		let requiredFildArr = (popInfo.value.id && prop.caseStageCode=='FKYJ' ? requiredFild_FKYJ:requiredFild);
		for(let index  in requiredFildArr){
		    var item =  requiredFildArr[index]
		    if(item.required && (!popInfo.value[item.key] || popInfo.value[item.key]=='' || (Array.isArray(popInfo.value[item.key]) && popInfo.value[item.key].length==0 ) )) return uni.showToast({
				title:item.message,
				duration: 1500,
				icon:'none'
			});
		};
		
		let postData = Object.assign({},popInfo.value);
		
		for (let [key, fildInfo] of fileFild) {
		    if(Array.isArray(popInfo.value[key])) {
		    	postData[key].forEach((item,index) => {
		    	    postData[`${key}${index+1}`] = item.url
		    	});
		    }
			if(popInfo.value.id && prop.caseStageCode=='FKYJ'){
				delete postData[key];
			}
		}
		debugger
		if(popInfo.value.caseClass && Array.isArray(popInfo.value.caseClass)){
			popInfo.value.caseClass = popInfo.value.caseClass.join(';'); postData.caseClass = popInfo.value.caseClass;
		}
		if(popInfo.value.id && prop.caseStageCode=='FKYJ'){
			debugger
			let {data} = await updateOrderDetail(postData)
		}
		if(showindex || showindex>=0){
			detailList.value[showindex] = popInfo.value;
		}else{
			detailList.value.push(popInfo.value)
		}
		showAddPop.value = false
		popInfo.value = {};
		emit('change',detailList.value);
	}
	
	const detailHistory = ref(null)
	
	const handleShow = (data,index) =>{
		debugger
		fileFild.forEach (function(value, key) {
			if(!Array.isArray(data[key])) {
				data[key] =  data[key]?data[key].split(';').map(i=>{
					// return{url:import.meta.env.VUE_APP_BASE_IMG + i}
					return	(prop.readOnly && prop.caseStageCode!=='FKYJ') ? import.meta.env.VUE_APP_BASE_IMG + i : {url:import.meta.env.VUE_APP_BASE_IMG + i}
				}):[] ;
			}
		})
		popInfo.value = data;
		showindex = index
		
		if(data.id){
			queryOrderDetailHistory(data.id).then((res) => {
				detailHistory.value = res.data;
			}).catch((err) => {
			});
		}
		
		if(popInfo.value.caseClass  && !Array.isArray(popInfo.value.caseClass))popInfo.value.caseClass = popInfo.value.caseClass.split(';')
		if(prop.readOnly && prop.caseStageCode!=='FKYJ'){
			showInfo.value = true;
		}else{
			showAddPop.value = true
		}
	}
	
	const calendarShow = ref(false)
	const calendarType = ref(null)
	const currentTime = ref(Date.now());
	const onCalendarShow  = (type,check = false) =>{
	    calendarType.value = type
	    calendarShow.value = true;
	}
	const onCalendarConfirm = ({mode,value}) => {
	    popInfo.value[calendarType.value] = `${dayjs(value).format('YYYY-MM-DD')}`;
	    // info.value[calendarType.value] = `${currentDate.value.join('-')} ${currentTime.value.join(':')}`
	    calendarShow.value = false
	};
	const reset=()=>{
		recordData.value = {}
		current.value = {}
		showLeft.value = false;
	}
	onMounted(async()=>{
		await dictStore.getDictBatch(['service_type','feedback_person_type','feedback_type','feedback_class','sheet_status','case_class']);
	})
	defineExpose({
		reset
	})
</script>

<style lang="scss" >
	.product_list{
		padding-bottom: 20px;
		.addInfo{
		    padding: 10px 0;
		    position: relative;
		    border-radius: var(--card-border-radius);
		    line-height: 1.4;
			position: relative;
			width: 100%;
			height: calc(100vh - 240px);
			overflow-y: scroll;
			overscroll-behavior: contain;
			transform:1;
		    .card{
		        padding: 0 15px;
		        background: none;
		        // padding: var(--card-padding);
		        position: relative;
		        border-radius: var(--card-border-radius);
		        line-height: 1.4;
		        // max-height: calc(100vh - 100px);
		        // overflow-y: scroll;
		        
		        .info{
		            font-size: 14px;
		            // margin: 5px 0;
		            display: flex;
		            flex-direction:row;
		            color: #333333;
		            width: 100%;
		            align-items: center;
		            line-height: 1.5;
		            .left{
		                // width: 60px;
		                color: var(--van-field-label-color);
		                &.required{
		                    &:before {
		                        margin-right: 2px;
		                        color: var(--van-field-required-mark-color);
		                        content: "*";
		                    }
		                }
		            }
		            .rigth{
		                margin-left: auto;
		                flex: 1;
		            }
		        }
		    }
			.u-form-item,.u-form-item__body__left__content__label,.uni-input-input,.uni-select,.u-input__content__field-wrapper__field{
				font-size: 12px !important;
			}
			.u-form-item__body{
				padding: 5px 0;
			}
			.u-form-item__body__right{
				padding: 4px;
			}
		}
		
		
	}
	.product_info{
		.card_info{
		    max-width: 100%;
		    padding: 10px;
			max-height: calc(100vh - 180px);
			overflow-y: scroll;
			overscroll-behavior: contain;
			.item{
				font-size: 14px;
				margin: 5px 0;
				display: flex;
				flex-direction:row;
				color: #333333;
				width: 100%;
				align-items: center;
				line-height: 1.5;
				.left{
					// width: 140px;
					color: #999999;
					flex: 1;
				}
				.rigth{
					margin-left: auto;
					flex: 1.5;
				}
			}
		}
	}
	.uni-table-td{
		font-size: 12px;
		padding: 4px 5px;
	}
	.detail_add{
	    color: #849EB2;
	    text-align: center;
	    margin-top: 10px;
		width: 100%; padding: 10px;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	.uni-group{
		.u-button,button{
			font-size: 12px;
			margin-top:10px ;
			padding: 12px 0;
			&:first-child{
				margin-top:0px ;
			}
		}
	}
	.card_head{
	    display: flex;
	    align-items: center;
	    // justify-content: center;
	    // border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
	    padding: 15px 0 10px 0;
	    &_tit{
	        display: flex;
	        align-items: center;
	        font-size: 18px;
	        font-weight: 600;
	        color: #333333;
	        span{
	            font-weight: 700;
	            font-size: 16px;
	            color: #000000;
	            margin-right: 5px;
	        }
	    }
	}

</style>