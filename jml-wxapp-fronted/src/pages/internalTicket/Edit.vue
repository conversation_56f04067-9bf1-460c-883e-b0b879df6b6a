<template>
    <div class="page pd100">
		<u--form labelPosition="top" labelWidth="auto" ref="form1" >
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">{{ info.ticket_type || '客诉类型'  }}</div>
                </div>
				<u-form-item label="主题" prop="info.subject" required >
					<u--input  v-model="info.subject"  class="int"  placeholder="主题"  />
				</u-form-item>
				<u-form-item label="工单日期"  prop="ticket_date"   required>
					<template >
						<view @click="onCalendarShow('ticket_date')">
						<u--input
							v-model="info.ticket_date"
							placeholder="工单日期"
							readonly
							disabled
							disabledColor="#ffffff"
							border="none"
							style="width: 100%;pointer-events: none;"
						/>
						</view>
					</template>
					<template #right><u-icon name="calendar" size="22" @click="onCalendarShow('ticket_date')"></u-icon></template>
				</u-form-item>
				<u-form-item label="描述"  prop="desc">
					<u--textarea :count="!!info.desc"  maxlength="800" v-model="info.desc"  class="int"   placeholder="描述"  />
				</u-form-item>
            </div>
        </div>
		</u--form>
		<u-datetime-picker
			:show="calendarShow"
			v-model="currentTime"
			confirmColor="#849EB2"
			closeOnClickOverlay
			@confirm="onCalendarConfirm"
			@cancel="calendarShow = false"
			@close="calendarShow = false"
			mode="date"
		></u-datetime-picker>
        <footer-btn  cancel_btn_text="保存草稿"  :confirm_btn_text="otherInfo.btnName || '提交受理'" @oncancel="save('NEW')" @onconfirm="save(otherInfo.btnCode?otherInfo.btnCode:'PROC')"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import LoadSelector from '@/components/LoadSelector.vue';
import {saveEdit,queryDetails}  from  '@/api/internalTicket'
import {queryListByStaff}  from  '@/api/company'
import {queryUserList,upload,asyncUpload}  from  '@/api/common'
import { isArray } from 'lodash';
import dayjs from "dayjs";
	const prop =  defineProps({
		id: String,
		isComponent: {
			type: Boolean,
			default: false,
		},
		accountId:{
			type: String,
			default: null,
		},
		ticket_type:{
			type: String,
			default: null,
		},
	})
	const emit = defineEmits(['confirm'])
    const router = useRouter();

    // 相关字典
    const dictStore = useDictStore()
    const companyList = ref([])
	const util = inject("util");
    const deleteFild = ['accName','createdStaffName','updateStaffName','factory_option_img','feedback_img','other_img','no_img','factory_option_img','break_img','closeStaffName'];
    const requiredFild = [
        {key:'subject',message:'请填写主题',required: true,},
        {key:'ticket_type',message:'请选择服务类型',required: true,},
    ]
    const save = (stage_code) => {
        for(let index  in requiredFild){
            var item =  requiredFild[index]
            if(item.required && (!info.value[item.key] || info.value[item.key]=='')) return uni.showToast({
				title:item.message,
				duration: 1500,
				icon:'none'
			});
        };
        let postData = {}
		//处理附件
		fileFild.forEach (function(fildInfo, key) {
			if(isArray(info.value[key])) {
				info.value[key].forEach((item,index) => {
				    info.value[`${key}${index+1}`] = item.url
				});
			}
		})
        for(var key in info.value){
            if(!deleteFild.includes(key))
                postData["ticket." + key] = info.value[key];
        }
        postData["ticket.stage_code"]= stage_code;
        saveEdit(postData).then((res)=>{
			uni.showModal({
				title: '提示',
				content: info.value.id ?'修改成功':'保存成功',
				showCancel:false,
				success: function (confirmres) {
					if (confirmres.confirm) {
						if(prop.isComponent)emit('confirm', res)
						else{
							uni.$emit("refresh", {refresh: true}); 
							router.back();
						}
					} else if (confirmres.cancel) {
						uni.$emit("refresh", {refresh: true}); 
						console.log('用户点击取消');
					}
				}
			});
        })
    }
	//需要处理的上传文件
	// const FileFild =[
	//     {key:'break_img',message:'损坏的具体照片',maxLenth: 1,},
	//     {key:'feedback_img',message:'反馈意见照片',maxLenth: 3,},
	//     {key:'no_img',message:'带有编号的照片',maxLenth: 1,},
	//     {key:'factory_option_img',message:'工厂照片',maxLenth: 3,},
	// ]

	const fileFild =new Map([
	    ['break_img',{key:'break_img',message:'损坏的具体照片',maxLenth: 1,}],
	    ['feedback_img',{key:'feedback_img',message:'反馈意见照片',maxLenth: 3,}],
	    ['no_img',{key:'no_img',message:'带有编号的照片',maxLenth: 1,}],
		['other_img',{key:'other_img',message:'带有编号的照片',maxLenth: 1,}],
	    ['factory_option_img',{key:'factory_option_img',message:'工厂照片',maxLenth: 3,}],
	])
    const getInfo = () =>{
        queryDetails(prop.id).then((res) => {
			var {btnCode,btnName} = res.data
			otherInfo.value = {btnCode,btnName}
			
			let { basic } = res.data
			//上传文件的处理
			// FileFild.forEach (function(itme, index) {
			// 	if((res.data.basic[itme.key] && res.data.basic[itme.key]!='')){
			// 		basic[itme.key] = res.data.basic[itme.key].split(';').map(i=>{
			// 			return{url:import.meta.env.VUE_APP_BASE_IMG + i}
			// 		})
			// 	}else{
			// 		basic[itme.key] = []
			// 	}
			// })
			fileFild.forEach (function(value, key) {
				basic[key] =  res.data.basic[key]?res.data.basic[key].split(';').map(i=>{
					return{url:import.meta.env.VUE_APP_BASE_IMG + i}
				}):[] 
			})
			info.value = basic;// Object.assign({},basic)
        }).catch((err) => {
        });
    }
    const info = ref({
        ticket_type:prop.ticket_type,
    });
    const otherInfo = ref({});
	
	const afterRead = async(event,type) =>{
		let files = [].concat(event.file)
		if(!info.value[type]) info.value[type] = [];
		let fileListLen =  info.value[type]?info.value[type].length:0
		files.map((item) => {
			info.value[type].push({
				...item,
				status: 'uploading',
				message: '上传中'
			})
		})
		for (let i = 0; i < files.length; i++) {
			try {
				const {data} = await asyncUpload(files[i].url)
				let item = info.value[type][fileListLen];
				info.value[type].splice(fileListLen, 1, {
				  ...item,
				  status: 'success',
				  message: '',
				  url: data.url,
				});
				fileListLen++;
			} catch (e) {
				console.log(e)
				uni.showToast({
					title: "图片上传失败",
					icon: "none"
				})
			}
		}
	}
	
	const beforeDelete = ({file,index},type) =>{
		uni.showModal({
			title: '提示',
			content: '是否删除该附件？',
			success: function (res) {
				if (res.confirm) {
					info.value[type].splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
	
	
    const userList =ref([])
    const init = async() =>{
        await dictStore.getDictBatch(['ticket_type','feedback_person_type']);
        (!userList.value || userList.value.length==0) && getUserList()
        if(!prop.account_id){
            let {data} = await queryListByStaff()
            companyList.value = data
        }
        if(prop.id){
            getInfo()
        }
    }
    const getUserList = ()=>{
        queryUserList().then((res) => {
            userList.value = res.data
        }).catch((err) => {
        });
    }
    const problemClassList =ref([])
    watch([()=>info.value.factory,()=>info.value.problem_type],(newvalue,oldvalue)=>{
        const factory = newvalue[0];
        const problem_type = newvalue[1];
        if(factory && problem_type){
            queryProblemClass(factory,problem_type).then((res) => {
                problemClassList.value = res.data
            }).catch((err) => {
            });
        }
    })
	const calendarShow = ref(false)
	const calendarType = ref(null)
	const currentTime = ref(Date.now());
	const onCalendarShow  = (type,check = false) =>{
	    calendarType.value = type
	    calendarShow.value = true;
	}
	const onCalendarConfirm = ({mode,value}) => {
	    info.value[calendarType.value] = `${dayjs(value).format('YYYY-MM-DD')}`;
	    // info.value[calendarType.value] = `${currentDate.value.join('-')} ${currentTime.value.join(':')}`
	    calendarShow.value = false
	};
	
    onMounted(() => {
        init()
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
</style>