<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        处理进度
                    </div>
                </div>
                <progress-status :status_list="info.stageList" dataKey="stage_code" dataLabel="stage_name" :active="info.basic.stage_code" :data="info.basic" readonly></progress-status>
            </div>
        </div>
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        {{ info.basic.name  }}
                    </div>
                    <div class="head_status bg">
                        {{ info.basic.ticket_stage }}
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
					<div class="item">
					    <div class="left">主题 </div>
					    <div class="rigth">
					         {{ info.basic.subject || '主题' }}
					    </div>
					</div>
                    
					<div class="item">
					    <div class="left">工单日期</div><div class="rigth">{{info.basic.ticket_date}}</div>
					</div>
					<div class="item">
					    <div class="left">描述</div><div class="rigth">{{info.basic.desc}}</div>
					</div>
					
                </div>
				
				<!-- 不同阶段的显示内容 -->
				<template v-if="info.stageList && findshow('PROC')">
					<div class="card_head_tit2 mt15">
						受理信息
					</div>
					<u-divider></u-divider>
					<div class="card_main">
						<div class="item">
							<div class="left">处理方案</div><div class="rigth">{{info.basic.ticket_solution}}</div>
						</div>
						<div class="item">
							<div class="left">方案描述</div><div class="rigth">{{info.basic.solution_desc}}</div>
						</div>
						<div class="item">
							<div class="left">关闭原因</div><div class="rigth">{{info.basic.close_remark}}</div>
						</div>
					</div>	
				</template>
					
					
				<template v-if="info.basic.stage_code && info.basic.stage_code=='CLOSE'">
				<div class="card_head_tit2 mt15">
				    结案信息
				</div>
				<u-divider></u-divider>
				<div class="card_main">
				    <div class="item">
				        <div class="left">结案人</div><div class="rigth">{{ info.basic.closeStaffName }}</div>
				    </div>
				    <div class="item">
				        <div class="left">结案时间</div><div class="rigth">{{ info.basic.close_time }}</div>
				    </div>
				</div>
				</template>
                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.basic.created_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.basic.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.basic.update_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.basic.updateStaffName }}</div>
                    </div>
                </div>
				
				<!-- 不同状态要填写的 -->
				<u--form labelPosition="top" labelWidth="auto" >
					<!-- {{info.btnCode}} -->
                    <template v-if="info.btnCode=='CLOSE'">
						<div class="card_head_tit2 mt15">
						   受理信息
						</div>
						<u-divider></u-divider>
						<u-form-item label="处理方案 " prop="ticket_solution" required >
							<zxz-uni-data-select
								clearable filterable placeholder="请选择"  
								v-model="model.ticket_solution" 
								:localdata="dict['ticket_solution']">
							</zxz-uni-data-select>
						</u-form-item>
						<u-form-item class="mt15" label="方案描述" prop="solution_desc" required>
							<u--textarea :count="!!model.solution_desc"   v-model="model.solution_desc"  class="int" maxlength="500"  placeholder="方案描述"  />
						</u-form-item>
						<u-form-item class="mt15" label="关闭原因" prop="close_remark" required>
							<u--textarea :count="!!model.close_remark"   v-model="model.close_remark"  class="int" maxlength="500"  placeholder="关闭原因"  />
						</u-form-item>
					</template>
				</u--form>
            </div>
        </div>
        <u-modal v-model:show="dialogShow" @confirm="dialogConfirm"   @close="dialogShow = false" @cancel="dialogShow = false" :title="dialogTitle" show-cancel-button>
            <div class="addInfo">
                <div class="card">
					<u--form labelPosition="top" labelWidth="auto" >
                    <u-form-item label="审批备注" prop="approval_remark" required>
                    	<u--textarea :count="!!popInfo.approval_remark"   v-model="popInfo.approval_remark"  class="int" maxlength="500"  placeholder="审批备注"  />
                    </u-form-item>
					</u--form>
                </div>
            </div>
        </u-modal>
	
        <related-list :data="info.relatedList" :id="routerQuery.id" :baseInfo="info.basic" title="相关信息"></related-list>
        
		<approve-list v-if="info.historyRelatedList && info.historyRelatedList.length>0 "  :list="info.historyRelatedList" :id="routerQuery.id" :baseInfo="info.basic" title="内部工单受理历史"></approve-list>
        
		<template v-if="routerQuery.type=='pages/internalTicket/Index'">
            <footer-btn v-if="['NEW'].includes(info.basic.stage_code)" cancel_btn_text="编辑"
                @oncancel="router.push({name: 'internalTicketEdit',params: {id:routerQuery.id}})" :confirm_btn_text="info.btnName"
                @onconfirm="save(info.btnCode)"
            ></footer-btn>
            <footer-btn v-if="['PROC'].includes(info.basic.stage_code)" :cancelBtnShow="false"  :confirm_btn_text="info.btnName"
                @onconfirm="save(info.btnCode)"
            ></footer-btn>
        </template>
        <!-- <ApproveBtn v-if="routerQuery.type=='pages/internalTicket/ApproveList'  && info.basic.stage_code=='ECSP'" cancel_btn_text="拒绝"  confirm_btn_text="通过"
            @oncancel="operate('cancel')" @onconfirm="operate('confirm')"
        ></ApproveBtn> -->
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import ProgressStatus from '@/components/ProgressStatus.vue';
import RelatedList from '@/components/RelatedList.vue';
import ApproveList from './components/ApproveList.vue';
// import ApproveBtn from './components/ApproveBtn.vue';
import {queryDetails,ticketDo}  from  '@/api/internalTicket'
import {asyncUpload}  from  '@/api/common'
import { isArray } from 'lodash';
	const prop =  defineProps({
		isComponent: {
			type: Boolean,
			default: false,
		},
		id: String,
		type:String
	})
    const router = useRouter();
	const util = inject("util");
    const dictStore = useDictStore()
    const routerQuery =  prop;
    const info = ref({
        basic:{},
        btnName:'',
        relatedList:[],
    });
    //提交用到
    const model = ref({
        id:routerQuery.id,
  //       feedback_img:[],
		// break_img:[],
		// no_img:[],
		// other_img:[],
		// factory_option_img:[],
        stage_code :info.value.btnCode,
    })
	const fileFild =new Map([
	    ['break_img',{key:'break_img',message:'损坏的具体照片',maxLenth: 1,}],
	    ['feedback_img',{key:'feedback_img',message:'反馈意见照片',maxLenth: 3,}],
	    ['no_img',{key:'no_img',message:'带有编号的照片',maxLenth: 1,}],
		['other_img',{key:'other_img',message:'带有编号的照片',maxLenth: 1,}],
	    ['factory_option_img',{key:'factory_option_img',message:'工厂照片',maxLenth: 3,}],
	])
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
			const { basic,btnCode,btnName,historyRelatedList,stageList } = res.data
			info.value = { basic,btnCode,btnName,historyRelatedList,stageList };
			let curinfo = stageList.find(i=>i.stage_code=== btnCode)
			uni.setNavigationBarTitle({
			　　title:`编辑${curinfo.stage_name}`
			})
        }).catch((err) => {
        });
    }
    const status_list = ref([]);
    const init = async() =>{
        const result = await dictStore.getDict('stage_code');
        status_list.value = result.map(item=>{
            return {label:item.dname,value:item.dval}
        })
        await dictStore.getDictBatch(['ticket_solution']);
        getInfo()
    }

    //审批用到
    const dialogShow = ref(false)
    const dialogTitle = ref('提示')
    const dialogConfirm = ()=>{
        if(operateType.value=='cancel'){
            popInfo.value.approval_status ='REJECT'
        }else if(operateType.value=='confirm'){
            popInfo.value.approval_status ='PASS'
        }
		popInfo.value.stage_code = info.value.btnCode;
        if((!popInfo.value.approval_remark || popInfo.value.approval_remark=='')) return uni.showToast({
			title: '备注必须填写',
			duration: 1500,
			icon:'none'
		});
        ticketDo(util.addPrefixToProperties(popInfo.value,'ticket.')).then((res)=>{
			uni.showModal({
				title: '提示',
				content: res.msg || '操作成功',
				showCancel:false,
				success: function (res) {
					if (res.confirm) {
						uni.$emit("refresh", {refresh: true}); 
						router.back()
					} else if (res.cancel) {
						console.log('用户点击取消');
					}
				}
			});
        })
    }
    const popInfo = ref({
        id:routerQuery.id,
    });
    const operateType = ref(null)
    const operate = (type) => {
        if(type=='cancel'){
            dialogTitle.value='拒绝操作'
        }else if(type=='confirm'){
            dialogTitle.value='通过操作'
        }
        operateType.value = type;
        dialogShow.value=true
    }

    //提交操作/保存
    const deleteFild = [];
    const requiredFild = [
        {key:'solutionType',message:'请选择处理类型',required: true,},
        {key:'solution',message:'请填写方案',required: true,},
        {key:'caseBtnStage',message:'caseBtnStage不能为空',required: true,},
    ]
	const imgFileFild = new Map([
	    ['feedback_img',{key:'feedback_img',message:'反馈意见照片',maxLenth: 1,}],
	])
    const save = (stage_code) => {
        // if(['处理意见','反馈意见'].includes(info.value.basic.stage_code)){
        //     for(let index  in requiredFild){
        //         var item =  requiredFild[index]
        //         if(item.required && (!model.value[item.key] || model.value[item.key]=='')) return ElMessage.error(item.message);
        //     };
        // }
        // if(['新建'].includes(info.value.basic.stage_code)){
        //     delete model.value.suppleInfo
        // }
		//处理附件
		imgFileFild.forEach (function(fildInfo, key) {
			if(isArray(model.value[key])) {
				model.value[key].forEach((item,index) => {
				    model.value[`${key}${index+1}`] = item.url
				});
			}
		})
		let post =Object.assign({},model.value)
		imgFileFild.forEach (function(fildInfo, key) {
			delete post[key]
		})
        if(stage_code)post.stage_code = stage_code ;
        ticketDo(util.addPrefixToProperties(post,'ticket.')).then((res)=>{
            uni.showModal({
            	title: '提示',
            	content: res.msg || '操作成功',
            	showCancel:false,
            	success: function (res) {
            		if (res.confirm) {
						uni.$emit("refresh", {refresh: true}); 
            			router.back()
            		} else if (res.cancel) {
            			console.log('用户点击取消');
            		}
            	}
            });
        })
    }
	
	
	const afterRead = async(event,type) =>{
		let files = [].concat(event.file)
		if(!model.value[type]) model.value[type] = [];
		let fileListLen =  model.value[type].length
		files.map((item) => {
			model.value[type].push({
				...item,
				status: 'uploading',
				message: '上传中'
			})
		})
		for (let i = 0; i < files.length; i++) {
			try {
				const {data} = await asyncUpload(files[i].url)
				let item = model.value[type][fileListLen];
				model.value[type].splice(fileListLen, 1, {
				  ...item,
				  status: 'success',
				  message: '',
				  url: data.url,
				});
				fileListLen++;
			} catch (e) {
				console.log(e)
				uni.showToast({
					title: "图片上传失败",
					icon: "none"
				})
			}
		}
	}
	
	const beforeDelete = ({file,index},type) =>{
		uni.showModal({
			title: '提示',
			content: '是否删除该附件？',
			success: function (res) {
				if (res.confirm) {
					model.value[type].splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
	}
	
	
	const findshow = computed(()=>(btnCode)=>{ //阶段对应显示的
	   let index = info.value.stageList.findIndex(i=> i.stage_code === btnCode)
	   const showArr = info.value.stageList.filter((item, i, arr) => i>index).map(item => item.stage_code);
	   return showArr.includes(info.value.basic.stage_code)>0
	})

	onShow(() => {
		uni.$once("refresh", (data) => {
			init()
		})
	})
    onMounted(() => {
        init()
    })
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
</style>
<style lang="scss" scoped>
    .page{
        --van-cell-group-inset-padding:0;
        --card-border-radius:5px;
        --card-padding:0 10px;
        --u-divider-line-height:1px;
        --u-divider-margin:0;
        --van-field-label-width:auto;
        --el-table-border:0;
        .addInfo{
        padding: 10px;
        position: relative;
        border-radius: var(--card-border-radius);
        line-height: 1.4;
		width: 100%;
        .card{
            background: none;
            padding: var(--card-padding);
            position: relative;
            border-radius: var(--card-border-radius);
            line-height: 1.4;
            .card_head{
                display: flex;
                align-items: center;
                justify-content: center;
                // border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
                padding: 15px 0 10px 0;
                &_tit{
                    display: flex;
                    align-items: center;
                    font-size: 18px;
                    font-weight: 600;
                    color: #333333;
                    span{
                        font-weight: 700;
                        font-size: 16px;
                        color: #000000;
                        margin-right: 5px;
                    }
                }
            }
        }
        :deep(){
            .int{
                background: none !important;
                .van-field__value{
                    background: #fff;
                }
                // &.van-cell:after{
                //     content: none;
                // }
            }
        }
    }
        :deep(){
            .int{
                background: none !important;
                .van-field__value{
                    background: #fff;
                }
                // &.van-cell:after{
                //     content: none;
                // }
            }
        }
        :deep(){
            .int{
                padding: 0;
                padding-top: 20px;
                &:first-child{
                    padding-top: 0px;
                }
                &:last-child{
                    padding-bottom: 20px;
                }
                &.pt0{
                    padding-top: 0px;
                }
                .van-field__value{
                    box-shadow: 0px 1px 4px 0px rgba(0,0,0,0.05);
                    border-radius: 4px;
                    opacity: 1;
                    border: 1px solid #E2E6F6;
                    padding: 8px;
                }
                &.van-cell:after{
                    content: none;
                }
            }
        }
        :deep(.el-select){
        width: 100%;
        line-height: normal;
        --el-disabled-bg-color:none;
        --el-text-color-placeholder:var(--van-field-placeholder-text-color);
        .el-input{
            --el-border:0;
            --el-component-size:24px;
            .el-input__wrapper{
                padding: 0;
                box-shadow:none;
                &.is-focus{
                    box-shadow:none !important;
                }
                .el-input__inner{
                    --el-input-inner-height:auto;
                }
            }
            &.is-focus{
                .el-input__wrapper{
                    padding: 0;
                    box-shadow:none !important;
                    
                }
            }
            &.is-disabled{
                .el-input__inner{
                    --el-text-color-placeholder:var(--van-field-placeholder-text-color);
                    --el-disabled-text-color:var(--van-field-placeholder-text-color);
                }
            }
        }
    }
    }
</style>