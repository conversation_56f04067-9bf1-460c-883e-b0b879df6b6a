
<template>
    <!-- 状态更改组件 -->
    <div class="approve_box">
        <div class="tit">{{title}}</div>
        <div class="list">
            <!-- <div v-for="(item,index) in list" @click.stop="change(item)" :key="index"  class="item">
                <div class="title">审批时间：2023-10-15 11:03:01</div>
                <div class="content">
                    <div class="txt">审批状态：通过</div>
                    <div class="txt">审批备注：通过了</div>
                    <div class="txt">审批人：zeta4</div>
                </div>
            </div> -->
            <div v-for="(item,index) in list" :key="index" class="item">
                <div class="title">时间：{{ item.acceptance_time }}</div>
                <div class="content">
                    <div class="txt">状态：{{ item.acceptance_status }}</div>
                    <div class="txt" v-if="item.solution_type">处理类型：{{ item.solution_type }}</div>
                    <div class="txt" v-if="item.solution">解决方案：{{ item.solution }}</div>
                    <div class="txt" v-if="item.remark">备注：{{ item.remark }}</div>
                    <div class="txt">操作人：{{ item.acceptance_staff_name }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
const util = inject("util");
const prop =  defineProps({
    data:{
        type:[Object,Number,String,Array],
        default: null
    },
    title: {
        type:String,
        default: '审批历史'
    },
    list:{
        type: [Array],
        default:[
        ]
    },
})

onMounted(()=>{
})
</script>

<style scoped lang="scss">
    .approve_box{
        padding: 10px;
        .tit{
            font-size: 18px;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
            color: #333333;
            line-height: 25px;
        }
        .list{
            border-radius: 6px;
            background: #fff;
            padding: 10px;
            margin-top: 10px;
            .item{
                font-size: 14px;
                font-family: PingFang SC-Regular, PingFang SC;
                font-weight: 400;
                color: rgba(0,0,0,0.9);
                line-height: 22px;
                padding: 10px;
                margin-bottom: 5px;
                .title{
                    display: flex;
                    // justify-content: center;
                    align-items: center;
                    &::before{
                        content: ' ';
                        width: 8px;
                        height: 8px;
                        display: inline-block;
                        background: #4085CB;
                        border-radius: 999px 999px 999px 999px;
                        margin-right: 12px;
                    }
                }
                .content{
                    margin: 0 4px;
                    border-left: 1px solid #4085CB;
                    font-size: 12px;
                    font-family: PingFang SC-Regular, PingFang SC;
                    font-weight: 400;
                    color: rgba(0,0,0,0.4);
                    // margin-bottom: 20px;
                    .txt{
                        margin-left: 15px;
                    }
                }
            }
        }
    }
</style>
