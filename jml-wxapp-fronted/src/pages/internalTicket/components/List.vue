<template>
    <div class="page">
        <div class="head">
            <div class="tool">
                <div @click="showTypePickerFun('ticketType','ticket_type')"  class="item">
                    <u-icon size="6" labelSize="12" labelPos="left" :label="searchQuery.ticketType ? searchQuery.ticketType :'类型'"  name="/static/icon/sel_icon.png" />
                </div>
            </div>
            <div class="search">
                <u-search
                    v-model="searchQuery.multiFiled"
                    placeholder="工单编号"
                    shape="square"
                    @change="util.debounce(onSearch)"
                	@custom="util.debounce(onSearch)"
                >
                </u-search>
            </div>
            <!-- <div class="search_btn">{{}}</div> -->
        </div>
        <div class="card_list">
            <div class="card_tool">
                <div class="tool">
                    <div @click="reset" v-if="showReset" style="position: absolute; left: 0;  display: flex; align-items: center;">
                        <u-icon size="16" name="close-circle-fill" color="#849EB2" />
                        <span class="" style=" margin-left: 5px; font-weight: normal;">清除筛选</span>
                    </div>
                    <!-- <div @click="showTypePickerFun('casePriority','case_priority')"  class="item">
						<u-icon size="6" labelSize="12" labelPos="left" :label="searchQuery.casePriority ? searchQuery.casePriority :'优先级'"  name="/static/icon/sel_icon.png" />
                    </div> -->
                    <div @click="showPickerFun"  class="item">
                        <u-icon size="6" labelSize="12" labelPos="left" :label="selectedUser && selectedUser.name?selectedUser.name:'员工'" name="/static/icon/sel_icon.png" />
                    </div>
                    <div @click="calendarShow= true" class="item">
                        <u-icon size="6" labelSize="12" labelPos="left" :label="searchQuery.startDate ? `${searchQuery.startDate} - ${searchQuery.endDate}` : '日期'" name="/static/icon/sel_icon.png" />
                    </div>
                </div>
            </div>
            <view class="swiper-item" style="height: calc(100% - 30px )">
				<scroll-view style="height:100%" scroll-y enable-flex  @scrolltolower="getDataPage()">		
            		<uni-swipe-action>
            		<div v-for="(item,index) in data.list" :key="index"  class="van-swipe-cell approve-swipe-cell">
            			<uni-swipe-action-item :disabled="item.stage_code!='NEW' && urlLink!=='pages/complaintApprove/Index'">
							
							<template v-if="item.stage_code=='NEW'" #left>
								<div @click.stop="deleteRecord(item.id)" class="card_left">
									<div class="card_left_btn">
										<u-icon size="20" color="#fff" name="trash" />
										<div class="txt">删除</div>
									</div>
								</div>
							</template>
							<div class="card"  @click="handDetail(item)">
								<div class="card_head">
									<div class="card_head_tit">
										工单编号:{{ item.name }}
									</div>
									<div class="head_status bg">
										{{ item.ticket_type }}
										<!-- <u-image v-if="item.case_record_type=='常规客诉'" width="auto" height="20" src="/static/icon/complaint_type1.png"/>
										<u-image v-if="item.case_record_type=='表面品质客诉'" width="auto" height="20" src="/static/icon/complaint_type2.png"/> -->
									</div>
								</div>
								<u-divider></u-divider>
								<div class="card_main">
									<div class="item">
										<div class="left">主题</div>
										<div class="rigth">{{item.subject || '主题'}}</div>
									</div>
									
									<div  class="item">
										<div class="left">工单日期</div>
										<div class="rigth">{{item.ticket_date}}</div>
									</div>
									<div v-if="item.ticket_solution" class="item">
										<div class="left">解决方案</div>
										<div class="rigth">{{item.ticket_solution}}</div>
									</div>
									<div class="card_footer">
										<div class="item">
											<div class="left">创建时间</div>
											<div class="rigth">{{item.created_time}} <span class="link">查看 <u-icon name="arrow-right" size="12" /></span> </div>
										</div>
										<div class="item">
											<div class="left">创建员工</div>
											<div class="rigth">{{item.createdStaffName}}</div>
										</div>
									</div>
								</div>
								<u-divider></u-divider>
								<progress-status :data="item" readonly :status_list="item.stageList" dataKey="stage_code" dataLabel="stage_name" :active="item.stage_code"></progress-status>
							</div>
							<template  #right>
								<div v-if="urlLink==='pages/complaintApprove/Index'" class="card_approve">
									<div @click.stop="operate('cancel',item.id)" class="card_right_btn refuse">
										<u-icon size="20" name="/static/icon/refuse.png" />
										<div class="txt">拒绝</div>
									</div>
									<div @click.stop="operate('confirm',item.id)" class="card_right_btn agree">
										<u-icon size="20" name="/static/icon/agree.png" />
										
										<div class="txt">同意</div>
									</div>
								</div>
								<div class="card_right" v-if="urlLink=='pages/complaint/Index' && item.stage_code =='NEW' ">
									<div @click.stop="router.push({name: 'complaintEdit',params: {id:item.id}})" class="card_right_btn">
										<u-icon size="20" name="/static/icon/edit.png" />
										<div class="txt">编辑</div>
									</div>
								</div>
							</template>
					
						</uni-swipe-action-item>
					</div>
					</uni-swipe-action>
					<div class="empty_box" v-if="!data.loading && (!data.list ||  data.list.length<=0)">
						<u-empty v-if="!data.list || data.list.length==0" text="未找到信息" :icon="`${config.static}images/empty/data.png`" ></u-empty>
						<div  @click="showAddPop()"
							class="btn_add"><u-icon size="20" name="/static/icon/btn_add_icon.png" />  <text class="span">新建 </text></div>
					</div>                    		
               </scroll-view>
           </view>
        </div>

		<u-calendar v-model:show="calendarShow" color="#849EB2"
			monthNum = "24"
			:minDate="dayjs().add(-1, 'year').format('YYYY-MM-DD')"
			:maxDate="dayjs().add(1, 'days').format('YYYY-MM-DD')"
			confirm-disabled-text="请选择结束时间" mode="range"  round="10" 
			@confirm="onCalendarConfirm"
			closeOnClickOverlay
			@close="calendarShow=false" 
		/>
	
       
	   <u-picker
			title="创建员工"
			v-model:show="showPicker"
			:columns="[userList]"
			@confirm="onUserConfirm"
			@cancel="showPicker = false"
			keyName="name"
			closeOnClickOverlay
	   />
		
		<u-picker
		    title="请选择"
			v-model:show="showTypePicker"
		    :columns="columns"
		    @confirm="onTypeConfirm"
		    @cancel="showTypePicker = false"
			keyName="dname"
			closeOnClickOverlay
		/>

        <u-modal v-model:show="dialogShow" @confirm="dialogConfirm" :title="dialogTitle" @close="dialogShow = false" @cancel="dialogShow = false" confirmColor="#849EB2" show-cancel-button>
            <div class="addInfo">
                <div class="card">
					<u--form labelPosition="top" labelWidth="auto" ref="form1" >
						<u-form-item label="备注" prop="info.name" required>
							<u--textarea count v-model="popInfo.remark"  class="int"   placeholder="备注"  />
						</u-form-item>
					</u--form>	
                </div>
            </div>
        </u-modal>

        <!-- 选择添加服务类型 -->
        <u-modal v-model:show="addDialogShow" :before-close="beforeClose" @confirm="addConfirm" @close="addDialogShow = false" @cancel="addDialogShow = false" title="选择服务类型" show-cancel-button confirmColor="#849EB2" confirm-button-text="提交">
            <div class="addInfo">
                <div class="card">
                    <u-radio-group v-model="addType" activeColor="#849EB2" iconPlacement="right" placement="column">
						<template v-for="item in dict['ticket_type']" :key="item.dval">
                        <u-radio :customStyle="{padding:'5px 10px'}"   :label="item.dname" :name="item.dval" />
						</template>
                    </u-radio-group>
					
                </div>
            </div>
        </u-modal>
        <floating-window>
            <div
                v-if="hasPermission('InternalTicket','C') && urlLink=='pages/internalTicket/Index'"
                @click="showAddPop()"
                class="item add_btn"
            >
               <u-icon size="20"  name="/static/icon/add.png" />
            </div>
        </floating-window>
    </div>
</template>
<script setup>
	import FloatingWindow from '@/components/FloatingWindow.vue';
	import ProgressStatus from '@/components/ProgressStatus.vue';
	import {queryPageData,deleteTicket,ticketDo}  from  '@/api/internalTicket'
	import {queryUserList}  from  '@/api/common'
	import dayjs from "dayjs";
	
    const router = useRouter();
	const util = inject("util");
    const {data} = useDataList()
    const dictStore = useDictStore()
    const finishedText = computed(() => {
        return  data.list.length>0?'没有更多了':'';
    })
    const onSearch = () => {
        initEmpty()
    }
    const routerQuery =  defineProps({});
	var pages = getCurrentPages();
	//获取当前页面
	var currentPage = pages[pages.length - 1];
	const urlLink = ref(currentPage.route)
	console.log(currentPage, '打印当前页路由')
	
    const searchQuery = reactive({
        multiFiled:'',
        flag: urlLink.value=='pages/complaint/ApproveList'?'1':'',
        ticketType:'',
        startDate: routerQuery.startDate || '',
        endDate: routerQuery.endDate || '',
        createdStaffId:'',
        pageNo:data.pageNo,
        pageSize:data.pageSize,
    })
	console.log(searchQuery)
    const getDataPage = (pageNo) =>{
		// debugger
    	if (data.finished || data.loading)return;
    	searchQuery.pageNo = pageNo || data.pageNo;
    	data.loading = true;
        queryPageData(searchQuery).then((res) => {
            data.list = [...data.list, ...res.data.list]
            data.totalRow = res.data.totalRow
            data.pageNo = data.pageNo + 1 ;
            data.loading = false;
            data.finished = res.data.lastPage;
        }).catch((err) => {
            data.finished = true;
        });
    }

    const deleteRecord = (id)=>{
		uni.showModal({
		    title: '提示',
		    content: '是否删除该记录？',
			success: function (unires) {
				if (unires.confirm) {
					deleteTicket(id).then((res) => {
					    uni.showModal({
					        title:'提示',
					        showCancel:false,
					        content: '操作成功',
					    }).then((aa) => {
					        initEmpty()
					    })
					}).catch((err) => {
					});
				}
			}
		})
    }

    const status_list = ref([]);
    const init = async() =>{
        const result = await dictStore.getDict('case_stage');
        status_list.value = result.map(item=>{
            return {label:item.dname,value:item.dval}
        })
		data.finished && (data.finished = false)
        getDataPage()
    }
    const initEmpty =() =>{
        data.initEmpty()
		getDataPage(1)
    }
    onMounted(() => {
		init()
    })
    //点击明细
    const handDetail =(item)=>{
        router.push({name: 'internalTicketInfo',params: {id: item.id,type:urlLink.value}})
    }
    //审批用到
    const dialogShow = ref(false)
    const dialogTitle = ref('提示')
    const dialogConfirm = ()=>{
        if(operateType.value=='cancel'){
            popInfo.value.caseBtnStage='拒绝'
        }else if(operateType.value=='confirm'){
            popInfo.value.caseBtnStage='通过'
        }
        if((!popInfo.value.remark || popInfo.value.remark=='')) return uni.showToast({
			title: '备注必须填写',
			duration: 1500,
			icon:'none'
		});
        caseDo(popInfo.value).then((res)=>{
			uni.showModal({
			    title:'提示',
			    showCancel:false,
			    content: res.msg || '操作成功',
			}).then((aa) => {
			    router.back()
			})
        })
    }
    const popInfo = ref({
        id:null
    });
    const operateType = ref(null)
    const operate = (type,id) => {
        if(type=='cancel'){
            dialogTitle.value='拒绝操作'
        }else if(type=='confirm'){
            dialogTitle.value='通过操作'
        }
        operateType.value = type;
        popInfo.value.id=id;
        dialogShow.value=true
    }

    // 添加相关
    const addDialogShow = ref(false)
    const addType = ref(null)
    const showAddPop = async()=>{
        !dictStore.hasDict('ticket_type') && await dictStore.getDictBatch(['ticket_type']);
        addDialogShow.value = true;
    }
    const addConfirm = ()=>{
        if(!addType.value)  return uni.showToast({
			title: '请选择类型',
			duration: 1500,
			icon:'none'
		});
		addDialogShow.value = false;
        router.push({name: 'internalTicketEdit',params: {ticket_type:addType.value}})
    }
    const beforeClose = (action) =>{
        return action !== 'confirm' || (addType.value)
    }

    /**
     *  搜索相关 开始
     */
    const calendarShow = ref(false)
    const onCalendarConfirm = (values) => {
    	const start= values[0],end = values[values.length - 1];
        calendarShow.value = false;
        searchQuery.startDate = `${dayjs(start).format('YYYY-MM-DD')}`;
        searchQuery.endDate = `${dayjs(end).format('YYYY-MM-DD')}`;
        initEmpty()
    };
    
    // 是否显示清除图标
    const showReset = computed(()=>{
        return (searchQuery.startDate != '' ||
        searchQuery.endDate != '' ||
        // searchQuery.casePriority != '' ||
        searchQuery.ticketType != '' ||
        searchQuery.createdStaffId != '' ||
        selectedUser.value != null)
    });
    //清空搜索条件
    const reset = () =>{
        searchQuery.startDate = ''
        searchQuery.endDate = ''
        searchQuery.ticketType= ''
        searchQuery.createdStaffId= ''
        // searchQuery.casePriority=''
        selectedUser.value = null
        initEmpty()
    }
    const showPicker = ref(false)
    const showTypePicker = ref(false)
    const pickerType = ref(null)
    const userList =ref([])
    const userLoad = ref(true)
    const getUserList = ()=>{
        queryUserList().then((res) => {
            userList.value = res.data
            userList.value.unshift({name:'全部',id:''})
            userLoad.value = false
            if(prop.createdStaffId) selectedUser.value = userList.value.find(i=>i.id==prop.createdStaffId)
        }).catch((err) => {
        });
    }
    const showPickerFun = ()=>{
        (!userList.value || userList.value.length==0) && getUserList()
        showPicker.value = true
    }
    const selectedUser = ref(null)
	const onUserConfirm = ({value,indexs}) => {
		console.log('onUserConfirm',value,indexs)
		let select = value[0];
		if(select){
			selectedUser.value = select
			searchQuery.createdStaffId = select.id
			initEmpty()
	    }
		showPicker.value = false
	}
	
	const columns = ref([]);
	const showTypePickerFun = async(type,key)=>{
		
	    pickerType.value = {type,key}
	    columns.value[0] = await dictStore.getDict(key);
		// debugger
	    showTypePicker.value = true
	}
	const onTypeConfirm = ({value,indexs}) =>{
		console.log('onTypeConfirm',value,indexs)
		let select = value[0];
		if(select){
			searchQuery[pickerType.value.type] = select.dval
			initEmpty()
		}
		showTypePicker.value = false
	}
	onShow(() => {
		uni.$once("refresh", (data) => {
			addType.value = false;
			initEmpty()
		})
	})
</script>
<style lang="scss" scoped>
    @import '@/styles/list.scss';
    
    .page{
		.search{
			margin-left: 10upx;
		}
        .addInfo{
			padding: 10px;
			position: relative;
			border-radius: var(--card-border-radius);
			line-height: 1.4;
			width: 100%;
			.u-icon{
				padding: 10px 15px;
			}
			.card{
				background: none;
				padding: var(--card-padding);
				position: relative;
				border-radius: var(--card-border-radius);
				line-height: 1.4;
				.card_head{
					display: flex;
					align-items: center;
					justify-content: center;
					// border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
					padding: 15px 0 10px 0;
					&_tit{
						display: flex;
						align-items: center;
						font-size: 18px;
						font-weight: 600;
						color: #333333;
						span{
							font-weight: 700;
							font-size: 16px;
							color: #000000;
							margin-right: 5px;
						}
					}
				}
			}
			
		}

	}
</style>