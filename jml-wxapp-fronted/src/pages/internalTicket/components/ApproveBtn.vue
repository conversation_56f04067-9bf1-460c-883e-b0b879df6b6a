
<template>
    <!-- 【底部 确认、取消 按钮组】 -->
    <div  class="footer">
        <div class="footer_btn" :style="footerBtnBoxStyle">
            <div v-if="cancelBtnShow" @click="oncancel" class="cancel_btn btn">
                <van-icon size="20" name="/static/icon/refuse.png" />
                {{cancel_btn_text}}
            </div>
            <div v-if="confirmBtnShow" @click="onconfirm" class="confirm_btn btn">
                <van-icon size="20" name="/static/icon/agree.png" />
                {{confirm_btn_text}}
            </div>
        </div>
        <div v-if="footer_tip && footer_tip!==''" class="footer_tip">{{footer_tip}}</div>
    </div>
</template>

<script>
export default {
    name: 'FooterBtn',
    props: {
        footer_tip: {
            type:String,
            default: null
        },
        footerBtnBoxStyle: {
            type:  [Object, String],
            default:"justify-content:space-evenly"
        },
        cancel_btn_text: {
            type:String,
            default: '取消'
        },
        confirm_btn_text: {
            type:String,
            default: '确认'
        },
        confirmBtnShow:{
            type:Boolean,
            default:true
        },
        cancelBtnShow: {
            type: Boolean,
            default:true,
        },
    },
    setup(prop,context) {
        const oncancel = (e) => {
            context.emit('oncancel')
        };
        const onconfirm = () => {
            context.emit('onconfirm')
        };
        return {
            onconfirm,
            oncancel,
        };
    },
};
</script>

<style scoped lang="scss">
    .footer{
        flex: 0 0 auto; 
        // height:70px;
        padding:0px;
        margin-top: auto;
        position: fixed;
        left: 0;
        bottom: 0;
        width: 100%;
        background: #fff;
        z-index: 999;
        .footer_btn{
            display: flex;
            // justify-content: center;
            // justify-content: space-between;
            width: 100%;
            justify-content: space-evenly;
            .btn{
                flex: 1;
                height: 50px;
                line-height: 50px;
                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                // background: linear-gradient(180deg, #F3F3F3 0%, #D4D4D4 100%);
                // box-shadow: inset 0px 0px 2px 1px #FFFFFF;
                background: #EB7155;
                border-radius: 0px;
                text-align: center;
                color: #FFFFFF;
                // margin-right: 30px;
                &.confirm_btn{
                    // margin-left: auto;
                    // background: linear-gradient(180deg, #FFAF78 0%, #FF6A00 100%);
                    background: #88D39B;
                }
                &:last-child{
                    margin-right: 0px;
                }
                .van-icon{
                    margin-right: 8px;
                }
            }
        }
        .footer_tip{
            margin-top: 10px;
            color: rgba(61, 61, 61, 0.5);
            font-size: 12px;
            line-height: 24px;
            text-align: center;
            &::before{
                content: '*';
                color: rgba(255, 2, 2, 1);
                font-size: 14px;
                margin-right: 5px;
                display: inline-block;
                vertical-align: middle;
            }
        }
    }
</style>
