1.如果客诉明细返回的阶段为：新建，case_stage，前端需要加个按钮为：
【编辑】点击后直接跳转至编辑页面，【保存】调用:saveCase 接口
  另外一个按钮从明细中的btnName 值，点击调用caseDo接口：case.case_stage_code : 阶段code：取值btnCode

若服务类型【service_type】不为：售后服务：
显示按钮：
1.当btnName为： 销售助理受理，且btnCode为：FKYJ
传值以下：
case.id:记录Id
case.case_stage_code : 阶段code：取值btnCode

2.当btnName为：结案，且btnCode为：CLOSE
传值以下：
case.id:记录Id
case.case_stage_code : 阶段code：取值btnCode
case.feedback_remark:反馈意见备注 文本区域

若服务类型【service_type】为：售后服务：
-内部人员提交明细界面：
1.当btnName为： 销售助理受理，且btnCode为：FKYJ
传值以下：
case.id:记录Id
case.case_stage_code : 阶段code：取值btnCode
case.feedback_person_type:反馈人类型，字典值
case.feedback_remark:反馈意见备注
case.feedback_img1/case.feedback_img2case./feedback_img3:反馈意见照片
case.case_class:事件分类 下拉选多选-字典

-销售助理提交明细界面：
2.当btnName为：销售总监受理，且btnCode为：CLFA
传值以下：
case.id:记录Id
case.case_stage_code : 阶段code：取值btnCode
case.solution_type: 处理方案 -多选-字典值
case.return_num - 退货（张数） 数字
case.exchange_num- 换货（张数） 数字
case.rework_num - 返工（张数）
case.discounts_amount - 折让（张数） 数字
case.claimant_amount -索赔（金额） 数字

-销售总监提交明细界面：
3.当btnName为：提交，且btnCode为：SMT
传值以下：
case.id:记录Id
case.case_stage_code : 阶段code：取值btnCode
case.solution_type: 处理方案 -多选-字典值
case.return_num - 退货（张数） 数字
case.exchange_num- 换货（张数） 数字
case.rework_num - 退货（张数） 数字
case.discounts_amount - 退货（张数） 数字
case.claimant_amount -索赔（金额） 数字

-工厂人员提交明细界面：
4.当btnName为：总经理审批，且btnCode为：ECSP
传值以下：
case.id:记录Id
case.case_stage_code : 阶段code：取值btnCode
case.factory_opino: 工厂意见 -文本区域
case.actory_option_img1- 工厂照片1
case.actory_option_img2- 工厂照片2
case.actory_option_img3- 工厂照片3

-总经理人员提交明细界面：
4.当客诉明细返回的阶段为：ECSP，则对应的按钮改为：
拒绝：
传值：case.case_stage 为 btnCode值
传值：case.approva_status 为 REJECT
通过：
传值：case.case_stage 为 btnCode值
传值：case.approva_status 为 REJECT
case.approval_remark- 审批备注