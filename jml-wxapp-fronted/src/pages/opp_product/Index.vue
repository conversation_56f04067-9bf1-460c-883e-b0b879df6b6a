<template>
    <div class="page">
        <div class="head">
            <!-- <div class="qty">共{{data.list.length}}条</div> -->
            <div class="search">
				<u-search
				    v-model="searchQuery.multiFiled"
				    placeholder="机会名称"
				    shape="square"
				    @change="util.debounce(onSearch)"
					@custom="util.debounce(onSearch)"
				>
				</u-search>
            </div>
            <!-- <div class="search_btn">搜索</div> -->
        </div>

       <div class="card_list" style="height: 100%;">
			<scroll-view style="height:100%" scroll-y enable-flex  @scrolltolower="getDataPage()">
				<uni-swipe-action>
				<div v-for="(item,index) in data.list" :key="index" class="van-swipe-cell"  @click="router.push({name: 'projectInfo',params: {id: item.id}})">
					<uni-swipe-action-item>
						<template #left>
						</template>
						<div class="card">
							<div class="card_head">
								<div class="card_head_tit">
									{{ item.name }}
								</div>
							</div>
							<van-divider></van-divider>
							<div class="card_main">
								<div class="item">
									<div class="left">机会类型</div>
									<div class="rigth">{{item.type}}</div>
								</div>
								<div class="item">
									<div class="left">来源</div>
									<div class="rigth">{{item.source}}</div>
								</div>
							</div>
							<progress-status :status_list="status_list" :active="item.stage" @update:active="handChange($event,item)"></progress-status>
							<van-divider></van-divider>
							<div class="card_footer">
								<div class="item">
									<div class="left">创建时间</div>
									<div class="rigth">{{item.created_time}} <span class="link">查看 <van-icon name="arrow" size="12" /></span> </div>
								</div>
								<div class="item">
									<div class="left">创建员工</div>
									<div class="rigth">{{item.createdStaffName}}</div>
								</div>
							</div>
						</div>
						<template #right>
							<div @click.stop="router.push({name: 'opp_productEdit',params: {id:item.id}})"  class="card_right">
								<div class="card_right_btn">
									<van-icon size="20" name="/assets/icon/edit.png" />
									<div class="txt">编辑</div>
								</div>
							</div>
						</template>
					</uni-swipe-action-item>
				</div>
				</uni-swipe-action>
				<div class="empty_box" v-if="!data.loading && (!data.list ||  data.list.length<=0)">
					<u-empty v-if="!data.list || data.list.length==0" text="未找到信息" :icon="`${config.static}images/empty/data.png`" ></u-empty>
					<div 
						v-if="hasPermission('OppProduct','C')"
						class="btn_add" 
						@click="router.push({name:'projectEdit'})"
						>
						<u-icon size="20" name="/static/icon/btn_add_icon.png" />  <span>新建</span>
					</div>
				</div>
			</scroll-view>
		</div>
				
		<floating-window>
		    <div
		        v-if="hasPermission('OppProduct','C')"
				@click="router.push({name:'opp_productEdit'})"
		        class="item add_btn"
		    >
		        <u-icon size="20" name="/static/icon/add.png"></u-icon>
		    </div>
		</floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject,computed} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import ProgressStatus from '@/components/ProgressStatus.vue';
import { useDataList } from '@/vueUse/dataList'
import {queryPageData}  from  '@/api/opp_product'
import {useDictStore} from '@/store/modules/dictStore'

    const router = useRouter();
    const util = inject("util");
    const {data} = useDataList()
    const dictStore = useDictStore()
    
    //触底加载
    const lowerBottom=()=>{
    	console.log('lowerBottom');
    }
    // 下拉刷新
    const refresherTriggered = ref(false);
    const getFresh=()=> {
    	console.log('refresh');
    	refresherTriggered.value = 'restore'
    	initEmpty()
    }
    const onPulling=(e)=> {
    	console.log('onPulling');
    	if (e.detail.deltaY < 0) return
    }
    const finishedText = computed(() => {
        return  data.list.length>0?'没有更多了':'';
    })
    const onSearch = () => {
        initEmpty()
    }
    const searchQuery = reactive({
        multiFiled:'',
        pageNo:data.pageNo,
        pageSize:data.pageSize,
    })
    
    const getDataPage = () =>{
        searchQuery.pageNo = data.pageNo
        queryPageData(searchQuery).then((res) => {
            data.list = [...data.list, ...res.data.list]
            data.totalRow = res.data.totalRow
            data.pageNo = data.pageNo + 1 ;
            data.loading = false;
            data.finished = res.data.lastPage;
        }).catch((err) => {
            data.finished = true;
        });
    }
    const init = () =>{
        getDataPage()
    }
    const initEmpty =() =>{
        data.initEmpty()
    	getDataPage(1)
    }
    onMounted(() => {
    	init()
    })
    onReachBottom(() => {
    	console.log('onReachBottom')
    	if(!data.finished) getDataPage();
    })
    onShow(() => {
    	uni.$once("refresh", (data) => {
    		initEmpty()
    	})
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/list.scss';
</style>