<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        {{ info.basic.name }}
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">产品名称 </div>
                        <div class="rigth">
                            <span @click="router.push({name: 'productInfo',params: {id:info.basic.product_id}})" class="link">
                                {{info.basic.productName}}
                            </span>
                        </div>
                    </div>
                    <div class="item">
                        <div class="left">数量</div><div class="rigth">{{info.basic.qty}}</div>
                    </div>
                    <div class="item">
                        <div class="left">实际单价</div><div class="rigth">{{info.basic.unit_price}}</div>
                    </div>
                    <div class="item">
                        <div class="left">标准价格</div><div class="rigth">{{info.basic.list_price}}</div>
                    </div>
                    <div class="item">
                        <div class="left">实际总价</div><div class="rigth">{{info.basic.total_amount}}</div>
                    </div>
                    <div class="item">
                        <div class="left">标准总价</div><div class="rigth">{{info.basic.total_list_amount}}</div>
                    </div>
                    <div class="item">
                        <div class="left">交期</div><div class="rigth">{{info.basic.delivery_date}}</div>
                    </div>
                </div>

                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.basic.created_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.basic.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.basic.update_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.basic.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>
		
		<related-list :data="info.relatedList" ref="related" :id="routerQuery.id" :baseInfo="info.basic" title="产品相关"></related-list>
		<footer-btn v-if="hasPermission('OppProduct','U')"  
			:cancelBtnShow="false" 
			@onconfirm="router.push({name: 'opp_productEdit',params: {id:routerQuery.id}})" confirm_btn_text="编辑">
		</footer-btn>
		<floating-window></floating-window>
		
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import RelatedList from '@/components/RelatedList.vue';
import {queryDetails}  from  '@/api/opp_product'
    const prop =  defineProps(['id'])
    const router = useRouter();
    const util = inject("util");
    const routerQuery =  prop;
    const info = ref({
        basic:{},
        relatedList:[],
    });
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const init = () =>{
        getInfo()
    }
	onShow(() => {
		uni.$once("refresh", (data) => {
			init()
		})
	})
    onMounted(() => {
        init()
    })
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
</style>