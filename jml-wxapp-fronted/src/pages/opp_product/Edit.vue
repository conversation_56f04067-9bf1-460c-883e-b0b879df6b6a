<template>
    <div class="page pd100">
		<u--form labelPosition="top" labelWidth="auto" ref="form1" >
			<div class="card_info">
				<div class="card">
					<div class="card_head">
						<div class="card_head_tit">机会产品信息</div>
					</div>
				
					<u-form-item label="产品" prop="product_id" required>
						<zxz-uni-data-select clearable placeholder="请选择"  
						v-model="info.product_id" 
						dataText="name" 
						dataValue="id"
						filterable
						:localdata="productList"></zxz-uni-data-select>
					</u-form-item>

                    <div class="info mt10">
                        <div class="left">实际单价：</div><div class="rigth">{{ info.unit_price }}</div>
                    </div>
					<u-form-item label="数量" prop="qty">
						<u--input  v-model="info.qty" type="digit"   class="int"   placeholder="数量"  />
					</u-form-item>
					<u-form-item label="标准价格" prop="list_price">
						<u--input  v-model="info.list_price" type="number"   class="int"   placeholder="标准价格"  />
					</u-form-item>

                    <div class="info mt10">
                        <div class="left">实际总价：</div><div class="rigth">{{ info.total_amount }}</div>
                    </div>
                    <div class="info mt10">
                        <div class="left">标准总价：</div><div class="rigth">{{ info.total_list_amount }}</div>
                    </div>
					<u-form-item label="交期" prop="delivery_date" >
						<u--input
							v-model="info.delivery_date"
							placeholder="交期"
							readonly
							disabled
							disabledColor="#ffffff"
							border="none"
							style="width: 100%;pointer-events: none;"
							@click="onCalendarShow('delivery_date')"
						/>
						<template #right><u-icon size='22' name="calendar" @click="onCalendarShow('delivery_date')"></u-icon></template>
					</u-form-item>
                </div>
            </div>
		</u--form>			
		<u-datetime-picker
			:show="calendarShow"
			v-model="currentTime"
			confirmColor="#849EB2"
			closeOnClickOverlay
			@confirm="onCalendarConfirm"
			@cancel="calendarShow = false"
			@close="calendarShow = false"
			mode="date"
		></u-datetime-picker>
		
        <footer-btn :cancelBtnShow="false"  confirm_btn_text="保存" @onconfirm="save()"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  watch,nextTick  ,computed  ,  inject,getCurrentInstance} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import {queryDetails,saveEdit}  from  '@/api/opp_product'
import {queryProductList}  from  '@/api/common'
import dayjs from "dayjs";
    const prop =  defineProps({
    	id: String,
		opportunity_id: String,
    	isComponent: {
    		type: Boolean,
    		default: false,
    	},
    })
    const emit = defineEmits(['confirm'])
    const router = useRouter();
    const route = useRoute();
    const routerQuery = prop;// prop.isComponent ? {} : router.currentRoute.value.query;
    // 相关字典
    const dictStore = useDictStore()
    const util = inject("util");
	
    const lodash = inject("lodash");
    const deleteFild = ['accName','createdStaffName','updateStaffName','productName'];
    const save = () => {
        let postData = {}
        for(var key in info.value){
            if(!deleteFild.includes(key))
                postData["oppProduct." + key] = info.value[key];
        }
        saveEdit(postData).then((res)=>{
            uni.showModal({
            	title: '提示',
            	content: info.value.id ?'修改成功':'保存成功',
            	showCancel:false,
            	success: function (confirmres) {
            		if (confirmres.confirm) {
            			if(prop.isComponent)emit('confirm', res)
            			else{
            				uni.$emit("refresh", {refresh: true}); 
            				router.back();
            			}
            		} else if (confirmres.cancel) {
            			uni.$emit("refresh", {refresh: true}); 
            			console.log('用户点击取消');
            		}
            	}
            });
        })
    }
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data.basic
            // info.value.sample = `${info.value.sample}`
            // info.value.sent_quotation = `${info.value.sent_quotation}`
        }).catch((err) => {
        });
    }
	const productList = ref({});
    const info = ref({
        opportunity_id:routerQuery.opportunity_id,
        // unit_price:null,
        // qty:null,
        // list_price:null,
    });
    const init = async() =>{
        await dictStore.getDictBatch(['opp_source','opp_type']);
        let {data} = await queryProductList()
        productList.value = data
        if(routerQuery.id){
            getInfo()
        }
    }
    const product = ref({});
    // const total_amount = computed(()=>{
    //     return info.value.qty? info.value.unit_price * Number(info.value.qty) : 0
    // })

    // const total_list_amount = computed(()=>{
    //     return (info.value.qty && info.value.list_price) ? info.value.list_price * Number(info.value.qty) : 0
    // })
    watch(() => info.value.product_id,(newValue, oldValue) => {
        if(newValue){
            let productInfo = productList.value.find(i=> i.id == newValue );
            if(productInfo){
                info.value.unit_price = productInfo.unit_price;
            }
        }
    });
    watch([()=>info.value.unit_price,()=>info.value.list_price, () => info.value.qty],([unit_price,list_price, qty]) => {
        if(unit_price && qty){
            info.value.total_amount = lodash.round(unit_price * qty,2);
        }
        if(list_price && qty){
            info.value.total_list_amount = lodash.round(list_price * qty,2);
        }
    });
	const calendarShow = ref(false)
    const calendarType = ref(null)
    const currentTime = ref(Date.now());// ref([`${dayjs().format('HH')}`,`${dayjs().format('mm')}`]);
    const onCalendarShow  = (type,check = false) =>{
        // if(check && routerQuery.id && info.checkin_address && (info.value[calendarType.value] && info.value[calendarType.value]!='')) return false
        calendarType.value = type
        calendarShow.value = true;
    }
    const onCalendarConfirm = ({mode,value}) => {
        info.value[calendarType.value] = `${dayjs(value).format('YYYY-MM-DD')}`;
        // info.value[calendarType.value] = `${currentDate.value.join('-')} ${currentTime.value.join(':')}`
        calendarShow.value = false
    };
    
    onMounted(() => {
        init()
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
</style>