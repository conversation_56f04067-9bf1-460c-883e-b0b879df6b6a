<template>
    <div class="page pd100">
        <div class="card_info">
			<u--form labelPosition="top" labelWidth="auto" ref="form1" >
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">机会信息</div>
                </div>
				
					<u-form-item label="地区" prop="district" required>
						<zxz-uni-data-select clearable placeholder="请选择"  v-model="info.district" dataText="district" @change="areaChange" dataValue="district"  :localdata="userInfo.staffAreaList"></zxz-uni-data-select>
					</u-form-item>
					<u-form-item label="机会名字" prop="name" required>
						<u--input  v-model="info.name"  class="int"   placeholder="请输入机会名字"  />
					</u-form-item>
					<u-form-item label="公司" required prop="account_id">
						<load-selector v-model="info.account_id" filterable  load_url="/api/account/queryListByStaff" />
					</u-form-item>
					<!-- <u-form-item label="市场活动" required prop="marketing_id">
						<load-selector v-model="info.marketing_id" value-key="id"  value-fild="id"   load_url="/api/marketing/queryListByStaff"></load-selector>
					</u-form-item> -->
					<u-form-item label="来源" prop="source">
						<zxz-uni-data-select clearable placeholder="请选择"  v-model="info.source" dataText="dval" dataValue="dname"  :localdata="dict['opp_source']"></zxz-uni-data-select>
					</u-form-item>
					<u-form-item label="金额" prop="amount" required>
						<u--input  v-model="info.amount" class="int"   placeholder="请输入金额"  />
					</u-form-item>
					<u-form-item label="机会类型" required prop="type">
						<zxz-uni-data-select clearable  placeholder="请选择"  v-model="info.type" dataText="dval" dataValue="dname"  :localdata="dict['opp_type']"></zxz-uni-data-select>
					</u-form-item>
					<u-form-item label="是否样品" labelPosition="left" prop="sample" required>
						<view style="display: flex; justify-content: flex-end; ">
							<up-radio-group 
							    v-model="info.sample"
								activeColor="#849EB2"
							    placement="row">
								<view style="display: flex; justify-content: flex-end; width: 100%;">
								<up-radio  label="是"  name="1"></up-radio>
								<up-radio  label="否" :customStyle="{marginLeft: '15px'}" name="0"></up-radio>
								</view>
							</up-radio-group>
						</view>
					</u-form-item>
					<u-form-item label="样品备注" prop="sample_remark" >
						<u--textarea :count="!!info.sample_remark" maxlength="800"  v-model="info.sample_remark"  class="int"   placeholder="样品备注"  />
					</u-form-item>
					
					<u-form-item label="报价是否发送" prop="sent_quotation" required>
						<u-radio-group v-model="info.sent_quotation" activeColor="#849EB2" iconPlacement="left">
						    <u-radio :customStyle="{marginRight: '16px'}"  label="是" name="1"></u-radio>
						    <u-radio  label="否" name="0"></u-radio>
						</u-radio-group>
					</u-form-item>
					<u-form-item label="报价备注" prop="quote_remark" >
						<u--textarea :count="!!info.quote_remark" maxlength="800"  v-model="info.quote_remark"  class="int"   placeholder="报价备注"  />
					</u-form-item>
					<u-form-item label="合同金额" prop="contract_amount">
						<u--input  v-model="info.contract_amount" class="int"   placeholder="请输入合同金额"  />
					</u-form-item>
				</div>
			</u--form>
        </div>

        <footer-btn :cancelBtnShow="false"  confirm_btn_text="保存" @onconfirm="save()"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import LoadSelector from '@/components/LoadSelector.vue';
import Selector from '@/components/Selector.vue';
import dayjs from "dayjs";
import {queryDetails,saveEdit}  from  '@/api/opportunity'
import {queryListByStaff}  from  '@/api/company'
	const prop =  defineProps({
			id: String,
			isComponent: {
				type: Boolean,
				default: false,
			},
			accountId:{
				type: String,
				default: null,
			}
		})
	const routerQuery = prop;
	const appStore = useAppStore();
	const router = useRouter();
	const { userInfo } = useUserStore();

    // 相关字典
    const dictStore = useDictStore()
    const companyList = ref([])
	const util = inject("util");
    const deleteFild = ['accName','createdStaffName','updateStaffName','salesLeadName','marketingName','areaName'];
    const requiredFild = [
        {key:'name',message:'请填写机会名称',required: true,},
        {key:'amount',message:'请填写金额',required: true,},
        {key:'type',message:'请选择机会类型',required: true,},
    ]
    const save = () => {
        for(let index  in requiredFild){ 
            var item =  requiredFild[index]
            if(item.required && (!info.value[item.key] || info.value[item.key]=='')){
            	return uni.showToast({
            		title: item.message,
            		duration: 1500,
            		icon:'none'
            	});
            }
        };
        let postData = {}
        for(var key in info.value){
            if(!deleteFild.includes(key))
                postData["opp." + key] = info.value[key];
        }
        saveEdit(postData).then((res)=>{
            uni.showModal({
            	title: '提示',
            	content: info.value.id ?'修改成功':'保存成功',
            	showCancel:false,
            	success: function (confirmres) {
            		if (confirmres.confirm) {
            			if(prop.isComponent)emit('confirm', res)
            			else{
            				uni.$emit("refresh", {refresh: true}); 
            				router.back();
            			}
            		} else if (confirmres.cancel) {
            			uni.$emit("refresh", {refresh: true}); 
            			console.log('用户点击取消');
            		}
            	}
            });
        })
    }
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data.basic
            info.value.sample = info.value.sample?`${info.value.sample}`:null
            info.value.sent_quotation = info.value.sent_quotation?`${info.value.sent_quotation}`:null
        }).catch((err) => {
        });
    }
    const info = ref({
        account_id:routerQuery.account_id || prop.accountId,
		area_code:userInfo.staffAreaList.length==1 ? userInfo.staffAreaList[0].areaCode : null,
		district:userInfo.staffAreaList.length==1 ? userInfo.staffAreaList[0].district : null ,
    });
	const areaChange =(value)=>{
		info.value.area_code = userInfo.staffAreaList.find(i=>i.district===value)?.areaCode;
	}
    const init = async() =>{
        await dictStore.getDictBatch(['opp_source','opp_type']);
        if(!routerQuery.account_id){
            let {data} = await queryListByStaff()
            companyList.value = data
        }
        if(routerQuery.id){
            getInfo()
        }
    }
    onMounted(() => {
        init()
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
</style>