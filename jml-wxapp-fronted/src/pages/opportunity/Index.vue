<template>
    <div class="page">
		<div class="head">
		    <div class="search">
		        <u-search
		            v-model="searchQuery.multiFiled"
		            placeholder="机会姓名"
		            shape="square"
		            @change="util.debounce(onSearch)"
		    		@custom="util.debounce(onSearch)"
		        >
		        </u-search>
		    </div>
		</div>

        <div class="card_list">

            <scroll-view style="height:100%" scroll-y enable-flex  @scrolltolower="getDataPage()">
            	<uni-swipe-action>
					
			<div v-for="(item,index) in data.list" :key="index" class="van-swipe-cell"  @click="router.push({name: 'oppInfo',params: {id: item.id}})">
				
			    <uni-swipe-action-item>
			        <template #left>
			        </template>
							
                    <div class="card">
                        <div class="card_head">
                            <div class="card_head_tit">
                                {{ item.name }}
                            </div>
                            <div v-if="item.is_new =='1'" class="head_status">
								<u-image class="van-icon__image" width="40"  mode="widthFix" src="/static/icon/new.png" />
                            </div>
                        </div>
                        <u-divider></u-divider>
                        <div class="card_main">
                            <div class="item">
                                <div class="left">机会类型</div>
                                <div class="rigth">{{item.type}}</div>
                            </div>
                            <div class="item">
                                <div class="left">来源</div>
                                <div class="rigth">{{item.source}}</div>
                            </div>
                        </div>
						<progress-status dataKey="dval" dataLabel="dname" :status_list="status_list" :active="item.stage" :custom-change="customChange" :data="item" @update:active="handChange($event,item)"></progress-status>
                        <u-divider></u-divider>
                        <div class="card_footer">
                            <div class="item">
                                <div class="left">创建时间</div>
                                <div class="rigth">{{item.created_time}} <span  class="link">查看 <u-icon name="arrow-right" size="12" /></span> </div>
                            </div>
                            <div class="item">
                                <div class="left">创建员工</div>
                                <div class="rigth">{{item.createdStaffName}}</div>
                            </div>
                        </div>
                    </div>
                    <template #right>
                        <div @click.stop="router.push({name: 'oppEdit',params: {id:item.id}})" class="card_right">
                            <div class="card_right_btn">
                                <u-icon size="20" name="/static/icon/edit.png" />
                                <div class="txt">编辑</div>
                            </div>
                        </div>
                    </template>
                </uni-swipe-action-item>
            </div>
            </uni-swipe-action>
			
				<div class="empty_box" v-if="!data.loading && (!data.list ||  data.list.length<=0)">
					<u-empty v-if="!data.list || data.list.length==0" text="未找到信息" :icon="`${config.static}images/empty/data.png`" ></u-empty>
					<div 
						v-if="hasPermission('Opp','C')"
							class="btn_add" @click="router.push({name: 'oppEdit'})">
						<u-icon size="20" name="/static/icon/btn_add_icon.png" />  <span>新建</span>
					</div>
				</div>
			</scroll-view>
		</div>
		
		
		<u-modal v-model:show="dialogShow" confirmColor="#849EB2" @confirm="dialogConfirm"   @close="dialogShow = false" @cancel="dialogShow = false" title="丢失并关闭" show-cancel-button>
		    <div class="addInfo">
		        <div class="card">
					<u--form labelPosition="top" labelWidth="auto" ref="form1" >
						<u-form-item label="丢失原因" required prop="loss_cause">
							<zxz-uni-data-select  placeholder="请选择丢失原因"  v-model="popInfo.loss_cause" dataText="dval" dataValue="dname" :localdata="dict['opp_loss_cause']"></zxz-uni-data-select>
						</u-form-item>
						<u-form-item label="丢失原因描述" prop="loss_desc" >
							<u--textarea :count="!!popInfo.loss_desc" maxlength="150"  v-model="popInfo.loss_desc"  class="int"   placeholder="丢失原因描述"  />
						</u-form-item>
					</u--form>	
		        </div>
		    </div>
		</u-modal>
		
		
		<floating-window>
		    <div
		        v-if="hasPermission('Opp','C')"
				@click="router.push({name:'oppEdit'})"
		        class="item add_btn"
		    >
		        <u-icon size="20" name="/static/icon/add.png"></u-icon>
		    </div>
		</floating-window>
		
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject,computed} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import ProgressStatus from '@/components/ProgressStatus.vue';
import { useDataList } from '@/vueUse/dataList'
import {queryPageData,changeStage}  from  '@/api/opportunity'
import {useDictStore} from '@/store/modules/dictStore'

   const router = useRouter();
   const util = inject("util");
   const {data} = useDataList()
   const dictStore = useDictStore()
   
   //触底加载
   const lowerBottom=()=>{
   	console.log('lowerBottom');
   }
   // 下拉刷新
   const refresherTriggered = ref(false);
   const getFresh=()=> {
   	console.log('refresh');
   	refresherTriggered.value = 'restore'
   	initEmpty()
   }
   const onPulling=(e)=> {
   	console.log('onPulling');
   	if (e.detail.deltaY < 0) return
   }
   const finishedText = computed(() => {
       return  data.list.length>0?'没有更多了':'';
   })
   const onSearch = () => {
       initEmpty()
   }
   const searchQuery = reactive({
       multiFiled:'',
       pageNo:data.pageNo,
       pageSize:data.pageSize,
   })
   
    const getDataPage = () =>{
        searchQuery.pageNo = data.pageNo
        queryPageData(searchQuery).then((res) => {
            data.list = [...data.list, ...res.data.list]
            data.totalRow = res.data.totalRow
            data.pageNo = data.pageNo + 1 ;
            data.loading = false;
            data.finished = res.data.lastPage;
        }).catch((err) => {
            data.finished = true;
        });
    }

	const status_list = ref([]);
	const init = async() =>{
		status_list.value = await dictStore.getDict('opp_stage');
		getDataPage()
	}
    const initEmpty =() =>{
        data.initEmpty()
    	getDataPage(1)
    }
    
    onMounted(() => {
    	init()
    })
    
    onReachBottom(() => {
    	console.log('onReachBottom')
    	if(!data.finished) getDataPage();
    })
    onShow(() => {
    	uni.$once("refresh", (data) => {
    		initEmpty()
    	})
    })
	
    const dialogShow = ref(false)
    const dialogConfirm = ()=>{
        handChange(popInfo.value.stage,tempInfo,popInfo.value)
    }
    const popInfo = ref({});
    let tempInfo = null
    const customChange = async(status,info) =>{
		debugger
        if(status == '丢失关闭'){
            const result = await dictStore.getDictBatch(['opp_loss_cause']);
            dialogShow.value = true;
            tempInfo = info
            popInfo.value.stage = status
            return false
        }else{
			return uni.showModal({
				title: '提示',
				content: '是否切换该状态？',
			}).then((res)=>{
				if (res.confirm) {
					handChange(status,info)
				} else if (res.cancel) {
					return false
				}
			}).catch(error => {
				return false
			    console.error('模态弹窗显示失败：', error);
			});
        }
    }
    const handChange =(status,info,other = {})=> {
        changeStage({...{id:info.id,stage:status},...other}).then((res) => {
            info.stage = status
			dialogShow.value = false
            initEmpty()
        }).catch((err) => {
        });
    }
</script>
<style lang="scss" scoped>
    @import '@/styles/list.scss';
    .page{
        --van-cell-group-inset-padding:0;
        --card-border-radius:5px;
        --card-padding:0 10px;
        --van-divider-line-height:1px;
        --van-divider-margin:0;
        --van-field-label-width:auto;
        --el-table-border:0;

        .addInfo{
        padding: 10px;
        position: relative;
        border-radius: var(--card-border-radius);
        line-height: 1.4;
		width: 100%;
        .card{
            background: none;
            padding: var(--card-padding);
            position: relative;
            border-radius: var(--card-border-radius);
            line-height: 1.4;
            .card_head{
                display: flex;
                align-items: center;
                justify-content: center;
                // border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
                padding: 15px 0 10px 0;
                &_tit{
                    display: flex;
                    align-items: center;
                    font-size: 18px;
                    font-weight: 600;
                    color: #333333;
                    span{
                        font-weight: 700;
                        font-size: 16px;
                        color: #000000;
                        margin-right: 5px;
                    }
                }
            }
        }
        :deep(){
            .int{
                background: none !important;
                .van-field__value{
                    background: #fff;
                }
                // &.van-cell:after{
                //     content: none;
                // }
            }
        }
    }

        :deep(){
            .int{
                background: none !important;
                .van-field__value{
                    background: #fff;
                }
                // &.van-cell:after{
                //     content: none;
                // }
            }
        }
        :deep(){
            .int{
                padding: 0;
                padding-top: 20px;
                &:first-child{
                    padding-top: 0px;
                }
                &:last-child{
                    padding-bottom: 20px;
                }
                &.pt0{
                    padding-top: 0px;
                }
                .van-field__value{
                    box-shadow: 0px 1px 4px 0px rgba(0,0,0,0.05);
                    border-radius: 4px;
                    opacity: 1;
                    border: 1px solid #E2E6F6;
                    padding: 8px;
                }
                &.van-cell:after{
                    content: none;
                }
            }
        }
        :deep(.el-select){
        width: 100%;
        line-height: normal;
        --el-disabled-bg-color:none;
        --el-text-color-placeholder:var(--van-field-placeholder-text-color);
        .el-input{
            --el-border:0;
            --el-component-size:24px;
            .el-input__wrapper{
                padding: 0;
                box-shadow:none;
                &.is-focus{
                    box-shadow:none !important;
                }
                .el-input__inner{
                    --el-input-inner-height:auto;
                }
            }
            &.is-focus{
                .el-input__wrapper{
                    padding: 0;
                    box-shadow:none !important;
                    
                }
            }
            &.is-disabled{
                .el-input__inner{
                    --el-text-color-placeholder:var(--van-field-placeholder-text-color);
                    --el-disabled-text-color:var(--van-field-placeholder-text-color);
                }
            }
        }
    }
    }
</style>