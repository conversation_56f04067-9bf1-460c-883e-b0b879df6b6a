<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        机会阶段
                    </div>
                </div>
                <progress-status  dataKey="dval" dataLabel="dname" :status_list="status_list" :active="info.basic.stage" :data="info.basic" :custom-change="customChange"  @update:active="handChange($event,info.basic)"></progress-status>
            </div>
        </div>
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        {{ info.basic.name }}
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
					<div class="item">
					    <div class="left">区域</div><div class="rigth">{{info.basic.areaName}}</div>
					</div>
					<div class="item">
					    <div class="left">地区</div><div class="rigth">{{info.basic.district}}</div>
					</div>
                    <div class="item">
                        <div class="left">客户 </div>
                        <div class="rigth">
                            <span @click="router.push({name: 'companyInfo',params: {id:info.basic.account_id}})" class="link">
                                {{info.basic.accName}}
                            </span>
                        </div>
                    </div>
                    <!-- <div class="item">
                        <div class="left">市场活动 </div>
                        <div class="rigth">
                            <span @click="router.push({name: 'marketingInfo',params: {id:info.basic.marketing_id}})" class="link">
                                {{info.basic.marketingName}}
                            </span>
                        </div>
                    </div> -->
                    <div class="item">
                        <div class="left">销售线索 </div>
                        <div class="rigth">
                            <span @click="router.push({name: 'leadInfo',params: {id:info.basic.sales_lead_id}})" class="link">
                                {{info.basic.salesLeadName}}
                            </span>
                        </div>
                    </div>
                    <div class="item">
                        <div class="left">来源</div><div class="rigth">{{info.basic.source}}</div>
                    </div>
                    <div class="item">
                        <div class="left">机会类型</div><div class="rigth">{{info.basic.type}}</div>
                    </div>
                    <div class="item">
                        <div class="left">金额</div><div class="rigth">{{info.basic.amount}}</div>
                    </div>
                    <div class="item">
                        <div class="left">是否样品</div><div class="rigth">{{info.basic.sample==1?'是':'否'}}</div>
                    </div>
                    <div class="item">
                        <div class="left">样品备注</div><div class="rigth">{{info.basic.sample_remark}}</div>
                    </div>
                    <div class="item">
                        <div class="left">报价是否发送</div>
                        <div class="rigth">{{info.basic.sent_quotation==1?'是':'否'}}</div>
                    </div>
                    <div class="item">
                        <div class="left">报价备注</div><div class="rigth">{{info.basic.quote_remark}}</div>
                    </div>
                    <div class="item">
                        <div class="left">合同金额</div>
                        <div class="rigth">{{info.basic.contract_amount}}</div>
                    </div>
                    <div class="item">
                        <div class="left">关闭日期</div>
                        <div class="rigth">{{info.basic.close_date}}</div>
                    </div>
                    <div class="item">
                        <div class="left">丢失原因</div>
                        <div class="rigth">{{info.basic.loss_cause}}</div>
                    </div>
                    <div class="item">
                        <div class="left">丢失原因描述</div>
                        <div class="rigth">{{info.basic.loss_desc}}</div>
                    </div>
                    <!-- <div class="item">
                        <div class="left">客户产品</div>
                        <div class="rigth">
                            <div class="tag_list">
                                <div v-for="(item,index) in info.basic.customerProductList" :key="index"  class="tag">
                                    <div class="tag_icon"><van-icon name="success" /></div> {{item}}
                                </div>
                            </div>
                        </div>
                    </div> -->
                </div>

                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.basic.created_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.basic.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.basic.update_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.basic.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>
		<u-modal v-model:show="dialogShow" confirmColor="#849EB2" @confirm="dialogConfirm"   @close="dialogShow = false" @cancel="dialogShow = false" title="丢失并关闭" show-cancel-button>
		    <div class="addInfo">
		        <div class="card">
					<u--form labelPosition="top" labelWidth="auto" ref="form1" >
						<u-form-item label="丢失原因" required prop="loss_cause">
							<zxz-uni-data-select  placeholder="请选择丢失原因"  v-model="popInfo.loss_cause" dataText="dval" dataValue="dname" :localdata="dict['opp_loss_cause']"></zxz-uni-data-select>
						</u-form-item>
						<u-form-item label="丢失原因描述" prop="loss_desc" >
							<u--textarea :count="!!popInfo.loss_desc" maxlength="150"  v-model="popInfo.loss_desc"  class="int"   placeholder="丢失原因描述"  />
						</u-form-item>
					</u--form>	
		        </div>
		    </div>
		</u-modal>
        <related-list :data="info.relatedList" :id="routerQuery.id" :baseInfo="info.basic" title="机会相关"></related-list>
        <footer-btn v-if="hasPermission('Opp','U')"  :cancelBtnShow="false" @onconfirm="router.push({name: 'oppEdit',params: {id:routerQuery.id}})" confirm_btn_text="编辑"></footer-btn>
		<floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import ProgressStatus from '@/components/ProgressStatus.vue';
import RelatedList from '@/components/RelatedList.vue';
import {useDictStore} from '@/store/modules/dictStore'
import {queryDetails,changeStage}  from  '@/api/opportunity'

    const prop =  defineProps(['id'])
    const router = useRouter();
    const util = inject("util");
	const dictStore = useDictStore()
    const routerQuery =  prop;
    const info = ref({
        basic:{},
        relatedList:[],
    	isEdit:0,
    });
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const status_list = ref([]);
    const init = async() =>{
        status_list.value = await dictStore.getDict('opp_stage');
        getInfo()
    }
    const dialogShow = ref(false)
    const dialogConfirm = ()=>{
        handChange(popInfo.value.stage,tempInfo,popInfo.value)
    }
    const popInfo = ref({});
    let tempInfo = null
    const customChange = async(status,info) =>{
        if(status == '丢失关闭'){
            const result = await dictStore.getDictBatch(['opp_loss_cause']);
            dialogShow.value = true;
            tempInfo = info
            popInfo.value.stage = status
            return false
        }else{
			
			return uni.showModal({
				title: '提示',
				content: '是否切换该状态？',
			}).then((res)=>{
				if (res.confirm) {
					handChange(status,info)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			});
        }
    }
    const handChange =(status,info,other = {})=> {
        changeStage({...{id:info.id,stage:status},...other}).then((res) => {
            info.stage = status
			 dialogShow.value = false;
            init()
        }).catch((err) => {
        });
    }
	onShow(() => {
		uni.$once("refresh", (data) => {
			init()
		})
	})
    onMounted(() => {
        init()
    })
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
</style>
<style lang="scss" scoped>
    .page{
        --van-cell-group-inset-padding:0;
        --card-border-radius:5px;
        --card-padding:0 10px;
        --van-divider-line-height:1px;
        --van-divider-margin:0;
        --van-field-label-width:auto;
        --el-table-border:0;
        .addInfo{
        padding: 10px;
        position: relative;
        border-radius: var(--card-border-radius);
        line-height: 1.4;
		width: 100%;
        .card{
            background: none;
            padding: var(--card-padding);
            position: relative;
            border-radius: var(--card-border-radius);
            line-height: 1.4;
            .card_head{
                display: flex;
                align-items: center;
                justify-content: center;
                // border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
                padding: 15px 0 10px 0;
                &_tit{
                    display: flex;
                    align-items: center;
                    font-size: 18px;
                    font-weight: 600;
                    color: #333333;
                    span{
                        font-weight: 700;
                        font-size: 16px;
                        color: #000000;
                        margin-right: 5px;
                    }
                }
            }
        }
        :deep(){
            .int{
                background: none !important;
                .van-field__value{
                    background: #fff;
                }
                // &.van-cell:after{
                //     content: none;
                // }
            }
        }
    }
        :deep(){
            .int{
                background: none !important;
                .van-field__value{
                    background: #fff;
                }
                // &.van-cell:after{
                //     content: none;
                // }
            }
        }
        :deep(){
            .int{
                padding: 0;
                padding-top: 20px;
                &:first-child{
                    padding-top: 0px;
                }
                &:last-child{
                    padding-bottom: 20px;
                }
                &.pt0{
                    padding-top: 0px;
                }
                .van-field__value{
                    box-shadow: 0px 1px 4px 0px rgba(0,0,0,0.05);
                    border-radius: 4px;
                    opacity: 1;
                    border: 1px solid #E2E6F6;
                    padding: 8px;
                }
                &.van-cell:after{
                    content: none;
                }
            }
        }
        :deep(.el-select){
        width: 100%;
        line-height: normal;
        --el-disabled-bg-color:none;
        --el-text-color-placeholder:var(--van-field-placeholder-text-color);
        .el-input{
            --el-border:0;
            --el-component-size:24px;
            .el-input__wrapper{
                padding: 0;
                box-shadow:none;
                &.is-focus{
                    box-shadow:none !important;
                }
                .el-input__inner{
                    --el-input-inner-height:auto;
                }
            }
            &.is-focus{
                .el-input__wrapper{
                    padding: 0;
                    box-shadow:none !important;
                    
                }
            }
            &.is-disabled{
                .el-input__inner{
                    --el-text-color-placeholder:var(--van-field-placeholder-text-color);
                    --el-disabled-text-color:var(--van-field-placeholder-text-color);
                }
            }
        }
    }
    }
</style>