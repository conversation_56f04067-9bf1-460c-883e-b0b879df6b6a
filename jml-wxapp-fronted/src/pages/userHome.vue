//
//                  ___====-_  _-====___
//            _--^^^#####//      \\#####^^^--_
//         _-^##########// (    ) \\##########^-_
//        -############//  |\^^/|  \\############-
//      _/############//   (@::@)   \############\_
//     /#############((     \\//     ))#############\
//    -###############\\    (oo)    //###############-
//   -#################\\  / VV \  //#################-
//  -###################\\/      \//###################-
// _#/|##########/\######(   /\   )######/\##########|\#_
// |/ |#/\#/\#/\/  \#/\##\  |  |  /##/\#/  \/\#/\#/\#| \|
// `  |/  V  V  `   V  \#\| |  | |/#/  V   '  V  V  \|  '
//    `   `  `      `   / | |  | | \   '      '  '   '
//                     (  | |  | |  )
//                    __\ | |  | | /__
//                   (vvv(VVV)(VVV)vvv)
//
//     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
//
//               神兽保佑            永无BUG


<template>
	<view class='container'>
		<u-sticky>
			<view class="tool  flex-end">
				<view class="tool_main">
					<view @click="showDrawer" class="menu_icon">
						<!-- <u-icon class="" name="list" color="#fff" size="28"></u-icon> -->
						<u-icon size="18" name="/static/icon/menu.png"></u-icon>
					</view>
					<!-- <view class="title">工作台</view> -->
					<!-- <view class="menu"></view> -->
				</view>
			</view>
		</u-sticky>
		<view class="menu">
			<view  class="menu_title">常用功能 </view>
			<view class="menu_list">
				<view v-for="(item,index) in meunlist" :key="index" :class="{'last': islastrow(index) }" @click="handMeun(item)" class="menu_item">
					<u-badge v-if="item.gmRead ==1" :isDot="true" :offset=[10,30] :absolute="true" ></u-badge>
					<u-icon labelSize="14" size="40" labelPos="bottom" space="10" :label="item.name"  :name="item.menu_icon" />
					<!-- <view class="menu_info_title">{{item.name}}</view> -->
				</view>
			</view>
		</view>
		<div class="calendar">
			<div class="calendar_list">
				<uni-calendar ref="calendar" :selected="calendarList" @change ="confirm" @monthSwitch="monthSwitch"  >
					<template #title>
						<view class="calendar_tool">拜访提醒</view>
					</template>
					<!-- <template #date-cell="{ data }">
					<p class="dateCell"  >
						{{data}}
					</p>
					</template> -->
				</uni-calendar>
				<u-divider></u-divider>
				<div class="notice" v-if="selectDays && noticelist">
					<div class="tit"> {{ util.dateUtils.timeFormat(selectDays,'mm月dd日') }} 周{{ util.dateUtils.timeFormat(selectDays,'W') }}</div>
					<div class="list">
						<template v-for="(item,index) in noticelist" :key="index">
							<!-- <div  class="item act" v-if="item.content=='休假'">
								<van-icon name="clock-o" /> <span>{{ item.time }}</span><span class="content">{{ item.content }}</span>
							</div> -->
							<div @click.stop="router.push({name: 'workreportInfo',params: {id:item.id}})" class="item link">
								<u-icon name="clock" /> <span>{{ item.time }}</span><span class="content">{{ item.content }}</span>
							</div>
							<u-divider></u-divider>
						</template>
					</div>
				</div>
			</div>
		</div>
		<div class="powered">
			<navigator url="test">
			Powered By Shanghai Changyun Network Ltd. Co
			</navigator>
		</div>
		<!-- <web-view @message="message"  src="http://yjn.zzjiedian.com/pages/index/index"></web-view> -->
		<select-map ref="map" title="一键签到" withUploader capture="camera"  v-model:show="mapShow" @cancel="mapShow = false" show-cancel-button @confirm="mapConfirm"></select-map>
		<u-popup :show="show" @close="close" mode="left" >
			<view class="leftmenu">
				<view class="leftmenu_tit">更多</view>
				<view class="leftmenu_box">
					<view @click="show = false" class="leftmenu_item">
						<u-icon size="18"  name="/static/icon/home_menu.png"></u-icon><text>首页</text>
						<view class="menu_left"><u-icon size="22"  name="/static/icon/menu_left.png"></u-icon></view>
					</view>
					<navigator url="user/changePwd" hover-class="navigator-hover">
						<view class="leftmenu_item">
							<u-icon size="18" name="/static/icon/pwd_menu.png"></u-icon>
							<text>更改密码</text>
							<view class="menu_left"><u-icon size="22"  name="/static/icon/menu_left.png"></u-icon></view>
						</view>
					</navigator>
					<!-- <view class="leftmenu_item"><u-icon size="18" name="/static/icon/out_menu.png"></u-icon><text>登出</text>
					<view class="menu_left"><u-icon size="22"  name="/static/icon/menu_left.png"></u-icon></view>
					</view> -->
				</view>
			</view>
		</u-popup>
		<drag-box v-if="hasPermission('SalesReport','C')" >
			<div @click="mapShow = true" class="check-in">
				<div><u-icon size="26" name="/static/icon/check-in.png" /></div>
				<div class="btn_txt">一键<br>签到</div>
			</div>
		</drag-box>
	</view>
</template>

<script setup lang="ts">
	interface menuItem {
	  menu_code: string,
	  menu_icon: string,
	  menu_id: string,
	  menu_url: string,
	  name: string,
	  parent_id: string,
	  // gmRead:[string,number,Boolean],
	}
	import {menu,queryReportNoticeByDay,queryDayListByDate}  from  '@/api/home';
	import DragBox from '@/components/DragBox.vue';
	import SelectMap from '@/components/SelectMap.vue';
	import dayjs from 'dayjs';
	import { isArray } from 'lodash';
	import {saveEdit}  from  '@/api/workreport'
	let router = useRouter()
	const show = ref(false);
	const showDrawer = ()=>{
		show.value = true
	};

	const close  = ()=>{
		show.value = false
	}
	const appStore = useAppStore();
    // const router = useRouter();
	const meunlist = ref<menuItem[]>([])

	const util = inject("util");
    //日历相关
    const selectDays = ref(new Date());
    const calendar = ref();	
    //有事项的日期
    const calendarList= ref([]);
    //选中天的事项列表
    const noticelist = ref([]);
	const confirm =(e)=>{
			// dayjs(e.fulldate);
			// debugger
		selectDays.value = new Date(e.fulldate);  //Date.parse(e.fulldate);
		console.log(e);
	}
	
	const lastMonth = ref(dayjs().format('YYYY-MM')) //这里是为了标记月份变化
	    // 获取某个月有事项的日期
	const getCalendarData = () =>{
		queryDayListByDate({date:lastMonth.value}).then(res=>{
			calendarList.value = res.data.map(i=> {
				return {date: i.day, info: '', data: { custom: '自定义信息', name: '自定义消息头'}}
			} )
		})
	}
	const monthSwitch = ({month,year})=>{
		lastMonth.value = `${year}-${month}`
	}
	// 获取一天的待办
	const getCalendarNotice = () =>{
		queryReportNoticeByDay({day:util.dateUtils.timeFormat(selectDays.value,'yyyy-mm-dd')}).then(res=>{
			//res.data.length>0 && (noticelist.value=res.data);
			noticelist.value=res.data
		})
	}
	
	watch(() => [selectDays.value, lastMonth.value], ([newDay,newMon],[oldDay,oldMon]) => {
		console.log(newDay,newMon);
		if(newDay!=oldDay){
			getCalendarNotice()
		}
		if(newMon!=oldMon){
			getCalendarData();
		}
	})
	
	onShow(()=>{
		console.log('onShow')
		getCalendarData();
		getCalendarNotice();
	})
	
    onMounted(() => {
        getMenu();
        // appStore.resetBatchExcludes(['workreport'])
    });
    const getMenu = ()=>{
        menu().then((res :any)=> {
            meunlist.value = res.data || []
        })
    };
    //点击菜单
    const handMeun =(item:any)=>{
        if(item.menu_url){
            router.push({name:item.menu_url})
        }
    }
    // 计算是否是最后一行的
    const islastrow = (index:number) => {
        let yu = meunlist.value.length % 3;
        let rows = meunlist.value.length / 3;
        if(yu == 0) return index >= 3*(rows-1)
        else  return index >= 3*rows
    }
	
	//一键签到
	const mapShow = ref(false)
	const map = ref();
	const mapConfirm = (mapInfo) => {
		let postData = {
			'salesReport.checkin_address': mapInfo.address || '',
			'salesReport.checkin_longitude':mapInfo.location.longitude || '',
			'salesReport.checkin_latitude':mapInfo.location.latitude || '',
			'salesReport.area_code':mapInfo.area_code || '',
			'salesReport.district': mapInfo.district || '',
		}
		if(isArray(mapInfo.files)) {
			mapInfo.files.forEach((item,index) => {
				postData[`salesReport.site_img${index+1}`] = item.url
			});
		}

		saveEdit(postData).then((res)=>{
			uni.showModal({
				title: '提示',
				content: '签到成功',
				showCancel:false,
				success: function (res) {
					map.value.reset()
					mapShow.value = false;
				}
			});
		})

	}
</script>
//#ifdef H5
<style lang="scss">
@import '@/styles/home.scss';
</style>
//#endif