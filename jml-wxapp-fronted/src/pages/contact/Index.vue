<template>
    <div class="page">
		
        <div class="head">
            <div class="search">
                <u-search
                    v-model="searchQuery.multiFiled"
                    placeholder="公司名称"
                    shape="square"
                    @change="util.debounce(onSearch)"
            		@custom="util.debounce(onSearch)"
                >
                </u-search>
            </div>
        </div>

       <div class="card_list" style="height: 100%;">
       		<scroll-view style="height:100%" scroll-y enable-flex  @scrolltolower="getDataPage()">
       			<uni-swipe-action>
					<div v-for="(item,index) in data.list" :key="index" class="van-swipe-cell" @click="router.push({name: 'contactInfo',params: {id: item.id}})">
						<uni-swipe-action-item >
							<template #left>
							</template>
							<div class="card">
								<div class="card_head">
									<div class="card_head_tit">
										{{ item.name }}
									</div>
									<div v-if="item.is_new =='1'" class="head_status">
										<u-image class="van-icon__image" width="40"  mode="widthFix" src="/static/icon/new.png" />
									</div>
								</div>
								<u-divider></u-divider>
								<div class="card_main">
									<div class="item">
										<div class="left">手机</div>
										<div class="rigth">{{item.mobile_phone}}</div>
									</div>
									<div class="item">
										<div class="left">邮箱</div>
										<div class="rigth">{{item.email}}</div>
									</div>
									<div class="item">
										<div class="left">是否在职</div>
										<div class="rigth">{{item.working}}</div>
									</div>
								</div>
								<u-divider></u-divider>
								<div class="card_footer">
									<div class="item">
										<div class="left">创建时间</div>
										<div class="rigth">{{item.created_time}} <span class="link">查看 <u-icon name="arrow-right" size="12" /></span> </div>
									</div>
									<div class="item">
										<div class="left">创建员工</div>
										<div class="rigth">{{item.createdStaffName}}</div>
									</div>
								</div>
							</div>
							<template #right>
								<div class="card_right">
									<div @click.stop="router.push({name: 'contactEdit',params: {id:item.id}})" class="card_right_btn">
										<u-icon size="20" name="/static/icon/edit.png" />
										<div class="txt">编辑</div>
									</div>
								</div>
							</template>
						</uni-swipe-action-item>	
					</div>
				</uni-swipe-action>	
				<div class="empty_box" v-if="!data.loading && (!data.list ||  data.list.length<=0)">
					<u-empty v-if="!data.list || data.list.length==0" text="未找到信息" :icon="`${config.static}images/empty/data.png`" ></u-empty>
					<div 
						v-if="hasPermission('Contact','C')"
						class="btn_add" @click="router.push('contactEdit')">
						<u-icon size="20" name="/static/icon/btn_add_icon.png" />  <span>新建</span>
					</div>
				</div>
			</scroll-view>
		</div>
        <floating-window>
            <div
				v-if="hasPermission('Contact','C')"
                @click="router.push({name:'contactEdit'})"
                class="item add_btn"
            >
                <u-icon size="20" name="/static/icon/add.png"></u-icon>
            </div>
        </floating-window>
    </div>
</template>
<script setup>
import FloatingWindow from '@/components/FloatingWindow.vue';
import { useDataList } from '@/vueUse/dataList'
import {queryPageData}  from  '@/api/contact'
    const router = useRouter();
	const util = inject("util");
    const {data} = useDataList()
	//触底加载
	const lowerBottom=()=>{
		console.log('lowerBottom');
	}
	// 下拉刷新
	const refresherTriggered = ref(false);
	const getFresh=()=> {
		console.log('refresh');
		refresherTriggered.value = 'restore'
		initEmpty()
	}
	const onPulling=(e)=> {
		console.log('onPulling');
		if (e.detail.deltaY < 0) return
	}
    const finishedText = computed(() => {
        return  data.list.length>0?'没有更多了':'';
    })
    const onSearch = () => {
        initEmpty()
    }
    const searchQuery = reactive({
        multiFiled:'',
        pageNo:data.pageNo,
        pageSize:data.pageSize,
    })

    const getDataPage = (pageNo) =>{
    	if (data.finished || data.loading)return;
    	searchQuery.pageNo = pageNo || data.pageNo;
    	data.loading = true;
        queryPageData(searchQuery).then((res) => {
    		if(refresherTriggered.value !== false) refresherTriggered.value = false
            data.list = [...data.list, ...res.data.list]
            data.totalRow = res.data.totalRow
            data.pageNo = data.pageNo + 1 ;
            data.loading = false;
            data.finished = res.data.lastPage;
        }).catch((err) => {
            data.finished = true;
        });
    }
    const init = () =>{
        getDataPage()
    }
    const initEmpty =() =>{
        data.initEmpty()
		getDataPage(1)
    }
	
    onMounted(() => {
		init()
    })

	onReachBottom(() => {
		console.log('onReachBottom')
		if(!data.finished) getDataPage();
	})
	onShow(() => {
		uni.$once("refresh", (data) => {

			uni.$off('refresh');

			initEmpty()

		})
	})
</script>
<style lang="scss" scoped>
    @import '@/styles/list.scss';
</style>