<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">联系人信息</div>
                </div>
				<u--form labelPosition="top" labelWidth="auto" ref="form1" >
					<u-form-item label="地区" prop="info.district" required>
						<zxz-uni-data-select clearable placeholder="请选择"  v-model="info.district" dataText="district" @change="areaChange" dataValue="district"  :localdata="userInfo.staffAreaList"></zxz-uni-data-select>
					</u-form-item>
					<u-form-item label="联系人" prop="info.name" required >
						<u--input  v-model="info.name"  label="联系人"  class="int"   placeholder="联系人"  />
					</u-form-item>
					<u-form-item label="公司" required prop="info.account_id">
						<load-selector v-model="info.account_id" filterable  load_url="/api/account/queryListByStaff" />
					</u-form-item>
					<u-form-item label="手机" prop="info.mobile_phone" required >
						<u--input  v-model="info.mobile_phone"  label="手机"  class="int"   placeholder="手机"  />
					</u-form-item>
					<u-form-item label="邮箱" prop="info.email"  >
						<u--input  v-model="info.email"  label="邮箱"  class="int"   placeholder="请输入邮箱"  />
					</u-form-item>
					<u-form-item label="微信" prop="info.wechat"  >
						<u--input  v-model="info.wechat"  label="邮箱"  class="int"   placeholder="请输入微信"  />
					</u-form-item>
					<u-form-item label="部门" prop="info.department" required >
						<u--input  v-model="info.department"  label="部门"  class="int"   placeholder="请输入部门"  />
					</u-form-item>
					<u-form-item label="职能" prop="info.function" required >
						<u--input  v-model="info.function"  label="职能"  class="int"   placeholder="请输入职能"  />
					</u-form-item>
					<u-form-item label="职位" prop="info.title"  >
						<u--input  v-model="info.title"  label="职位"  class="int"   placeholder="请输入职位"  />
					</u-form-item>
					<u-form-item label="是否在职" prop="info.working" required>
						<u-radio-group v-model="info.working" activeColor="#849EB2" iconPlacement="left">
						    <u-radio :customStyle="{marginRight: '16px'}"  label="是" name="1"></u-radio>
						    <u-radio  label="否" name="0"></u-radio>
						</u-radio-group>
					</u-form-item>
					<u-form-item label="是否为决策者" prop="info.decision_maker">
						<u-radio-group v-model="info.decision_maker" activeColor="#849EB2" iconPlacement="left">
						    <u-radio :customStyle="{marginRight: '16px'}"  label="是" name="1"></u-radio>
						    <u-radio  label="否" name="0"></u-radio>
						</u-radio-group>
					</u-form-item>
				</u--form>
            </div>
        </div>

        <footer-btn :cancelBtnShow="false" v-if="isEdit=='1'" confirm_btn_text="保存" @onconfirm="save()"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import LoadSelector from '@/components/LoadSelector.vue';
import {queryDetails,saveEdit}  from  '@/api/contact'
	const prop =  defineProps({
		id: String,
		isComponent: {
			type: Boolean,
			default: false,
		},
		accountId:{
			type: String,
			default: null,
		}
	})
	const emit = defineEmits(['confirm'])
	const router = useRouter();
	const route = useRoute();
	const routerQuery =  prop;
	// 相关字典
	const dictStore = useDictStore()

    const deleteFild = ['accName','createdStaffName','updateStaffName','salesLeadName','marketingArr','areaName'];
    const requiredFild = [
		{key:'district',message:'请选择地区',required: true,},
        {key:'name',message:'请填写联系人姓名',required: true,},
        {key:'account_id',message:'请选择所在公司',required: true,},
        {key:'mobile_phone',message:'请填写手机',required: true,},
        {key:'department',message:'请填写部门',required: true,},
        {key:'function',message:'请填写职能',required: true,},
        {key:'working',message:'请选择是否在职',required: true,},
    ]
    const save = () => {
        for(let index  in requiredFild){ 
            var item =  requiredFild[index]
            if(item.required && (!info.value[item.key] || info.value[item.key]=='')){
            	return uni.showToast({
            		title: item.message,
            		duration: 1500,
            		icon:'none'
            	});
            }
        };
        let postData = {}
        // info.value.marketing_id_list = info.value.marketingArr.join(',')
        for(var key in info.value){
            if(!deleteFild.includes(key))
                postData["contact." + key] = info.value[key];
        }
        saveEdit(postData).then((res)=>{
			uni.showModal({
				title: '提示',
				content: info.value.id ?'修改成功':'保存成功',
				showCancel:false,
				success: function (confirmres) {
					if (confirmres.confirm) {
						if(prop.isComponent)emit('confirm', res)
						else{
							uni.$emit("refresh", {refresh: true}); 
							router.back();
						}
					} else if (confirmres.cancel) {
						uni.$emit("refresh", {refresh: true}); 
						console.log('用户点击取消');
					}
				}
			});
        })
    }
	const isEdit=ref(routerQuery.id?0:1);
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data.basic
            info.value.working = (info.value.working===0 || info.value.working)? `${info.value.working}`:null
            info.value.decision_maker = (info.value.decision_maker || info.value.decision_maker===0) ? `${info.value.decision_maker}`:null;
			isEdit.value = res.data?.isEdit
            // info.value.marketingArr = info.value.marketing_id_list ? info.value.marketing_id_list.split(','):[];
        }).catch((err) => {
        });
    }
	const { userInfo } = useUserStore();
    const info = ref({
        account_id:routerQuery.account_id || prop.accountId,
		area_code:userInfo.staffAreaList.length==1 ? userInfo.staffAreaList[0].areaCode : null,
		district:userInfo.staffAreaList.length==1 ? userInfo.staffAreaList[0].district : null ,
    });
    const init = () =>{
        if(routerQuery.id){
            getInfo()
        }
    }
    const selectorChange = (data,info) =>{
        console.log('selector',data,info)
    }
   onLoad((option) => {
		routerQuery = prop.isComponent ? {} : option;
   })
   const areaChange =(value)=>{
		info.value.area_code = userInfo.staffAreaList.find(i=>i.district===value)?.areaCode;
   }
   onMounted(() => {
		info.value.account_id = routerQuery.account_id || prop.accountId;
		if(!prop.isComponent){
			const title = routerQuery.id?'编辑联系人':'新建联系人';
			uni.setNavigationBarTitle({
		   　　	title
			})
	   }
	   init()
   })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
</style>