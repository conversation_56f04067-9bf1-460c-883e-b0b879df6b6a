<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        {{ info.basic.name }}
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">公司名称 </div>
                        <div class="rigth">
                            <span @click="router.push({name: 'companyInfo',params: {id:info.basic.account_id}})" class="link">
                                {{info.basic.accName}}
                            </span>
                        </div>
                    </div>
					<div class="item">
					    <div class="left">区域</div><div class="rigth">{{info.basic.areaName}}</div>
					</div>
					<div class="item">
					    <div class="left">地区</div><div class="rigth">{{info.basic.district}}</div>
					</div>
                    <div class="item">
                        <div class="left">销售线索 </div>
                        <div class="rigth">
                            <span @click="router.push({name: 'leadInfo',params: {id:info.basic.sales_lead_id}})" class="link">
                                {{info.basic.salesLeadName}}
                            </span>
                        </div>
                    </div>
                    <div class="item">
                        <div class="left">手机</div><div class="rigth"><a :href="`tel:${ info.basic.mobile_phone }`">{{info.basic.mobile_phone}}</a> </div>
                    </div>
                    <div class="item">
                        <div class="left">邮箱</div><div class="rigth">{{info.basic.email}}</div>
                    </div>
                    <div class="item">
                        <div class="left">微信</div><div class="rigth">{{info.basic.wechat}}</div>
                    </div>
                    <div class="item">
                        <div class="left">部门</div><div class="rigth">{{info.basic.department}}</div>
                    </div>
                    <div class="item">
                        <div class="left">职能</div><div class="rigth">{{info.basic.function}}</div>
                    </div>
                    <div class="item">
                        <div class="left">是否在职</div>
                        <div class="rigth">{{info.basic.working==1?'是':'否'}}</div>
                    </div>
                    <div class="item">
                        <div class="left">职位</div><div class="rigth">{{info.basic.title}}</div>
                    </div>
                    <div class="item">
                        <div class="left">是否为决策者</div>
                        <div class="rigth">{{info.basic.decision_maker==1?'是':'否'}}</div>
                    </div>
                    <!-- <div class="item">
                        <div class="left">客户产品</div>
                        <div class="rigth">
                            <div class="tag_list">
                                <div v-for="(item,index) in info.basic.customerProductList" :key="index"  class="tag">
                                    <div class="tag_icon"><van-icon name="success" /></div> {{item}}
                                </div>
                            </div>
                        </div>
                    </div> -->
                </div>

                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.basic.created_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.basic.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.basic.update_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.basic.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>
		<related-list :data="info.relatedList" :id="routerQuery.id" :baseInfo="info.basic" title="联系人相关"></related-list>
        <footer-btn v-if="hasPermission('Contact','U') && info.isEdit==1"   :cancelBtnShow="false" @onconfirm="router.push({name: 'contactEdit',params: {id:routerQuery.id}})" confirm_btn_text="编辑"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import RelatedList from '@/components/RelatedList.vue';
import {queryDetails}  from  '@/api/contact'
    const routerQuery =  defineProps({
    	isComponent: {
    		type: Boolean,
    		default: false,
    	},
    	id:{
    		type: String,
    		default: null,
    	}
    })
	const router = useRouter();
	const route = useRoute();
	const util = inject("util");
    const info = ref({
        basic:{},
        relatedList:[],
		isEdit:0,
    });
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const init = () =>{
        getInfo()
    }
	onLoad((option) => {
		init()
	})
	onShow(() => {
		uni.$once("refresh", (data) => {
			debugger
			init()
		})
	});
    onMounted(() => {
    })
</script>
<style lang="scss">
	 @import '@/styles/detailed.scss';
</style>