<template>
    <div class="page pd100">
        <div class="card_info">
            <u--form labelPosition="top" labelWidth="auto" ref="form1">
                <u-form-item label="日期" prop="date">
                    <u--input type="date" v-model="popInfo.date" placeholder="请输入" />
                </u-form-item>
                <u-form-item label="班次" prop="shift">
                    <u--input type="text" v-model="popInfo.shift" placeholder="请输入" />
                </u-form-item>
                <u-form-item label="人数" prop="peopleNum">
                    <u--input type="number" v-model.number="popInfo.peopleNum" placeholder="请输入" />
                </u-form-item>
                <u-form-item label="良品数量" prop="goodQty" >
                    <u--input type="number" v-model.number="popInfo.goodQty"  placeholder="请输入"  @change="calculatePrintQty" />
                </u-form-item>
                <u-form-item label="不良品数量" prop="defectiveQty" >
                    <u--input type="number" v-model.number="popInfo.defectiveQty"  placeholder="请输入"  @change="calculatePrintQty" />
                </u-form-item>
                <u-form-item label="打印数量" prop="printQty" >
                    <u--input type="number" v-model.number="popInfo.printQty" readonly  placeholder="自动计算=良品数量 +不良品数量"  />
                </u-form-item>
                <u-form-item label="备注" prop="remark">
                    <u--input type="textarea" v-model="popInfo.remark" placeholder="请输入" />
                </u-form-item>
            </u--form>
        </div>
        <u-popup :show="showAddPop" :round="10" closeable mode="bottom" @close="showAddPop = false">
            <div class="addInfo_pop">
                <div class="card_from">
                    <u--form labelPosition="top" labelWidth="auto">
                        <u-form-item label="日期" prop="date">
                            <u--input type="date" v-model="popInfo.date" placeholder="请输入" />
                        </u-form-item>
                        <u-form-item label="班次" prop="shift">
                            <u--input type="text" v-model="popInfo.shift" placeholder="请输入" />
                        </u-form-item>
                        <u-form-item label="人数" prop="peopleNum">
                            <u--input type="number" v-model.number="popInfo.peopleNum" placeholder="请输入" />
                        </u-form-item>
                        <u-form-item label="良品数量" prop="goodQty" >
                            <u--input type="number" v-model.number="popInfo.goodQty"  placeholder="请输入"  @change="calculatePrintQty" />
                        </u-form-item>
                        <u-form-item label="不良品数量" prop="defectiveQty" >
                            <u--input type="number" v-model.number="popInfo.defectiveQty"  placeholder="请输入"  @change="calculatePrintQty" />
                        </u-form-item>
                        <u-form-item label="打印数量" prop="printQty" >
                            <u--input type="number" v-model.number="popInfo.printQty" readonly  placeholder="自动计算=良品数量 +不良品数量"  />
                        </u-form-item>
                        <u-form-item label="备注" prop="remark">
                            <u--input type="textarea" v-model="popInfo.remark" placeholder="请输入" />
                        </u-form-item>
                    </u--form>
                </div>
                <div class="btn_box">
                    <u-button type="primary" text="保存" @click="submitForm" />
                </div>
            </div>
        </u-popup>
    </div>
</template>

<script setup>
import { ref } from 'vue';

const showAddPop = ref(false);
const popInfo = ref({
    date: '',
    shift: '',
    peopleNum: 0,
    goodQty: 0,
    defectiveQty: 0,
    printQty: 0,
    remark: '',
});

const calculatePrintQty = () => {
    const goodQty = popInfo.value.goodQty || 0;
    const defectiveQty = popInfo.value.defectiveQty || 0;
    popInfo.value.printQty = goodQty + defectiveQty;
};

const submitForm = () => {
    console.log(popInfo.value);
    showAddPop.value = false;
};
</script>