<template>
    <div class="page pd100">
		<u--form labelPosition="top" labelWidth="auto" ref="form1" >
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">工作报告</div>
                </div>
				
				<u-form-item label="签到地址" prop="checkin_address"  @click="onCheckinAddress">
					<u--input
						v-model="info.checkin_address"
						placeholder="签到地址"
						readonly
						disabled
						disabledColor="#ffffff"
						border="none"
						style="width: 100%;pointer-events: none;"
					/>
					<template #right><u-icon name="map" @click="onCheckinAddress" ></u-icon></template>
				</u-form-item>
				
				<u-form-item label="签到时间" prop="checkin_time" >
					<u--input
						v-model="info.checkin_time"
						placeholder="签到时间"
						readonly
						disabled
						disabledColor="#ffffff"
						border="none"
						style="width: 100%;pointer-events: none;"
					/>
					<template #right><u-icon name="calendar"></u-icon></template>
				</u-form-item>
				<u-form-item label="公司"  prop="info.account_id">
					<load-selector
					 v-model="info.account_id" filterable  @change="accountChange"  load_url="/api/account/queryListByStaff">
					 <template #empty>
					     <div @click="addShowPop('account_id','accountSelect')"  class="sel_add">
					         <u-icon name="plus" label="新增" bold/> 
					     </div>
					 </template>
					 </load-selector>
				</u-form-item>
				<u-form-item label="地区" prop="info.district" required>
					<zxz-uni-data-select clearable placeholder="请选择"  v-model="info.district" dataText="district" @change="areaChange" dataValue="district"  :localdata="userInfo.staffAreaList"></zxz-uni-data-select>
				</u-form-item>
				<!-- <u-form-item label="机会"  prop="info.opportunity_id">
					<zxz-uni-data-select :disabled="addShow===true || !info.account_id || info.account_id==''" 
						clearable filterable placeholder="请选择"  
						v-model="info.opportunity_id" 
						dataText="id" dataValue="name"  
						:localdata="oppList">
						<template #empty>
						    <div @click="addShowPop('opportunity_id','opportunitySelect')"  class="sel_add">
						        <u-icon name="plus" label="新增" bold/> 
						    </div>
						</template>
					</zxz-uni-data-select>
				</u-form-item> -->
				
				<u-form-item label="联系人"  prop="info.contact_id">
					<zxz-uni-data-select :disabled="addShow===true || !info.account_id || info.account_id==''" 
						clearable filterable placeholder="请选择"  
						v-model="info.contact_id" 
						dataText="name" dataValue="id"  
						:localdata="contactList">
						<template #empty>
						    <div @click="addShowPop('contact_id','contactSelect')"  class="sel_add">
						        <u-icon name="plus" label="新增" bold/> 
						    </div>
						</template>
					</zxz-uni-data-select>
				</u-form-item>
				
				<u-form-item label="拜访类型" prop="info.type">
					<zxz-uni-data-select clearable filterable placeholder="请选择"  v-model="info.type" dataText="dval" dataValue="dname"  :localdata="dict['sales_report_type']"></zxz-uni-data-select>
				</u-form-item>
				<u-form-item label="拜访状态" prop="info.status">
					<zxz-uni-data-select clearable filterable placeholder="请选择"  v-model="info.status" dataText="dval" dataValue="dname"  :localdata="dict['sales_report_status']"></zxz-uni-data-select>
				</u-form-item>
            </div>
        </div>

        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">销售拜访</div>
                </div>
				<u-form-item label="到达时间" prop="checkin_time"  @click="onCalendarShow('arrival_time')">
					<u--input
						v-model="info.arrival_time"
						placeholder="到达时间"
						readonly
						disabled
						disabledColor="#ffffff"
						border="none"
						style="width: 100%;pointer-events: none;"
					/>
					<template #right><u-icon name="calendar" @click="onCalendarShow('arrival_time')"></u-icon></template>
				</u-form-item>
				
				<u-form-item label="拜访目的" prop="info.visit_purpose">
					<zxz-uni-data-select clearable filterable placeholder="请选择"  v-model="info.visit_purpose" dataText="dval" dataValue="dname"  :localdata="dict['sales_report_visit_purpose']"></zxz-uni-data-select>
				</u-form-item>
				<u-form-item label="当天工作内容" prop="info.work_of_the_day" >
					<u--textarea :count="!!info.work_of_the_day"   v-model="info.work_of_the_day"  class="int" maxlength="800"  placeholder="当天工作内容"  />
				</u-form-item>
				<u-form-item label="客户需求" prop="info.requirement" >
					<u--textarea :count="!!info.requirement"   v-model="info.requirement"  class="int" maxlength="800"  placeholder="客户需求"  />
				</u-form-item>
				<u-form-item label="当天工作总结" prop="info.summary" >
					<u--textarea :count="!!info.summary"   v-model="info.summary"  class="int" maxlength="800"  placeholder="当天工作总结"  />
				</u-form-item>
				<u-form-item label="明天工作计划" prop="info.schedule" >
					<u--textarea :count="!!info.schedule"    v-model="info.schedule"  class="int" maxlength="800"  placeholder="当天工作总结"  />
				</u-form-item>
				<u-form-item label="离开时间" prop="departure_time"  @click="onCalendarShow('departure_time')">
					<u--input
						v-model="info.departure_time"
						placeholder="离开时间"
						readonly
						disabled
						disabledColor="#ffffff"
						border="none"
						style="width: 100%;pointer-events: none;"
					/>
					<template #right><u-icon name="calendar" @click="onCalendarShow('departure_time')"></u-icon></template>
				</u-form-item>

            </div>
        </div>
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">销售拜访</div>
                </div>
				<u-form-item label="待跟进事宜" prop="info.follow_up" >
					<u--textarea :count="!!info.follow_up"   v-model="info.follow_up"  class="int" maxlength="800" placeholder="待跟进事宜"  />
				</u-form-item>
				<u-form-item label="签到照片" prop="info.files" required>
					<u-upload
					    :max-count="2"
					    multiple
					    @after-read="afterRead($event,'files')"
					    :deletable="!info.id"
						:disabled="info.id && info.files.length>0"
						:sizeType="['compressed']"
					    :readonly="info.id && info.files.length>0"
					    :before-delete="(file,detail)=>beforeDelete(file,detail)"
						@delete="beforeDelete($event,'files')"
						:fileList="info.files"
					>
					</u-upload>
				</u-form-item>
				<u-form-item label="工作照片" prop="info.work_img" >
					<u-upload
					    :max-count="3"
					    multiple
					    @after-read="afterRead($event,'work_img')"
					    :deletable="true"
						:sizeType="['compressed']"
					    :before-delete="(file,detail)=>beforeDelete(file,detail)"
						@delete="beforeDelete($event,'work_img')"
						:fileList="info.work_img"
					>
					</u-upload>
				</u-form-item>

            </div>
        </div>
		</u--form>
        <select-map title="报告签到" v-model:show="mapShow" @cancel="mapShow = false" withUploader :show-cancel-button="false" @confirm="mapConfirm"></select-map>
        <!-- <u-calendar v-model:show="calendarShow" :show-confirm="false" :min-date="new Date(2010, 0, 1)" color="#849EB2"  @confirm="onCalendarConfirm" /> -->
    
		<u-datetime-picker
			:show="calendarShow"
			v-model="currentTime"
			confirmColor="#849EB2"
			closeOnClickOverlay
			@confirm="onCalendarConfirm"
			@cancel="calendarShow = false"
			@close="calendarShow = false"
			mode="datetime"
		></u-datetime-picker>
		
        <u-popup v-model:show="addShow" round="10" mode="bottom" zIndex="998" closeOnClickOverlay @close="addShow=false">
            <div class="iframeAddInfo" :style="{ height: '80vh', position:'relative', }" > 
                <company v-if="addInfo.field=='account_id'" @confirm="iframeConfirm($event,'accountSelect')" isComponent></company>
                <contact :accountId="info.account_id" v-if="addInfo.field=='contact_id'" @confirm="iframeConfirm($event,'contact')" isComponent></contact>
                <!-- <opportunity :accountId="info.account_id" v-if="addInfo.field=='opportunity_id'" @confirm="iframeConfirm($event,'opportunity')" isComponent></opportunity> -->
            </div>
        </u-popup>
        <footer-btn :cancelBtnShow="false" v-if="isEdit=='1'" confirm_btn_text="保存" @onconfirm="save()"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import LoadSelector from '@/components/LoadSelector.vue';
import SelectMap from '@/components/SelectMap.vue';

import dayjs from "dayjs";
import {queryDetails,saveEdit}  from  '@/api/workreport'
import {queryListByAccountId} from '@/api/company'
import {upload,asyncUpload}  from  '@/api/common'
import { isArray, map } from 'lodash';
import company from '../company/Edit.vue';
// import opportunity from '../opportunity/Edit.vue';
import contact from '../contact/Edit.vue';
	const prop =  defineProps({
		id: String,
		isComponent: {
			type: Boolean,
			default: false,
		},
		accountId:{
			type: String,
			default: null,
		}
	})
	const routerQuery = prop;
    const appStore = useAppStore();
    const router = useRouter();
   const { userInfo } = useUserStore();

    // 相关字典
    const dictStore = useDictStore()
    const calendarShow = ref(false)
    const deleteFild = ['updateStaffName','createdStaffName','reiName','oppName',
        'contactName','accName','uploader','files','mobile_phone','lead_qty',
        'win_opp_qty','opp_qty','contact_qty','site_img','workuploader','work_img',
		'areaName'
    ];
    const save = () => {
        if(!info.value.files || info.value.files.length==0)  return uni.showToast({
			title: '请上传工作照片',
			duration: 1500,
			icon:'none'
		}); 
        let postData = {}
        if(isArray(info.value.files)) {
            info.value.files.forEach((item,index) => {
                info.value[`site_img${index+1}`] = item.url
            });
        }
        if(isArray(info.value.work_img)) {
            info.value.work_img.forEach((item,index) => {
                info.value[`work_img${index+1}`] = item.url
            });
        }
        for(var key in info.value){
            if(!deleteFild.includes(key))
            postData["salesReport." + key] = info.value[key];
        }
        saveEdit(postData).then((res)=>{
			
			uni.showModal({
				title: '提示',
				content: info.value.id ?'修改成功':'保存成功',
				showCancel:false,
				success: function (confirmres) {
					if (confirmres.confirm) {
						if(prop.isComponent)emit('confirm', res)
						else{
							uni.$emit("refresh", {refresh: true}); 
							router.back();
						}
					} else if (confirmres.cancel) {
						uni.$emit("refresh", {refresh: true}); 
						console.log('用户点击取消');
					}
				}
			});
        })
    }
    const info = ref({
        files:[],
        work_img:[],
    });
	const isEdit=ref(routerQuery.id?0:1);
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
			isEdit.value = res.data?.isEdit
            if(res.data.contactList) contactList.value = res.data.contactList
            if(res.data.oppList) oppList.value = res.data.oppList

            info.value = res.data.basic
            if(res.data.basic.site_img){
                info.value.files = res.data.basic.site_img.split(';').map(i=>{
					return{url:import.meta.env.VUE_APP_BASE_IMG + i}
				})
            }else{
                info.value.files = []
            }
            if(res.data.basic.work_img && res.data.basic.work_img!=''){
                info.value.work_img = res.data.basic.work_img.split(';').map(i=>{
					return{url:import.meta.env.VUE_APP_BASE_IMG + i}
				})
            }else{
                info.value.work_img = []
            }
        }).catch((err) => {
        });
    }
    const init = async() =>{
        await dictStore.getDictBatch(['sales_report_type','sales_report_visit_purpose','sales_report_status']);
        if(routerQuery.id){
            getInfo()
        }
    }
    const calendarType = ref(null)
    // debugger
    // const currentDate = ref([`${dayjs().format('YYYY')}`, `${dayjs().format('MM')}`,`${dayjs().format('DD')}`]);
    const currentTime = ref(Date.now());// ref([`${dayjs().format('HH')}`,`${dayjs().format('mm')}`]);
    const onCalendarShow  = (type,check = false) =>{
		debugger
        // if(check && routerQuery.id && info.checkin_address && (info.value[calendarType.value] && info.value[calendarType.value]!='')) return false
        calendarType.value = type
        calendarShow.value = true;
    }
    const onCalendarConfirm = ({mode,value}) => {
		debugger
        info.value[calendarType.value] = `${dayjs(value).format('YYYY-MM-DD HH:mm')}`;
        // info.value[calendarType.value] = `${currentDate.value.join('-')} ${currentTime.value.join(':')}`
        calendarShow.value = false
    };

    const maxCount = 3

    const afterRead = async(event,type) =>{
		let files = [].concat(event.file)
		if(!info.value[type]) info.value[type] = [];
		let fileListLen =  info.value[type].length
		files.map((item) => {
			info.value[type].push({
				...item,
				status: 'uploading',
				message: '上传中'
			})
		})
		for (let i = 0; i < files.length; i++) {
			try {
				const {data} = await asyncUpload(files[i].url)
				let item = info.value[type][fileListLen];
				info.value[type].splice(fileListLen, 1, {
				  ...item,
				  status: 'success',
				  message: '',
				  url: data.url,
				});
				fileListLen++;
			} catch (e) {
				console.log(e)
				uni.showToast({
					title: "图片上传失败",
					icon: "none"
				})
			}
		}
    }

    const beforeDelete = ({file,index},type) =>{
		uni.showModal({
			title: '提示',
			content: '是否删除该附件？',
			success: function (res) {
				if (res.confirm) {
					info.value[type].splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
    }

    const contactList =ref([])
    const oppList =ref([])
    const accountChange = (id,remove = true) =>{
		if(!id) return;
        if(remove){
            info.value.opportunity_id = null;
            info.value.contact_id = null;
        }
        queryListByAccountId(id).then((res) => {
            contactList.value = res.data.contactList
            oppList.value = res.data.oppList
        })
    }
    const mapShow = ref(routerQuery.id?false:true)
    const mapConfirm = (mapInfo) => {
        console.log('mapInfo',mapInfo);
        info.value.checkin_address =  mapInfo.address
        info.value.checkin_longitude = mapInfo.location.longitude
        info.value.checkin_latitude = mapInfo.location.latitude
		info.value.area_code = mapInfo.area_code
		info.value.district = mapInfo.district
        info.value.checkin_time = `${dayjs().format('YYYY-MM-DD HH:mm')}`;
        info.value.files = mapInfo.files;
		mapShow.value = false
        // if(isArray(mapInfo.files)) {
        //     mapInfo.files.forEach((item,index) => {
        //         postData[`salesReport.site_img${index+1}`] = item
        //     });
        // }
    }
    const addShow = ref(false)
    // watch(() => addShow.value,(newValue, oldValue) => {
    //     let appDom = document.querySelector('#app')
    //     if(newValue){
    //         appDom.classList.add('select')
    //     }else{
    //         appDom.classList.remove('select')
    //     }
    // });
    const addInfo = reactive({
        field:'account_id',
    })
    const _this = getCurrentInstance()
    const addShowPop =  (field,refdom) =>{
		debugger
        // _this?.refs[refdom]?.blur()
        addInfo.field = field
        addShow.value = true
    }
    const iframeConfirm = (res,refdom) =>{
		debugger
        refdom=='accountSelect'? _this?.refs[refdom]?.getDataPage() : accountChange(info.value.account_id,false)
        info.value[addInfo.field] = res.data
        addShow.value = false
    }
    const onCheckinAddress = ()=>{
        if(info.value.checkin_address=='' || !info.value.checkin_address) mapShow.value = true
    }
	const areaChange =(value)=>{
		info.value.area_code = userInfo.staffAreaList.find(i=>i.district===value)?.areaCode;
	}
    onMounted(() => {
        init()
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
</style>