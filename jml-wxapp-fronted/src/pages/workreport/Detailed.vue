<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div v-if="info.basic.contactName && info.basic.contactName.length>0" class="card_head_name">
                        {{ info.basic.contactName.substr(0,1) }}
                    </div>
                    <div class="card_head_tit">
                        <div class="txt1">{{ info.basic.contactName }}</div>
                        <div class="txt2">{{ info.basic.mobile_phone }}</div>
                    </div>
                </div>
                <div class="card_head">
                    <div class="card_head_tit">
                        工作报告
                    </div>
                </div>
				<canvas :style="'height:'+ canvasInfo.height +'px;width:'+ canvasInfo.width +'px'" canvas-id="html2Img" ></canvas>
                <u-divider></u-divider>
                <div class="card_main">
					<div class="item">
					    <div class="left">区域</div><div class="rigth">{{info.basic.areaName}}</div>
					</div>
					<div class="item">
					    <div class="left">地区</div><div class="rigth">{{info.basic.district}}</div>
					</div>
                    <div class="item">
                        <div class="left">拜访公司 </div>
                        <div class="rigth">
                            <span @click="router.push({name: 'companyInfo',params: {id:info.basic.account_id}})" class="link">
                                {{info.basic.accName}}
                            </span>
                        </div>
                    </div>
					<div class="item">
					    <div class="left">联系人 </div>
					    <div class="rigth">
					        <span @click="router.push({name: 'contactInfo',params: {id:info.basic.contact_id}})" class="link">
					            {{info.basic.contactName}}
					        </span>
					    </div>
					</div>
                    <!-- <div class="item">
                        <div class="left">相关机会 </div>
                        <div class="rigth">
                            <span @click="router.push({name: 'oppInfo',params: {id:info.basic.opportunity_id}})" class="link">
                                {{info.basic.oppName}}
                            </span>
                        </div>
                    </div> -->
                    <div class="item">
                        <div class="left">类型</div><div class="rigth">{{info.basic.type}}</div>
                    </div>
                </div>

                <div class="card_head_tit2 mt15">
                    销售拜访
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">签到地址</div>
                        <div class="rigth">
                            <span v-if="info.basic.latitude!='' && info.basic.longitude!=''" @click="openLocation(info.basic.checkin_latitude,info.basic.checkin_longitude,'签到地址',info.basic.checkin_address)" class="link">
                                {{info.basic.checkin_address}}
                            </span>
                            <span v-else>{{info.basic.checkin_address}}</span>
                        </div>
                    </div>
                    <div class="item">
                        <div class="left">签到时间</div><div class="rigth">{{info.basic.checkin_time}}</div>
                    </div>
                    <div class="item">
                        <div class="left">拜访状态</div><div class="rigth">{{info.basic.status}}</div>
                    </div>
                    <div class="item">
                        <div class="left">拜访目的</div><div class="rigth">{{info.basic.visit_purpose}}</div>
                    </div>
                    <!-- <div class="item">
                        <div class="left">拜访内容</div><div class="rigth">{{info.basic.visit_content}}</div>
                    </div> -->
                    <div class="item">
                        <div class="left">当天工作内容</div><div class="rigth">{{info.basic.work_of_the_day}}</div>
                    </div>
                    <div class="item">
                        <div class="left">客户需求</div><div class="rigth">{{info.basic.requirement}}</div>
                    </div>
                    <div class="item">
                        <div class="left">当天工作总结</div><div class="rigth">{{info.basic.summary}}</div>
                    </div>
                    <div class="item">
                        <div class="left">明天工作计划</div><div class="rigth">{{info.basic.schedule}}</div>
                    </div>
                    <div class="item">
                        <div class="left">到达时间</div><div class="rigth">{{info.basic.arrival_time}}</div>
                    </div>
                    <div class="item">
                        <div class="left">离开时间</div><div class="rigth">{{info.basic.departure_time}}</div>
                    </div>
                </div>

                <div class="card_head_tit2 mt15">
                    工作记录
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">待跟进事宜</div><div class="rigth">{{info.basic.follow_up}}</div>
                    </div>
                    <div class="item">
                        <div class="left">费用报销 </div>
                        <div class="rigth">
                            <span @click="router.push({name: 'costInfo',params: {id:info.basic.reimbursement_id}})" class="link">
                                {{info.basic.reiName}}
                            </span>
                        </div>
                    </div>
                    <div class="item">
                        <div class="left">签到照片</div><div class="rigth">
                            <image v-for="(item,index) in info.files" :key="index"   mode="widthFix"  :src="imgBase+item" />
                            <!-- {{info.basic.site_img}} -->
                        </div>
                    </div>
                    <div class="item">
                        <div class="left">工作照片</div><div class="rigth">
                            <image v-for="(item,index) in info.work_img" :key="index"   mode="widthFix"  :src="imgBase+item" />
                            <!-- {{info.basic.site_img}} -->
                        </div>
                    </div>
                </div>

                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.basic.created_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.basic.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.basic.update_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.basic.updateStaffName }}</div>
                    </div>
                </div>
            </div>

            <!-- 评论相关开始 -->
            
            <div  v-if="info.basic.submit_status == '1'" class="comments">

                <uni-collapse v-model="activeNames">
                <uni-collapse-item name="1">
                    <template #title>
                        <div class="item flex title">
                            <div class="left ">共{{data.totalRow}}条评论</div>
                            <div @click="comment_slide" style="margin-left: auto;" class="rigth">
                                {{ show_comments_box?'收起全部':'展开全部' }}
                            </div>
                        </div>
                    </template>
					<!-- <div slot="title" class="item flex title">
					    <div class="left ">共{{data.totalRow}}条评论</div>
					    <div @click="comment_slide" style="margin-left: auto;" class="rigth">
					        {{ show_comments_box?'收起全部':'展开全部' }}
					    </div>
					</div> -->
                    <div class="comments_box">
                        <u-list
                            v-model:loading="data.loading"
                            :finished="data.finished"
                            :finished-text="finishedText"
                            offset="50"
                            @load="getDataPage"
                        >

                        <div class="comments_item" v-for="(item,index) in data.list" :key="index">
                            <div class="comments_item_info">
                                <div class="avatar">
                                    <u-image
                                        class="avatar_img"
                                        width="34px"
                                        height="34px"
                                        radius="4px"
                                        fit="cover"
                                        position="left"
                                        :src="item.avatar"
                                        />
                                </div>
                                <div class="info">
                                    <div class="name">{{item.name}}</div>
                                    <div class="content" v-html="item.content">
                                    </div>
                                    <div class="time">
                                        {{ item.created_time}} <text @click="reply(item,item.id)">回复</text>
                                    </div>
                                    <div v-if="item.subComments" class="comments_item_sub">
                                        <div v-for="(subItem,subIndex) in item.subComments" :key="subIndex"  class="comments_item">
                                            <div v-if="subIndex==0" class="comments_item_info">
                                                <div class="avatar">
                                                    <u-image
                                                        class="avatar_img"
                                                        width="34px"
                                                        height="34px"
                                                        radius="4px"
                                                        fit="cover"
                                                        position="left"
                                                        :src="subItem.avatar"
                                                    />
                                                </div>
                                                <div class="info">
                                                    <div class="name">{{subItem.name}}</div>
                                                    <div class="content" v-html="subItem.content"></div>
                                                    <div class="time">
                                                        {{ subItem.created_time}} <text @click="reply(subItem,item.id)">回复</text>
                                                    </div>
                                                    <div v-if="!item.openSum && item.subCount>1" @click="item.openSum = true" class="suball">
                                                        展开{{ item.subCount -1 }}条回复
                                                    </div>
                                                </div>
                                            </div>
                                            <div v-if="subIndex>0 && item.openSum" class="comments_item_info">
                                                <div class="avatar">
                                                    <u-image
                                                        class="avatar_img"
                                                        width="34px"
                                                        height="34px"
                                                        radius="4px"
                                                        fit="cover"
                                                        position="left"
                                                        :src="subItem.avatar"
                                                    />
                                                </div>
                                                <div class="info">
                                                    <div class="name">{{subItem.name}}</div>
                                                    <div class="content" v-html="subItem.content"></div>
                                                    <div class="time">
                                                        {{ subItem.created_time}} <text @click="reply(subItem,item.id)">回复</text>
                                                    </div>
                                                    <div v-if="subIndex==item.subCount-1 && item.openSum" @click="item.openSum = false" class="suball">
                                                        收起{{ item.subCount - 1 }}条回复
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>

                        </u-list>

                        <div class="empty_box" v-if="!data.loading && (!data.list ||  data.list.length<=0)">
                            <u-empty  :icon="`${config.static}images/empty/data.png`"  description="暂时没有评论" />
                        </div>

                    </div>
                </uni-collapse-item>
            </uni-collapse>

                <u-popup
                    v-model:show="comments_input"
                    mode="bottom"
                    closeOnClickOverlay
                    @close="showUserPop = false"
                    :style="{ maxHeight: '50%' }"
                    :overlay-style="{background: 'transparent'}"
                >
                    <div class="comments_pop">
						<editor
							id="editor"
							show-img-size show-img-toolbar
							class="comments_textarea"
						    :placeholder="comments_info.placeholder || '请输入评论内容' "
						    ref="comments_textarea"
							:read-only="false"
							@ready="onEditorReady"
							@blur="comments_textareaBlur"
							@input="comments_textareaInput"
							@focus="comments_textareaFocus"
						>
						</editor>
                        <div @click="at_click" class="at_btn">@</div>
                        <div @click="sendReply" class="comments_submit">提交</div>
                    </div>
                </u-popup>
            </div>
            <!-- end评论相关  -->
        </div>
        <!-- @待选列表 -->
        <u-popup
            v-model:show="showUserPop"
            mode="bottom"
            closeable
			closeOnClickOverlay
			@close="showUserPop = false"
            round="10"
            :style="{ minHeight: '40%',maxHeight: '90%',overflow:'hidden' }"
        >
        <div class="user">
            <div class="user_tit">选择要通知的人</div>
            <view style="padding: 10px;">
				<u-search v-model="userQuery.staffName" @update:model-value="util.debounce(getUserList)" :showAction="false" shape="square" placeholder="请输入姓名" />
			</view>
            <div class="user_list" >
                <!-- <u-index-bar> -->
                    <div v-for="(item,index) in userList" :key="index" @click="at_user(item)" class="user_list_item">
                        <div class="avatar">
                            <u-image
                                class="avatar_img"
                                width="34px"
                                height="34px"
                                radius="4px"
                                fit="cover"
                                position="left"
                                :src="item.avatar"
                                />
                        </div>
                        <div class="info">
                            <div class="name">{{ item.name }}</div>
                        </div>
                    </div>
                <!-- </u-index-bar> -->
            </div>
        </div>
        </u-popup>
        <!-- end@待选列表 -->

        <footer-btn v-if="info.basic.submit_status!='1' && hasPermission('SalesReport','U') && info.isEdit=='1'"  :cancelBtnShow="false" @onconfirm="router.push({name: 'workreportEdit',params: {id:routerQuery.id}})" confirm_btn_text="编辑"></footer-btn>
        <floating-window>
            <div v-if="info.basic.submit_status == '1'"  @click="reply()" class="item">
                <u-icon size="26" name="/static/icon/reply.png" />
                <div class="lable">写评论</div>
            </div>
        </floating-window>
		
    </div>
</template>
<script setup>
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {queryDetails,queryCommentsPageBy,saveComment,queryByMentionedUser}  from  '@/api/workreport'
	const prop =  defineProps(['id'])
	const router = useRouter();
	const util = inject("util");
    const routerQuery =  prop;
    const info = ref({
        basic:{},
        relatedList:[],
		isEdit:0,
    });
	const canvasInfo = reactive({
		canvasHidden:true,
		width:200,
		height:30,
	})
    const getInfo = () =>{
        queryDetails(routerQuery.id,routerQuery.readFlag).then((res) => {
            info.value = res.data
            if(res.data.basic.site_img){
                info.value.files = res.data.basic.site_img.split(';')
            }else{
                info.value.files = []
            }
            if(res.data.basic.work_img && res.data.basic.work_img!=''){
                info.value.work_img = res.data.basic.work_img.split(';')
            }else{
                info.value.work_img = []
            }
        }).catch((err) => {
        });
    }
    const openLocation = (latitude,longitude,name,address) =>{
		uni.openLocation({
			latitude: parseFloat(latitude),
			longitude: parseFloat(longitude),
			name: name,
			address: address,
			scale: 15,
			success: function () {
				console.log('success');
			},
			fail:function (e) {
				debugger
				console.log('fail');
			},
		});
    }
    const init = () =>{
        getInfo()
        getDataPage()
    }
    onMounted(() => {
        init()
    })

    const activeNames = ref([]);
    const show_comments_box = ref(false);
    const showUserPop = ref(false);
    const comments_input = ref(false);
    const comments_info = ref({
        message:'',
        placeholder:'请输入评论内容',
        cursorPosition:0,
        lastEditRange:null,
    })
    const parent_reply_info = ref(null)// 被评论的记录
    const reply_info = reactive({
        content:'',
        sales_report_id:routerQuery.id,
        mentioned_staff_userids:null,
        parent_comment_id:null,
        top_comment_id:null,
    })
    const comments_textarea = ref() //评论填写 dom
    const userList = ref([])
    const userQuery = reactive({
        staffName:'',
    })
	const editorCtx  = ref(null);
	const onEditorReady=()=>{
		// #ifdef APP-PLUS || MP-WEIXIN || H5
		setEditorCtx()
		// #endif
	}
	const setEditorCtx = (callfun)=>{
		if(!editorCtx.value){
			uni.createSelectorQuery().select('#editor').context((res) => {
				editorCtx.value = res.context
				console.log('editorCtx',editorCtx.value)
				typeof callfun=== 'function' && callfun()
			}).exec()
		}else typeof callfun=== 'function' && callfun()
	}
	const onStatusChange =()=>{
		
	}
     // 弹出评论输入框
    const comment_slide = ()=>{
        show_comments_box.value =! show_comments_box.value;
        if(show_comments_box.value && data.list.length===0) getDataPage()
    }

    // 评论输入框 Blur事件
    const comments_textareaBlur = ({detail})=>{
		console.log('b',detail)
        // comments_info.value.cursorPosition = getPosition(element.target)
        // comments_info.value.lastEditRange = window.getSelection().getRangeAt(0)
        // comments_info.value.message = element.target.innerHTML
        // console.log('comments_info',comments_info.value)
    }
	const comments_textareaFocus =  ({detail})=>{
		console.log('f',detail)
        // comments_info.value.cursorPosition = getPosition(element.target)
        // comments_info.value.lastEditRange = window.getSelection().getRangeAt(0)
        // comments_info.value.message = element.target.innerHTML
        // console.log('comments_info',comments_info.value)
    }
	
	const comments_textareaInput = ({detail})=>{
		console.log('i',detail)
		const {html} = detail;
		// if(at_userids.value.length>0){
		// 	if(html=='<p><br></p>'){
		// 		//说明删完了
		// 		at_userids.value = [];
		// 	}else{
		// 		if(comments_info.value.message!='' && comments_info.value.message.length>html.length){
		// 			//和新内容比较下是不是删了 @用户
		// 			let arraySpan = comments_info.value.message.match(/<span[^>]*>(.*?)<\/span>/g);
		// 			console.log(comments_info.value.message,html,arraySpan)
		// 		}
		// 	}
		// }
		comments_info.value.message = html
	}
    

    // 弹出待@用户列表
    const at_click = async () =>{
        if(userList.value.length ==0) getUserList()
		setEditorCtx(()=>{
			showUserPop.value = true
		})
    }

    //评论填写pop 展开
    const reply =(info,top_comment_id) =>{
		comments_input.value = true
        if(info){
			//被回复的人添加 @效果
            parent_reply_info.value = info;
            reply_info.parent_comment_id = info.id
            reply_info.top_comment_id = top_comment_id
            comments_info.value.placeholder = `@${info.name}`;
            // comments_info.value.html = `<span contenteditable="false" mentioned_staff_userids="${info.userid}">@${info.name}</span><\n>`
			nextTick(()=>{
				setEditorCtx(()=>{
					editorCtx.value.insertImage({
						src:info.nameImg,
						alt:info.name,
						data:{id:info.userid},
						nowrap:true,
						// width:0,
						height:16
					})
				})
			})
        }else{
            parent_reply_info.value = null;
            reply_info.parent_comment_id = null
            comments_info.value.placeholder = null;
            reply_info.top_comment_id = null
        }
    }
	const at_userids = ref([]);
    //@ 用户 到填写区域
    const at_user = async (userInfo) =>{
		// let a = await toimg(`@ ${userInfo.name}`);
		editorCtx.value.insertImage({
			src:userInfo.nameImg,
			alt:userInfo.name,
			data:{id:userInfo.id},
			nowrap:true,
			// width:0,
			height:16
		})
		showUserPop.value = false
		// debugger
		// editorCtx.value.getContents({
		// 	success:(data)=>{
		// 		let _html = data.html;
		// 		// 去掉文本中默认的标签
		// 		_html = _html.replace('<p><br></p>','');
		// 		// 接去掉文本中最后面的</p>标签
		// 		_html = _html.substring(0,_html.length-4);
		// 		// 重新拼接文本,并在末尾添加</p>标签
		// 		let html = `${_html}<span style="color: #3a8afb;"> @${userInfo.name}<i style="font-style: normal;color: #000;"> </i></p>`;
		// 		// 给富文本设置内容
		// 		editorCtx.value.setContents({html});
		// 		at_userids.value.push(userInfo)
		// 		showUserPop.value = false
				
		// 		editorCtx.value.getContents({
		// 			success:(data2)=>{
		// 				let _html2 = data2.html;
		// 				debugger
		// 				comments_info.value.message = _html2
		// 			},
		// 			fail:(res)=>{
		// 				debugger
		// 			},
		// 		})
				
		// 		// debugger
		// 	},
		// 	fail:(res)=>{
		// 		debugger
		// 	},
		// })
    }

    const {data} = useDataList()
    // 评论分页查询参数
    const searchQuery = reactive({
        salesReportId:routerQuery.id,
        pageNo:data.pageNo,
        pageSize:data.pageSize,
    })
    const finishedText = computed(() => {
        return  data.list.length>0?'没有更多了':'';
    })

    // 评论分页查询接口
    const getDataPage = () =>{
        if(data.finished) return;
        searchQuery.pageNo = data.pageNo
        queryCommentsPageBy(searchQuery).then((res) => {
            data.list = [...data.list, ...res.data.list]
            data.totalRow = res.data.totalRow
            data.pageNo = data.pageNo + 1 ;
            data.loading = false;
            data.finished = res.data.lastPage;
        }).catch((err) => {
            data.finished = true;
        });
    }
    // 待@用户查询接口
    const getUserList = () =>{
        queryByMentionedUser({
            staffName:userQuery.staffName,
            ownerStaffId:info.value.basic.created_staff_id
        }).then((res) => {
            userList.value = res.data
        }).catch((err) => {
        });
    }

    // 提交评论
    const sendReply = () =>{
        let arraySpan = comments_info.value.message.match(/<img[^>]+data-custom=['"]([^'"]+)['"][^>]*>/g);
        let results = [];
        if (arraySpan && arraySpan.length > 0) {
            results = arraySpan.map(item => {
                let array = item.match(/<img[^>]+id=([^'"]+)[^>]*>/);
                return array[1]
            });
            reply_info.mentioned_staff_userids = results.join(',');
        }
        reply_info.content = comments_info.value.message
        let postData = {}
        for(var key in reply_info){
            postData["comment." + key] = reply_info[key];
        }
        saveComment(postData).then((res) => {
            // if(parent_reply_info.value){
            //     if(parent_reply_info.value.subCount){
            //         parent_reply_info.value.subCount ++;
            //         parent_reply_info.value.subComments.push(res.data)
            //     }else{
            //         parent_reply_info.value.subCount = 1;
            //         parent_reply_info.value.subComments = [res.data]
            //     }
            // }else{
            //     data.totalRow ++;
            //     data.list.push(res.data)
            // }
            comments_input.value = false
            activeNames.value = ['1']
            data.initEmpty()
            // if(!activeNames.value.includes('1'))
            getDataPage()
            comments_textarea.value.innerHTML = "";
            omments_info.value = {
                message:'',
                placeholder:'请输入评论内容',
                cursorPosition:0,
                lastEditRange:null,
            }
        }).catch((err) => {
        });
    }
	const toimg = (text)=>{
		// debugger
		return new Promise((resolve, reject) => {
			
			let that = this;
			const ctx = uni.createCanvasContext('html2Img');
			ctx.font = '1px'
			const metrics = ctx.measureText(text)
			canvasInfo.width = Number(metrics.width  * 1.75 );
			canvasInfo.height = 14;
				// 填充文字
			ctx.setFillStyle('#3a8afb');
			ctx.setFontSize(14);
			ctx.setTextAlign('center')
			ctx.fillText(text, canvasInfo.width/2, 12);
			// ctx.fillText('标题内容', 1, 30);

			ctx.draw(false, () => {
				uni.canvasToTempFilePath({
					canvasId: 'html2Img',
					quality: 1,
					width: canvasInfo.width,
					height: canvasInfo.height,
					destWidth: canvasInfo.width,
					destHeight: canvasInfo.height,
					success: (res) => {
						// debugger
						let base64 = res.tempFilePath;//'data:image/jpeg;base64,' + uni.getFileSystemManager().readFileSync(res.tempFilePath, 'base64');
						resolve(base64)
					},
					fail:(err)=>{
						reject(err)
						console.log(err)
					}
				}, this)
			});
		});
	}
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
    [contenteditable]:focus{outline: none;}
    .v-enter-active,
    .v-leave-active {
        transition: opacity 0.5s ease;
    }

    .v-enter-from,
    .v-leave-to {
        opacity: 0;
    }
    
    .comments_box{
        border-radius: 6px;
        background: #fff;
        padding: 20px;
        .comments_item_info{
            margin: 5px auto 10px auto;
            display: flex;
        }
        .comments_item_sub{
            margin-top: 20px;
        }
        .comments_item{
            margin: 5px auto 0px auto;
			width: 100%;
            span{
                color: #849EB2;
            }
            // display: flex;
            .avatar{
                .avatar_img{
                    border-radius: 4px;
                }
            }
            .info{
                margin-left: 10px;
                font-size: 14px;
                font-family: Roboto-Regular, Roboto;
                font-weight: 400;
                color: #333333;
                line-height: 22px;
                .name{
                    font-size: 16px;
                    font-family: PingFang SC-Semibold, PingFang SC;
                    font-weight: 600;
                    color: #999999;
                    line-height: 22px;
                }
                .content{
                    margin-top: 5px;
                }
                .time{
                    font-size: 12px;
                    color: #999999;
                    line-height: 14px;
                    margin-top: 5px;
                    text{
                        color: #849EB2;
                        margin-left: 5px;
                    }
                }
            }
            .suball{
                margin-top: 5px;
                color: #849EB2;
                height: 16px;
                font-size: 14px;
            }
        }
    }
    .comments_pop{
        width: 100%;
        padding: 10px;
        position: fixed;
        bottom: 0;
        left: 0;
        background: #CFD1D5;
        z-index: 1500;
        // position: relative;
        display: flex;
        align-items: flex-end;
        padding-bottom: calc(20px + var(--ios-safe-bottom-low));
        padding-bottom: calc(20px + var(--ios-safe-bottom-higt));
        .comments_textarea{
            border-radius: 6px;
            background: #fff;
            padding:5px 15px;
            font-size: 16px;
            font-weight: 400;
            color: #333333;
            line-height: 22px;
			min-height: 40px;
			height: 100%;
            // user-modify: read-only;
            // user-modify: read-write;
            // user-modify: write-only;//可以输入富文本
            // [contenteditable]:focus{outline: none;}
            flex: 1;
            flex-grow: 1;
            word-wrap: break-word;/*超出部分自动换行*/
            max-width: calc(100% - 70px);
            span{
                color: #849EB2;
            }
        }
        .comments_textarea[contenteditable]:empty:before {
            content: attr(placeholder);
            color: #cccccc;
        }
        .comments_textarea[contenteditable]:focus {
            content: none;
        }
        .at_btn{
            position: absolute;
            right: 85px;
            bottom: calc(25px + var(--ios-safe-bottom-low));
            bottom: calc(25px + var(--ios-safe-bottom-higt));
            font-size: 18px;
            font-family: Roboto-Regular, Roboto;
            font-weight: 400;
            color: #999999;
            line-height: 21px;
        }
        .comments_submit{
            width: 52px;
            height: 38px;
            text-align: center;
            line-height: 38px;
            background: #1943FE;
            border-radius: 11px 11px 11px 11px;
            color: #fff;
            font-size: 16px;
            margin-left: 15px;
        }
    }
    .user{
        // background: #F5F6F8;
        height: 90vh;
        &_tit{
            padding: 25px 0 15px 0;
            // background: #F5F6F8;
            font-size: 16px;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
            color: #333333;
            text-align: center;
        }
        &_list{
            padding: 0 20px;
            line-height: 1.6;
            background: #fff;
            max-height: calc(100% - 65px);
            overflow-y: scroll;
            padding-bottom: 70px;
            &_item{
                padding: 10px 0;
                display: flex;
                align-items: center;
                margin: 5px auto 5px auto;
                display: flex;
                .avatar{
                    display: flex;
                    .avatar_img{
                        border-radius: 4px;
                    }
                }
                .info{
                    margin-left: 10px;
                    font-size: 14px;
                    font-family: Roboto-Regular, Roboto;
                    font-weight: 400;
                    color: #333333;
                    line-height: 22px;
                    .name{
                        font-size: 16px;
                        font-family: PingFang SC-Semibold, PingFang SC;
                        font-weight: 600;
                        color: #020202;
                        line-height: 22px;
                    }
                    .content{
                        margin-top: 5px;
                    }
                    .time{
                        font-size: 12px;
                        color: #999999;
                        line-height: 14px;
                        margin-top: 5px;
                        text{
                            color: #849EB2;
                            margin-left: 5px;
                        }
                    }
                }
            }
        }
    }
</style>
<style scoped lang="scss">
    .comments{
		.title{
		    font-size: 14px;
		    font-family: Roboto-Regular, Roboto;
		    font-weight: 400;
		    color: #999999;
		    // padding: 10px 0;
		    .rigth{
		        color: #849EB2;
		    }
			padding: 10px;
		}
        :deep(){
            .van-collapse{
                --van-cell-background:transparent !important;
            }
            .van-cell{
                background: transparent !important;
            }
        }
    }
</style>