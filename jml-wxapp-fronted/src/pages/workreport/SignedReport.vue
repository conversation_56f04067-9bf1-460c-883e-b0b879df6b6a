<template>
    <div class="page">
        <u-sticky >
            <div class="card_list" style="margin: 0; padding: 10px  10px 0 10px; height: auto;">
                <div class="card_tool">
                    <div class="tool">
                        <!-- <div @click="showPickerFun" class="item">
                            {{  selectedUser && selectedUser.name?selectedUser.name:'创建人' }}
                            <u-icon size="6"  name="/static/icon/sel_icon.png" />
                        </div> -->
                        <div @click="calendarShow= true" class="item">
                            <template v-if="searchQuery.startDate">{{ `${searchQuery.startDate} - ${searchQuery.endDate}  `}}</template>
                            <template v-else>报告日期：</template>
                            <u-icon size="6"  name="/static/icon/sel_icon.png" />
                        </div>
                    </div>
                </div>
            </div>
        </u-sticky>
        <div class="card_list" style="padding-top: 0; height: auto;">
            <div ref="table"  class="">
                <uni-table border stripe emptyText="暂无更多数据" >
                	<!-- 表头行 -->
                	<uni-tr  >
                		<uni-th fixed  align="center" :width="80">姓名</uni-th>
                		<uni-th v-for="(item,index) in data.dayList"  :key="index"  :width="100" align="center">{{item.label}}</uni-th>
                	</uni-tr>
                	<!-- 表格数据行 -->
                	<uni-tr v-for="(info,i) in data.reportList" :key="i" >
                		<uni-td fixed  align="center">{{info.staffName}}</uni-td>
                		<uni-td v-for="(item,index) in data.dayList" align="center"  :key="index">
                			<view v-if="info[item.field]>0" class="link" style="color: #849EB2; font-weight: bold;" @click="openiframe(item.field,info['staffId'])">{{info[item.field]}}</view>
                			<view v-else>{{info[item.field]}}</view>
                		</uni-td>
                	</uni-tr>
                </uni-table>
            </div>
        </div>
        <u-calendar v-model:show="calendarShow" color="#849EB2"
        	monthNum = "36"
        	minDate="2022-01-01"
        	:maxDate="dayjs().add(1, 'days').format('YYYY-MM-DD')"
        	confirm-disabled-text="请选择结束时间" mode="range"  round="10" 
        	@confirm="onCalendarConfirm"
        	closeOnClickOverlay
        	@close="calendarShow=false" 
        />
        <!-- <van-popup v-model:show="showPicker"  round position="bottom">
            <van-picker
                title="选择创建人"
                :loading="userLoad"
                :columns="userList"
                @confirm="onConfirm"
                @cancel="showPicker = false"
                :columns-field-names="{text: 'name', value: 'id' }"
            />
        </van-popup> -->

        <u-popup v-model:show="iframeShow" round="10" mode="bottom" :style="{ height: '80%' }" zIndex="998" closeable closeOnClickOverlay @close="iframeShow=false">
        	 <div class="iframeAddInfo" :style="{ height: '80vh', position:'relative', }" > 
        		<h3 class="iframe_tit">工作报告</h3>
        		<div class="iframe_Info">
        			<workreport ref="iframeworkreport" :date="iframeDate" :createdStaffId="staffId" isComponent></workreport>
        		</div>
        	</div>	
        </u-popup>

        <floating-window>
            <!-- <div
                v-has="{ menu_code: 'Contact', function_code: 'U' }"
                @click="router.push({name: 'contactEdit'})"
                class="item add_btn"
            >
                <van-icon size="20"  name="/static/icon/add.png" />
            </div> -->
        </floating-window>
    </div>
</template>
<script setup>
import FloatingWindow from '@/components/FloatingWindow.vue';
import dayjs from "dayjs";
import {queryGmReport}  from  '@/api/workreport'
import {queryUserList}  from  '@/api/common'
import workreport from '@/pages/workreport/Index.vue';
	const prop =  defineProps({})
    const router = useRouter();
	const util = inject("util");
    const data = ref({});
    const searchQuery = reactive({
        multiFiled:'',
        startDate:dayjs().subtract(7, 'day').format('YYYY-MM-DD'),
        endDate:dayjs().subtract(0, 'day').format('YYYY-MM-DD'),
        isAccReport:3
    })
    const getDataPage = () =>{
        queryGmReport(searchQuery).then((res) => {
            data.value = res.data
        }).catch((err) => {
        });
    }
    const init = () =>{
        getDataPage()
    }


    const calendarShow = ref(false)
    const onCalendarConfirm = (values) => {
    	const start= values[0],end = values[values.length - 1];
        calendarShow.value = false;
        searchQuery.startDate = `${dayjs(start).format('YYYY-MM-DD')}`;
        searchQuery.endDate = `${dayjs(end).format('YYYY-MM-DD')}`;
        getDataPage()
    };

    const showPicker = ref(false)
    const table = ref();
    const windowSize = ref({})
    const windowResizeCallback = (res) => {
    	windowSize.value = res.size
    }
    uni.onWindowResize(windowResizeCallback)
	
    const iframeShow = ref(false)
    const iframeDate = ref(null)
    const iframeworkreport = ref(null)
    const staffId = ref(null)
    const openiframe =  (date,userId) =>{
        iframeDate.value = date
        staffId.value = userId
        iframeShow.value = true
        // iframeworkreport.value?.getDataPage()
    }
    onMounted(() => {
        init()
    })
	onUnmounted(()=>{
		uni.offWindowResize(windowResizeCallback)
	})
</script>
<style lang="scss" scoped>
    @import '@/styles/list.scss';
    .page{
        :deep(.van-popup){
            --van-padding-md:15px;
        }
        :deep(){
            .van-sticky{
                background: #fff;
            }
        }
        :deep(.el-table){
            .cell{
                padding: 0 5px;
            }
        }
        :deep(.el-table--striped){
            --el-table-border:0;
            --el-table-bg-color:none;
            .el-table__row{
                background: #F7F7F7;
                td{
                    background: #F7F7F7;
                }
                &.el-table__row--striped{
                    background: #fff;
                    td{
                        background: #fff;
                    }
                }
            }
        }
    }
</style>