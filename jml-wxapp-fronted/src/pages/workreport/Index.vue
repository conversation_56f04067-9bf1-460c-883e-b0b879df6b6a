<template>
    <div class="page">
        <div v-if="!isComponent" class="head">
            <!-- <div class="qty">共{{data.list.length}}条</div> -->
            <div class="search">
                <u-search
                    v-model="searchQuery.multiFiled"
                    placeholder="公司名称"
                    shape="square"
                    @change="util.debounce(onSearch)"
            		@custom="util.debounce(onSearch)"
                >
                </u-search>
            </div>
        </div>

        <div class="card_list" :class="{'isComponent':isComponent}">
            <div v-if="!isComponent" class="card_tool">

				<view class="van-tabs">
					<view class="van-tab" :class="{'active':active_tabs=='MyReport' }" title="我的报告" @click="tabChange('我的报告','MyReport')" v-if="hasPermission('MyReport','R')">我的报告</view>
					<view class="van-tab" :class="{'active':active_tabs=='SubReprot' }" title="区域报告" @click="tabChange('区域报告','SubReprot')" v-if="hasPermission('SubReprot','R')" >区域报告</view>
					<view class="van-tab" :class="{'active':active_tabs=='UnReadReport' }" title="未读报告" @click="tabChange('未读报告','UnReadReport')" v-if="hasPermission('UnReadReport','R')">未读报告</view>
					<view class="van-tab" :class="{'active':active_tabs=='AllReport' }" title="全部报告" @click="tabChange('全部报告','AllReport')" v-if="hasPermission('AllReport','R')" >全部报告</view>
				</view>
                <div class="tool">
                    <div @click="reset" v-if="searchQuery.startDate" style="position: absolute; left: 0;  display: flex; align-items: center;">
                        <u-icon size="16" name="close-circle-fill" color="#849EB2" />
                        <span class="" style=" margin-left: 5px; font-weight: normal;">清除筛选</span>
                    </div>
                    <div @click="showPickerFun" v-if="searchQuery.tab!='我的报告'" class="item">
                        <text class="text">{{  selectedUser && selectedUser.name?selectedUser.name:'员工' }}</text>
                        <u-icon size="6"  name="/static/icon/sel_icon.png" />
                    </div>
                    <div @click="calendarShow= true" class="item">
						<text class="text">
							{{ searchQuery.startDate ? `${searchQuery.startDate} - ${searchQuery.endDate}` : '日期'}}
						</text>
                        <u-icon size="6"  name="/static/icon/sel_icon.png" />
                    </div>
                </div>
            </div>
            <!-- <view class="card_list" style="height: 100%;"> -->
            	<view class="swiper-item" style="height:calc(100% - 65px);">	
            		<scroll-view style="height:100%" scroll-y enable-flex  @scrolltolower="getDataPage()">
						<uni-swipe-action>
						<div v-for="(item,index) in data.list" :key="index" class="van-swipe-cell">
							<uni-swipe-action-item >
								<template v-if="active_tabs=='MyReport'"  #left>
									<div v-if="item.submit_status!='1'" @click.stop="deleteRecord(item.id)" class="card_left">
										<div class="card_left_btn">
											<u-icon size="20" color="#fff" name="trash" />
											<div class="txt">删除</div>
										</div>
									</div>
								</template>
								<div class="card" >
									<div v-if="!isComponent" class="card_head">
										<div v-if="item.contactName && item.contactName.length>0" class="card_head_name">
											{{ item.contactName.substr(0,1) }}
										</div>
										<div class="card_head_tit">
											<div class="txt1">{{ item.contactName }}</div>
											<div class="txt2">{{ item.mobile_phone }}</div>
										</div>
										<div  v-if="hasPermission('UnReadReport','R')"  class="head_status">
											<image size="40" v-if="item.manager_read =='1'"  class="van-icon__image" width="40"  mode="widthFix" src="/static/icon/read.png" />
											<image size="40" v-if="item.manager_read =='0'" class="van-icon__image" width="40"  mode="widthFix" src="/static/icon/unread.png" />
										</div>
									</div>
									<u-divider v-if="!isComponent"></u-divider>
									<div class="card_main">
										<template v-if="isComponent">
											<div class="item">
												<div class="left">签到地址</div>
												<div class="rigth">{{item.checkin_address}}</div>
											</div>
										</template>
										<template v-else>
											<div class="item">
												<div class="left">拜访公司</div>
												<div class="rigth">{{item.accName}}</div>
											</div>
											<div class="item">
												<div class="left">相关机会</div>
												<div class="rigth">{{item.oppName}}</div>
											</div>
										</template>
										<div class="item">
											<div class="left">提交状态</div>
											<div class="rigth">
												<u-tag  v-if="item.submit_status=='1'" style="display: inline-flex;" size="mini" borderColor="#99F18D" bgColor="#99F18D" text="已提交"></u-tag>
												<u-tag  v-else bgColor="#F1A48D" size="mini" style="display: inline-flex;" borderColor="#F1A48D" text="未提交" />
											</div>
										</div>
									</div>
									<u-divider></u-divider>
									<div class="card_footer">
										<div class="item">
											<div class="left">创建时间</div>
											<div class="rigth">{{item.created_time}} <span  @click.stop="handDetail(item)" class="link">查看 <u-icon name="arrow-right" size="12" /></span> </div>
										</div>
										<div class="item">
											<div class="left">创建员工</div>
											<div class="rigth">{{item.createdStaffName}}</div>
										</div>
									</div>
								</div>
								<template v-if="active_tabs=='MyReport'"  #right>
									<div v-if="item.submit_status!='1'"  class="card_right">
										<div @click.stop="router.push({name: 'workreportEdit',params: {id:item.id}})" class="card_right_btn">
											<u-icon size="20" name="/static/icon/edit.png" />
											<div class="txt">编辑</div>
										</div>
										<div @click.stop="submitRecord(item.id)" class="card_right_btn">
											<u-icon size="20" color="#fff" name="checkmark-circle-fill" />
											<div class="txt">提交</div>
										</div>
									</div>
								</template>
							</uni-swipe-action-item>
						</div>
						</uni-swipe-action>
						<div class="empty_box" v-if="!data.loading && (!data.list ||  data.list.length<=0)">
						    <u-empty v-if="!data.list || data.list.length==0" text="未找到信息" :icon="`${config.static}images/empty/data.png`" ></u-empty>
							<div v-if="active_tabs=='MyReport' && hasPermission('Reimbursement','C')"
								@click="router.push({name:'workreportEdit'})"  
								class="btn_add"><u-icon size="20" name="/static/icon/btn_add_icon.png" />  <text class="span">新建 </text></div>
						</div>                    		
                    </scroll-view>
                </view>
			<!-- </view>	 -->
        </div>
        <u-calendar v-model:show="calendarShow" confirmColor="#849EB2" 
			monthNum = "24"
			:minDate="dayjs().add(-1, 'year').format('YYYY-MM-DD')"
			:maxDate="dayjs().add(1, 'days').format('YYYY-MM-DD')"
			confirm-disabled-text="请选择结束时间" mode="range"  round="10" 
			@confirm="onCalendarConfirm"
			closeOnClickOverlay
			@close="calendarShow=false" 
		/>
		<u-picker
		    title="创建员工"
			v-model:show="showPicker"
			confirmColor="#849EB2"
		    :columns="[userList]"
		    @confirm="onConfirm"
		    @cancel="showPicker = false"
			keyName="name"
			closeOnClickOverlay
		/>
        <floating-window v-if="!isComponent">
            <div v-if="hasPermission('GMReadReport','U')"  @click="onGmRead" class="item">
                <span class="gmRead">一键<br>阅读</span>
            </div>
            <div
				v-if="hasPermission('Reimbursement','C')"
                @click="router.push({name:'workreportEdit'})"
                class="item add_btn"
            >
                <u-icon size="20"  name="/static/icon/add.png" />
            </div>
        </floating-window>
    </div>
</template>
<script setup>
import FloatingWindow from '@/components/FloatingWindow.vue';
import dayjs from "dayjs";
import {queryPageData,gmRead,deleteReport,submitReport}  from  '@/api/workreport'
import {queryUserList}  from  '@/api/common'
	const prop =  defineProps({
		isComponent: {
			type: Boolean,
			default: false,
		},
		date:{
			type: String,
			default: null,
		},
		createdStaffId:{
			type: String,
			default: '',
		},
	})
    const router = useRouter();
	const util = inject("util");
    const data = ref({});
    
    const onSearch = () => {
        initEmpty()
    }
    const routerQuery =  prop;
    const searchQuery = reactive({
        multiFiled:'',
        startDate:  prop.date || '',
        endDate:   prop.date || '',
        createdStaffId:prop.createdStaffId,
        type : prop.isComponent?2:1,
        tab:prop.isComponent?'' : '我的报告',
    })
    watch([()=>prop.date,()=>prop.createdStaffId],([date,createdStaffId]) => {
		debugger
        if(date && createdStaffId){
            searchQuery.startDate = date
            searchQuery.endDate = date
            searchQuery.createdStaffId = createdStaffId
            if(prop.createdStaffId) selectedUser.value = userList.value.find(i=>i.id==prop.createdStaffId)
            initEmpty()
        }
    });
	const permissionTool = usePermission();
    let UnReadReport = permissionTool.hasPermission('UnReadReport','R')?1:0;
    const active_tabs = ref('MyReport') //ref((prop.date || routerQuery.startDate) ?0:UnReadReport)
    const pageData = ref({})
    const tabChange = (title,name)=>{
        searchQuery.tab = title
		active_tabs.value = name
    }
    const getDataPage = (pageNo) =>{
        let info = pageData.value[active_tabs.value]?pageData.value[active_tabs.value]:ref({}).value;
        if(!info.loadData){
            info.loadData = useRelatedList({ size:10})
            info.loadData.query = searchQuery;
        }
        // if(active_tabs.value==1){
        //     info.loadData.query.managerRead = 0
        // }else{
        //     delete info.loadData.query.managerRead
        // }
        if(searchQuery.tab=='我的报告'){
            delete info.loadData.query.createdStaffId
        }
		if(info.loadData.finished ||  info.loadData.loading){
		    pageData.value = info.loadData;
		    return;
		}
		info.loadData.loading = true;
        queryPageData(info.loadData.payload).then((res) => {
            info.loadData.list = [...info.loadData.list, ...res.data.list]
            info.loadData.totalRow = res.data.totalRow
            info.loadData.pageNo = info.loadData.pageNo + 1 ;
            info.loadData.loading = false;
            info.loadData.finished = res.data.lastPage;
            info.loadData.refreshLoading = false
            data.value = info.loadData;
            pageData.value[active_tabs.value]= info;
        }).catch((err) => {
            info.loadData.finished = true;
        });
    }
    const init = () =>{
        prop.createdStaffId && (!userList.value || userList.value.length==0) && getUserList()
        getDataPage()
    }
    const initEmpty =() =>{
        data.value = {}
        pageData.value = [];
		getDataPage(1)
    }
    watch(() => active_tabs.value,(newValue, oldValue) => {
        if(pageData.value[newValue] && pageData.value[newValue].loadData){
            data.value =  pageData.value[newValue].loadData;
        }else{
            getDataPage()
        }
    });
    onMounted(() => {
      console.log("挂载");
    })
    const handDetail =(item)=>{
        if(searchQuery.tab=='未读报告'){
            router.push({name: 'workreportInfo',params: {id: item.id,readFlag:1}})
        }else{
            router.push({name: 'workreportInfo',params: {id: item.id}})
        }
    }
    const finishedText = computed(() => {
        return data && data.list &&  data.list.length>0?'没有更多了':'';
    })
    const calendarShow = ref(false)
    const onCalendarConfirm = (values) => {
		const start= values[0],end = values[values.length - 1];
        calendarShow.value = false;
        searchQuery.startDate = `${dayjs(start).format('YYYY-MM-DD')}`;
        searchQuery.endDate = `${dayjs(end).format('YYYY-MM-DD')}`;
        initEmpty()
    };


    const showPicker = ref(false)
    const userList =ref([])
    const userLoad = ref(true)
    const getUserList = ()=>{
        queryUserList().then((res) => {
            userList.value = res.data
            userList.value.unshift({name:'全部',id:''})
            userLoad.value = false
            if(prop.createdStaffId) selectedUser.value = userList.value.find(i=>i.id==prop.createdStaffId)
        }).catch((err) => {
        });
    }
    const showPickerFun = ()=>{
        (!userList.value || userList.value.length==0) && getUserList()
        showPicker.value = true
    }
    const selectedUser = ref(null)
    const onConfirm = ({value,indexs}) =>{
		console.log('onConfirm',value,indexs)
        selectedUser.value = value[0]
        searchQuery.createdStaffId = value[0].id
        showPicker.value = false
        initEmpty()
    }
    const onGmRead =()=>{
		uni.showModal({
			title: '提示',
			content: '是否一键已读',
			success: function (unires) {
				if (unires.confirm) {
					gmRead().then((res) => {
						uni.showModal({
						    title:'提示',
						    showCancel:false,
						    content: '操作成功',
						}).then((aa) => {
						    initEmpty()
						})
					}).catch((err) => {
					});
				}
			}
		});
		
    }
    const deleteRecord = (id)=>{
        uni.showModal({
            title: '提示',
            content: '是否删除该记录？',
			success: function (unires) {
				if (unires.confirm) {
					deleteReport(id).then((res) => {
					    uni.showModal({
					        title:'提示',
					        showCancel:false,
					        content: '操作成功',
					    }).then((aa) => {
					        initEmpty()
					    })
					}).catch((err) => {
					});
				}
			}
        })
    }
    const submitRecord = (id)=>{
        uni.showModal({
            title: '提示',
            content: '是否提交该记录？',
			success: function (unires) {
				if (unires.confirm) {
					submitReport(id).then((res) => {
					    uni.showModal({
					        title:'提示',
					        showCancel:false,
					        content: '操作成功',
					    }).then((aa) => {
					        initEmpty()
					    })
					}).catch((err) => {
					});
				}
			}
        })
    }
    const reset = () =>{
        searchQuery.startDate = ''
        searchQuery.endDate = ''
        initEmpty()
    }
	onMounted( async() => {
		init()
	})
	onShow(() => {
		uni.$once("refresh", (data) => {

			uni.$off('refresh');

			initEmpty()

		})
	})
    defineExpose({
        getDataPage,initEmpty
    })
</script>
<style lang="scss" scoped>
    @import '@/styles/list.scss';
</style>
<style lang="scss">
	.van-tabs {
	    position: relative;
		display: flex;
		
		.van-tab{
		    background: none;
		    position: relative;
			display: flex;
			align-items: center;
			justify-content: center;
			box-sizing: border-box;
			padding: 0 var(--van-padding-base);
			color: var(--van-tab-text-color);
			font-size: var(--van-tab-font-size);
			line-height: var(--van-tab-line-height);
			cursor: pointer;
			border: 1px solid #D1D2D6;
			&.active{
				border-color: #849eb2;
				color: var(--van-tab-act-text-color);
				font-weight: 600;
			}
			&:first-child{
				border-radius: 4px 0 0 4px;
			}
			&:last-child{
				border-radius:0 4px 4px 0;
			}
		}
	}
</style>