<template>
    <div class="page pd100">
		<u--form labelPosition="top" labelWidth="auto" ref="form1" >
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">报销信息</div>
                </div>
				<u-form-item label="地区" prop="district" required>
					<zxz-uni-data-select clearable placeholder="请选择"  v-model="info.district" dataText="district" @change="areaChange" dataValue="district"  :localdata="userInfo.staffAreaList"></zxz-uni-data-select>
				</u-form-item>
				
				<u-form-item label="时间" prop="checkin_time" required  @click="calendarShow= true">
					<!-- <u--input
						v-model="info.start_date"
						placeholder="项目时间"
						readonly
						disabled
						disabledColor="#ffffff"
						border="none"
						style="width: 100%;pointer-events: none;"
					/> -->
					<text v-if="info.start_date">{{ info.start_date  }} - {{ info.end_date }}</text>
					<u--input disabled disabledColor="#ffffff" v-else class="van-field__control" readonly placeholder="请选择时间" />
					
					<template #right><u-icon name="calendar" size='22' @click="calendarShow= true"></u-icon></template>
				</u-form-item>
				
                <!-- <van-field label="时间" required  readonly  label-align="top" right-icon="calendar-o" class="int"  @click="calendarShow= true" >
                    <template #input>
                        <span v-if="info.start_date">{{ info.start_date  }} - {{ info.end_date }}</span>
                        <input v-else class="van-field__control" readonly placeholder="请选择时间">
                    </template>
                </van-field> -->
				
				<u-form-item label="工作报告"  prop="accountId" required>
					<selector
					    title="选择工作报告(多选)"
					    :readonly="!info.start_date || !info.end_date"
					    readonlyMsg="请先选择时间！"
					    v-model="info.reportArr"
					    :fieldArr="[{label:'客户',field:'accName'},{label:'日期',field:'arrival_time'}]"
					    :list_api_url="list_api_url"
					    @change="selectorChange($event,'666')"
					>
					</selector>
					<!-- <template #right><u-icon name="calendar"></u-icon></template> -->
				</u-form-item>
				
				<u-form-item label="备注" prop="info.remark" >
					<u--textarea :count="!!info.remark" maxlength="150"  v-model="info.remark"  class="int"   placeholder="备注"  />
				</u-form-item>
            </div>
        </div>
		</u--form>
        <div class="card_info detail">
			
			<!-- <uni-collapse ref="collapse" v-model="value">
						<uni-collapse-item title="默认开启" >
							<view class="content">
								<text class="text">{{content}}</text>
							</view>
						</uni-collapse-item>
						<uni-collapse-item title="折叠内容">
							<view class="content">
								<text class="text">折叠内容主体，这是一段比较长内容。默认折叠主要内容，只显示当前项标题。点击标题展开，才能看到这段文字。再次点击标题，折叠内容。</text>
							</view>
						</uni-collapse-item>
					</uni-collapse> -->
			
            <uni-collapse v-model="activeNames">
            <uni-collapse-item name="1" open>
                <template #title>
                    <div class="uni-collapse-item__title-box detail_tit">
                        费用报销明细
                    </div>
                </template>
                <div class="detail_list">
					<uni-table border stripe emptyText="暂无更多数据">
						<!-- 表头行 -->
						<uni-tr>
							<uni-th width="80" align="center">编号</uni-th>
							<uni-th width="120" align="center">报销大类</uni-th>
							<uni-th width="120" align="center">科目</uni-th>
							<uni-th width="120" align="center">金额</uni-th>
							<uni-th width="80" align="center">客户人员</uni-th>
							<uni-th width="150" align="center">创建时间</uni-th>
							<uni-th width="80" operate align="center">操作</uni-th>
						</uni-tr>
						<!-- 表格数据行 -->
						<uni-tr v-for="(detail,i) in detailList" :key="i">
							<uni-td align="center"> {{i+1}} </uni-td>
							<uni-td align="center"> {{detail.category}}</uni-td>
							<uni-td align="center"> {{detail.item}}</uni-td>
							<uni-td align="center"> {{detail.amount}}</uni-td>
							<uni-td align="center"> {{detail.contactName}}</uni-td>
							<uni-td align="center"> {{detail.created_time}}</uni-td>
							<uni-td  operate align="center">
								<view class="uni-group">
									<u-button class="uni-button" @click="handleShow(detail,i)" color="#849EB2" :plain="true" :hairline="true" size="mini" type="primary">查看</u-button>
									<div v-if="!detail.id">
									    <u-button class="uni-button"  @click.prevent="deleteRow()" :plain="true" :hairline="true" size="mini" type="primary">删除 ></u-button>
									</div>
								</view>
							</uni-td>
						</uni-tr>
					</uni-table>
                    <div @click="handleShow({img:[]},detailList.length)"   class="detail_add"><van-icon name="plus" /> 添加一栏</div>
                </div>
            </uni-collapse-item>
            </uni-collapse>
        </div>
		<!-- <u-modal v-model:show="showAddPop" @confirm="handconfirm" overlayStyle="{'touch-action':'none'}" @close="showAddPop = false" @cancel="showAddPop = false" title="费用报销明细" show-cancel-button confirmColor="#849EB2"> -->
		<u-popup :show="showAddPop" :round="10" closeable mode="bottom" @close="showAddPop = false">	
			<div class="addInfo">
				<u--form labelPosition="top" labelWidth="auto">
					<div class="card">
						<div class="card_head">
							<div class="card_head_tit">费用报销明细</div>
						</div>
						<div class="card_from">
						<u-form-item label="科目" prop="category">
							<uni-data-picker placeholder="报销科目" popup-title="报销科目"
								v-slot:default="{data, error, options}"
								:map="{text:'dname',value:'dval'}"
								:localdata="cascaderOptions"
								@change="onFinish"
							 >
								<view v-if="popInfo.category" class="selected showarea">
									 <text>{{ popInfo.category  }}{{ popInfo.item  }}</text>
								</view>
								<view v-else class="showarea">
									<text class="readonly">请选择报销科目</text>
								</view>
							</uni-data-picker>
						</u-form-item>
						
					    <van-field label="发生日期"   readonly  label-align="top" right-icon="calendar-o" class="int"  @click="dateShow = true" >
					        <template #input>
					            <span v-if="popInfo.occur_date">{{ popInfo.occur_date  }} </span>
					            <input v-else class="van-field__control" readonly placeholder="发生日期">
					        </template>
					    </van-field>
						<u-form-item label="未税金额" prop="untaxed_amount"  >
							<u--input  v-model="popInfo.untaxed_amount" type="digit"   placeholder="请输入金额"  />
						</u-form-item>
						
						<u-form-item label="税金" prop="tax"  >
							<u--input  v-model="popInfo.tax" type="digit"   placeholder="请输入金额"  />
						</u-form-item>
						
					    <div class="info mt10">
					        <div class="left">含税金额：</div><div class="rigth">{{ popInfo.amount }}</div>
					    </div>
						
						<u-form-item label="联系人"  prop="contactId">
							<load-selector v-model="popInfo.contactId" filterable value-key="id" value-fild="id" load_url="/api/contact/queryListByStaff"></load-selector>
						</u-form-item>
						<u-form-item label="招待人员数量" prop="invites"  >
							<u--input  v-model="popInfo.invites" type="digit"   placeholder="请输入招待人员数量"  />
						</u-form-item>
						<u-form-item label="附件图片" prop="img"  >
							<u-upload
								:max-count="2"
								multiple
								@after-read="afterRead($event,'img')"
								:deletable="true"
								:sizeType="['compressed']"
								:before-delete="(file,detail)=>beforeDelete(file,detail)"
								@delete="beforeDelete($event,'img')"
								:fileList="popInfo.img"
							>
							</u-upload>
						</u-form-item>
						
					   <u-form-item label="备注" prop="remark" >
							<u--textarea :count="!!popInfo.remark" maxlength="150"  v-model="popInfo.remark"  class="int"   placeholder="备注"  />
					   </u-form-item>
					   </div>
					</div>
					
				</u--form>
				<footer-btn @onconfirm="handconfirm" @oncancel="showAddPop = false"  confirm_btn_text="确定"></footer-btn>
            </div>
        </u-popup>

		<u-calendar v-if="calendarShow" v-model:show="calendarShow" confirmColor="#849EB2"
			monthNum = "12"
			:minDate="minDate"
			:maxDate="maxDate"
			confirm-disabled-text="请选择结束时间" mode="range"  round="10" 
			@confirm="onCalendarConfirm"
			closeOnClickOverlay
			@close="calendarShow=false" 
		/>
		
        <footer-btn :cancelBtnShow="false" confirm_btn_text="保存"  @onconfirm="save()"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  watch ,  inject ,computed} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import {useDictStore} from '@/store/modules/dictStore'
import {queryDetails,saveEdit}  from  '@/api/cost'
import {queryListData as queryContactData}  from  '@/api/contact'
import {queryListData as queryReportData}   from  '@/api/workreport'
import Selector from '@/components/Selector.vue';
import LoadSelector from '@/components/LoadSelector.vue';
import dayjs from "dayjs";
import { isArray } from 'lodash';
import {upload,dictTree,asyncUpload}  from  '@/api/common'
   const prop =  defineProps({
   	id: String,
   	isComponent: {
   		type: Boolean,
   		default: false,
   	},
   	accountId:{
   		type: String,
   		default: null,
   	},
   	serviceType:{
   		type: String,
   		default: null,
   	},
   })
   const routerQuery = prop;
   const router = useRouter();

   
    // 相关字典
    const dictStore = useDictStore()
	const util = inject("util");
    const lodash = inject("lodash");
    const activeNames = ref(['1'])
	
	const { userInfo } = useUserStore();
	const info = ref({
		area_code:userInfo.staffAreaList.length==1 ? userInfo.staffAreaList[0].areaCode : null,
		district:userInfo.staffAreaList.length==1 ? userInfo.staffAreaList[0].district : null ,
		reportArr:[]
	});
	
    const detailList = ref([])
    const userList = ref([])
    const reportList = ref([])
    const popInfo = ref({});
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data.basic;
            info.value.reportArr = info.value.salesReportIds ? info.value.salesReportIds.split(','):[];
            detailList.value = res.data.detailList.map(item=>{
				if(item.img){
				    item.img = item.img.split(';').map(i=>{
						return{url:import.meta.env.VUE_APP_BASE_IMG + i}
					})
				}else{
				    item.img = []
				}
				return item
			})
        }).catch((err) => {
        });
    }
    const deleteFild = ['createdStaffName','created_time','updateStaffName','update_time','reportArr','salesLeadName'];
    const list_api_url = computed(()=>{
        return `/api/salesReport/queryListBy?reimId=${routerQuery.id?routerQuery.id:''}&startDate=${info.value.start_date}&endDate=${info.value.end_date}`
    })
    const requiredFild = [
        {key:'start_date',message:'请选择开始时间',required: true,},
        {key:'end_date',message:'请选择结束时间',required: true,},
    ]
    const save = () => {
        for(let index  in requiredFild){ 
            var item =  requiredFild[index]
            if(item.required && (!info.value[item.key] || info.value[item.key]=='')) return ElMessage.error(item.message);
        };
        let postData = info.value
        postData.salesReportIds = postData.reportArr.join(',')
        deleteFild.forEach((key)=>{
            delete postData[key]
        })
        postData.detailList = detailList.value.map(item=>{
            let detail = {
                id : item.id,/**记录Id，修改时必填**/
                category:item.category, /**报销大类*/
                occur_date:item.occur_date,
                item : item.item,/**科目*/
                amount : item.amount,/**含税金额*/
                contactId : item.contactId,/**客户人员Id**/
                remark : item.remark,/**备注*/
                invites : item.invites,/**招待人员*/
                tax: item.tax,/**税金[注意不是税率]*/
                untaxed_amount:item.untaxed_amount /*未税金额*/
            }
            if(isArray(item.img)) {
                item.img.forEach((item,index) => {
                    detail[`img${index+1}`] = item.url
                });
            }
            return detail
        })
        saveEdit(postData).then((res)=>{
            uni.showModal({
            	title: '提示',
            	content: info.value.id ?'修改成功':'保存成功',
            	showCancel:false,
            	success: function (confirmres) {
            		if (confirmres.confirm) {
            			if(prop.isComponent)emit('confirm', res)
            			else{
            				uni.$emit("refresh", {refresh: true}); 
            				router.back();
            			}
            		} else if (confirmres.cancel) {
            			uni.$emit("refresh", {refresh: true}); 
            			console.log('用户点击取消');
            		}
            	}
            });
        })
    }
    const getUserList = () =>{
        queryContactData().then((res) => {
            userList.value = res.data
        }).catch((err) => {
        });
    }
    const getReportList = () =>{
        queryReportData().then((res) => {
            reportList.value = res.data
        }).catch((err) => {
        });
    }
    const init = async() =>{
        getUserList()
        const result = await dictStore.getDictBatch(['reim_itme']);
        if(routerQuery.id){
            getInfo()
        }
    }
    const calendarShow = ref(false)
	const minDate = dayjs().add(-6, 'month').format('YYYY-MM-DD')
	const maxDate = dayjs().add(6, 'month').format('YYYY-MM-DD')
    const onCalendarConfirm = (values) => {
		const start= values[0],end = values[values.length - 1];
		calendarShow.value = false;
        info.value.start_date = `${dayjs(start).format('YYYY-MM-DD')}`;
        info.value.end_date = `${dayjs(end).format('YYYY-MM-DD')}`;
    };
    const selectorChange = (data,info) =>{
        console.log('selector',data,info)
    }
    const deleteRow = (index) => {
        detailList.value.splice(index, 1)
    }
    let showindex = null;
    const handconfirm = () =>{
        // debugger
        if(showindex || showindex>=0){
            detailList.value[showindex] = popInfo.value;
        }else{
            detailList.value.push(popInfo.value)
        }
        showAddPop.value = false
        popInfo.value = {};
    }
    const handleShow = (data,index) =>{
        popInfo.value = data;
        if(data.img){
            if(!Array.isArray(data.img))popInfo.value.img = data.img.split(';')
        }else{
            popInfo.value.img = []
        }
        showindex = index
		if(cascaderOptions.value.length == 0){
		    dictTree('reim_category').then((res) => {
		        cascaderOptions.value = res.data
		    }).catch((err) => {
		    });
		}
        showAddPop.value = true
    }
    watch(() => popInfo.value.contactId,(newValue, oldValue) => {
        if(newValue){
            popInfo.value.contactName = userList.value.find(i => i.id == newValue ).name
        }
    });
    watch([()=>popInfo.value.untaxed_amount,()=>popInfo.value.tax],([untaxed_amount,tax]) => {
        if(untaxed_amount && tax){
            popInfo.value.amount = lodash.round(lodash.add(parseFloat(untaxed_amount) + parseFloat(tax)),2);
        }
    });

    
	const afterRead = async(event,type) =>{
		let files = [].concat(event.file)
		if(!popInfo.value[type]) popInfo.value[type] = [];
		let fileListLen =  popInfo.value[type]?popInfo.value[type].length:0
		files.map((item) => {
			popInfo.value[type].push({
				...item,
				status: 'uploading',
				message: '上传中'
			})
		})
		for (let i = 0; i < files.length; i++) {
			try {
				const {data} = await asyncUpload(files[i].url)
				let item = popInfo.value[type][fileListLen];
				popInfo.value[type].splice(fileListLen, 1, {
				  ...item,
				  status: 'success',
				  message: '',
				  url: data.url,
				});
				fileListLen++;
			} catch (e) {
				console.log(e)
				uni.showToast({
					title: "图片上传失败",
					icon: "none"
				})
			}
		}
	}
	
    const beforeDelete = ({file,index},type) =>{
    	uni.showModal({
    		title: '提示',
    		content: '是否删除该附件？',
    		success: function (res) {
    			if (res.confirm) {
    				popInfo.value[type].splice(index, 1)
    			} else if (res.cancel) {
    				console.log('用户点击取消');
    			}
    		}
    	});
    }
    	
    
    const cascaderShow = ref(false)
    const cascaderValue = ref(null)
    const cascaderOptions = ref([])
    const onFinish = (selectedValues) => {
		let selectedOptions = selectedValues.detail.value;
        popInfo.value.category  = selectedOptions[0].text
        popInfo.value.item  = selectedOptions[1].text
        cascaderShow.value = false;
		debugger
    };
	
	
    const dateShow = ref(false)
    const onDateConfirm = (value) => {
        popInfo.value.occur_date = `${dayjs(value).format('YYYY-MM-DD')}`;
        // popInfo.value[calendarType.value] = `${currentDate.value.join('-')} ${currentTime.value.join(':')}`
        dateShow.value = false
    };
	const areaChange =(value)=>{
		info.value.area_code = userInfo.staffAreaList.find(i=>i.district===value)?.areaCode;
	}
    onMounted(() => {
        init()
    })
    const showAddPop = ref(false)
</script>
<style lang="scss" scoped>
    @import '@/styles/edit.scss';
	.addInfo{
	    padding: 10px 0;
	    position: relative;
	    border-radius: var(--card-border-radius);
	    line-height: 1.4;
		position: relative;
		width: 100%;
		
		
		overscroll-behavior: contain;
		transform:1;
		margin-bottom: 60px;
		.card{
			padding: 0 !important;
		}
		.card_from{
			max-height: calc(100vh - 160px);
			overflow-y: scroll;
			padding: 0 20px;
		}
	}
</style>