<template>
    <div class="page pd100">
        <div class="card_info">
            <div class="card">
                <div class="card_head">
                    <div class="card_head_tit">
                        {{ info.basic.name }}
                    </div>
                </div>
                <u-divider></u-divider>
                <div class="card_main">
					<div class="item">
					    <div class="left">区域</div><div class="rigth">{{info.basic.areaName}}</div>
					</div>
					<div class="item">
					    <div class="left">地区</div><div class="rigth">{{info.basic.district}}</div>
					</div>
                    <div class="item">
                        <div class="left">总金额</div><div class="rigth">{{info.basic.amount}}</div>
                    </div>
                    <div class="item">
                        <div class="left">开始时间</div><div class="rigth">{{info.basic.start_date}}</div>
                    </div>
                    <div class="item">
                        <div class="left">结束时间</div><div class="rigth">{{info.basic.end_date}}</div>
                    </div>
                    <div class="item">
                        <div class="left">备注</div><div class="rigth">{{info.basic.remark}}</div>
                    </div>
                </div>

                <div class="card_head_tit2 mt15">
                    系统信息
                </div>
                <u-divider></u-divider>
                <div class="card_main">
                    <div class="item">
                        <div class="left">创建时间</div><div class="rigth">{{ info.basic.created_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">创建员工</div><div class="rigth">{{ info.basic.createdStaffName }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改时间</div><div class="rigth">{{ info.basic.update_time }}</div>
                    </div>
                    <div class="item">
                        <div class="left">修改员工</div><div class="rigth">{{ info.basic.updateStaffName }}</div>
                    </div>
                </div>
            </div>
        </div>
        <related-list :data="info.relatedList" :id="routerQuery.id" :baseInfo="info.basic" title="费用明细相关"></related-list>
        <div class="other">
            <div class="other_tit">费用报销明细</div>
			
			<!-- <ItemList :dataList="info.detailList" :caseStageCode="info.basic.caseStageCode" readOnly></ItemList> -->
			<uni-table border stripe emptyText="暂无更多数据">
				<!-- 表头行 -->
				<uni-tr>
					<uni-th width="80" align="center">编号</uni-th>
					<uni-th width="120" align="center">报销大类</uni-th>
					<uni-th width="120" align="center">科目</uni-th>
					<uni-th width="120" align="center">金额</uni-th>
					<uni-th width="80" align="center">客户人员</uni-th>
					<uni-th width="150" align="center">创建时间</uni-th>
					<uni-th width="80" operate align="center">操作</uni-th>
				</uni-tr>
				<!-- 表格数据行 -->
				<uni-tr v-for="(detail,i) in info.detailList" :key="i">
					<uni-td align="center"> {{i+1}} </uni-td>
					<uni-td align="center"> {{detail.category}}</uni-td>
					<uni-td align="center"> {{detail.item}}</uni-td>
					<uni-td align="center"> {{detail.amount}}</uni-td>
					<uni-td align="center"> {{detail.contactName}}</uni-td>
					<uni-td align="center"> {{detail.created_time}}</uni-td>
					<uni-td  operate align="center">
						<view class="uni-group">
							<u-button class="uni-button" @click="handleShow(detail,i)" color="#849EB2" :plain="true" :hairline="true" size="mini" type="primary">查看</u-button>
						</view>
					</uni-td>
				</uni-tr>
			</uni-table>
			
            <!-- <el-table  :data="info.detailList" stripe  :header-cell-style="{textAlign: 'center'}" table-layout="auto" style="width: 100%">
                <el-table-column fixed type="index" align="center" label="编号" width="70" />
                <el-table-column prop="category" label="报销大类" align="center" width="90" />
                <el-table-column prop="item" label="科目" align="center" width="100" />
                <el-table-column prop="amount" label="金额" align="center" width="80" />
                <el-table-column prop="contactName" label="客户人员" align="center" width="80" />
                <el-table-column prop="created_time" label="创建时间" align="center" width="160"/>
                <el-table-column fixed="right" align="center" label="操作" width="60">
                <template #default="scope">
                    <el-button link @click.prevent="handleShow(scope.row,scope.$index)" type="primary" size="small">查看 ></el-button>
                </template>
                </el-table-column>
            </el-table> -->
        </div>
        <!-- 底部弹出 -->
		<u-popup :show="showPopInfo" :round="10" closeable mode="bottom" @close="showPopInfo = false">
            <div class="pop_info">
                <div class="pop_info_tit">费用报销明细</div>
                <div class="card_info">
                    <div class="card">
                        <div class="card_main">
                            <div class="item">
                                <div class="left">报销大类</div><div class="rigth">{{popinfo.category}}</div>
                            </div>
                            <div class="item">
                                <div class="left">科目</div><div class="rigth">{{popinfo.item}}</div>
                            </div>
                            <div class="item">
                                <div class="left">发生日期</div><div class="rigth">{{popinfo.occur_date}}</div>
                            </div>
                            <div class="item">
                                <div class="left">未税金额</div><div class="rigth">{{popinfo.untaxed_amount}}</div>
                            </div>
                            <div class="item">
                                <div class="left">税金</div><div class="rigth">{{popinfo.tax}}</div>
                            </div>
                            <div class="item">
                                <div class="left">含税金额</div><div class="rigth">{{popinfo.amount}}</div>
                            </div>
                            <div class="item">
                                <div class="left">附件照片</div><div class="rigth">
                                    <!-- <van-image v-for="(item,index) in popinfo.files" :key="index"   @click="showImagePreview([imgBase+item])" fit="fill" width="100" :src="imgBase+item" /> -->
                                    <image v-for="(item,index) in popinfo.img" :key="index"   @click="showImagePreview(popinfo.img,index)"  mode="widthFix"  :src="imgBase+item" />
                                </div>
                            </div>
                            <div class="item">
                                <div class="left">备注信息</div><div class="rigth">{{popinfo.remark}}</div>
                            </div>
                            <div class="item">
                                <div class="left">创建日期</div><div class="rigth">{{popinfo.created_time}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </u-popup>
        
		<footer-btn v-if="info.basic.submit_status!='1' && hasPermission('Reimbursement','U')" :cancelBtnShow="false" @onconfirm="router.push({name: 'costEdit',params: {id:routerQuery.id}})" confirm_btn_text="编辑"></footer-btn>
        <floating-window></floating-window>
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import FooterBtn from '@/components/FooterBtn.vue';
import RelatedList from '@/components/RelatedList.vue';
import {queryDetails,queryItemDetails}  from  '@/api/cost'
import ItemList from './components/ItemList.vue';
	const prop =  defineProps(['id'])
	const router = useRouter();
	const util = inject("util");
	const routerQuery =  prop;
    const showPopInfo = ref(false)
    const popinfo = ref({})
    const handleShow = async(info,index) =>{
        let {data} = await queryItemDetails(info.id)
        popinfo.value = data;
        if(data.img){
            popinfo.value.img = data.img.split(';')
        }else{
            popinfo.value.img = []
        }
        showPopInfo.value = true
    }
    const info = ref({
        basic:{},
        relatedList:[],
        detailList:[],
    });
    const getInfo = () =>{
        queryDetails(routerQuery.id).then((res) => {
            info.value = res.data
        }).catch((err) => {
        });
    }
    const init = () =>{
        getInfo()
    }
	onShow(()=>{
		 init()
	})
    onMounted(() => {
    })
</script>
<style lang="scss">
    @import '@/styles/detailed.scss';
</style>
<style lang="scss" scoped>
    .page{
        :deep(.van-popup){
            --van-padding-md:15px;
        }
		.pop_info{
			.card_info{
				max-height: calc(100vh - 100px);
				overflow-y: scroll;
			}
		}
		
        :deep(.el-table--striped){
            --el-table-border:0;
            --el-table-bg-color:none;
            .el-table__row{
                background: #F7F7F7;
                td{
                    background: #F7F7F7;
                }
                &.el-table__row--striped{
                    background: #fff;
                    td{
                        background: #fff;
                    }
                }
            }
        }
    }
</style>