<template>
    <div class="page">
		<div class="head">
		    <div class="search">
		        <u-search
		            v-model="searchQuery.multiFiled"
		            placeholder=""
		            shape="square"
		            @change="util.debounce(onSearch)"
		    		@custom="util.debounce(onSearch)"
		        >
		        </u-search>
		    </div>
		</div>

        <div class="card_list">
        
            <scroll-view style="height:100%" scroll-y enable-flex  @scrolltolower="getDataPage()">
            	<uni-swipe-action>
        			
        	<div v-for="(item,index) in data.list" :key="index" class="van-swipe-cell"  @click="router.push({name: 'costInfo',params: {id: item.id}})">
        		
        	    <uni-swipe-action-item>
					
                    <template  #left>
                        <div v-if="item.submit_status!='1'" @click.stop="deleteRecord(item.id)" class="card_left">
                            <div class="card_left_btn">
                            	<u-icon size="20" color="#fff" name="trash" />
                            	<div class="txt">删除</div>
                            </div>
                        </div>
                    </template>
                    <div class="card">
                        <div class="card_head">
                            <div class="card_head_tit">
                                {{ item.name }}
                            </div>
                        </div>
                        <u-divider></u-divider>
                        <div class="card_main">
                            <div class="item">
                                <div class="left">总金额</div>
                                <div class="rigth">{{item.amount}}</div>
                            </div>
                            <div class="item">
                                <div class="left">开始时间</div>
                                <div class="rigth">{{item.start_date}}</div>
                            </div>
                            <div class="item">
                                <div class="left">结束时间</div>
                                <div class="rigth">{{item.end_date}}</div>
                            </div>
                            <div class="item">
                                <div class="left">提交状态</div>
                                <div class="rigth">
                                    <u-tag  v-if="item.submit_status=='1'" style="display: inline-flex;" size="mini" borderColor="#99F18D" bgColor="#99F18D" text="已提交"></u-tag>
                                    <u-tag  v-else bgColor="#F1A48D" size="mini" style="display: inline-flex;" borderColor="#F1A48D" text="未提交" />
                                </div>
                            </div>
                        </div>
                        <u-divider></u-divider>
                        <div class="card_footer">
                            <div class="item">
                                <div class="left">创建时间</div>
                                <div class="rigth">{{item.created_time}} <span  class="link">查看 <u-icon name="arrow-right" size="12" /></span> </div>
                            </div>
                            <div class="item">
                                <div class="left">创建员工</div>
                                <div class="rigth">{{item.createdStaffName}}</div>
                            </div>
                        </div>
                    </div>
                    <template  #right>
                        <div v-if="item.submit_status!='1'"  class="card_right">
							<div @click.stop="router.push({name: 'costEdit',params: {id:item.id}})" class="card_right_btn">
								<u-icon size="20" name="/static/icon/edit.png" />
								<div class="txt">编辑</div>
							</div>
							<div @click.stop="submitRecord(item.id)" class="card_right_btn">
								<u-icon size="20" color="#fff" name="checkmark-circle-fill" />
								<div class="txt">提交</div>
							</div>
                        </div>
                    </template>
                
				</uni-swipe-action-item>
            </div>
            </uni-swipe-action>
			
		<div class="empty_box" v-if="!data.loading && (!data.list ||  data.list.length<=0)">
					<u-empty v-if="!data.list || data.list.length==0" text="未找到信息" :icon="`${config.static}images/empty/data.png`" ></u-empty>
					<div 
						v-if="hasPermission('Reimbursement','C')"
						@click="router.push({name:'costEdit'})"
							class="btn_add">
						<u-icon size="20" name="/static/icon/btn_add_icon.png" />  <span>新建</span>
					</div>
				</div>
			</scroll-view>
		</div>
		
		
		<floating-window>
		    <div
		        v-if="hasPermission('Reimbursement','C')"
		        @click="router.push({name:'costEdit'})"
		        class="item add_btn"
		    >
		        <u-icon size="20" name="/static/icon/add.png"></u-icon>
		    </div>
		</floating-window>
		
    </div>
</template>
<script setup>
import { reactive, onMounted,  ref,  nextTick ,  inject,computed} from 'vue';
import FloatingWindow from '@/components/FloatingWindow.vue';
import { useDataList } from '@/vueUse/dataList'
import {queryPageData,deleteReim,submitReim}  from  '@/api/cost'


    const router = useRouter();
    const util = inject("util");
    const {data} = useDataList()
    const dictStore = useDictStore()
    
    //触底加载
    const lowerBottom=()=>{
    	console.log('lowerBottom');
    }
    // 下拉刷新
    const refresherTriggered = ref(false);
    const getFresh=()=> {
    	console.log('refresh');
    	refresherTriggered.value = 'restore'
    	initEmpty()
    }
    const onPulling=(e)=> {
    	console.log('onPulling');
    	if (e.detail.deltaY < 0) return
    }
    const finishedText = computed(() => {
        return  data.list.length>0?'没有更多了':'';
    })
    const onSearch = () => {
        initEmpty()
    }
    const searchQuery = reactive({
        multiFiled:'',
        pageNo:data.pageNo,
        pageSize:data.pageSize,
    })
	

    const getDataPage = () =>{
        searchQuery.pageNo = data.pageNo
        queryPageData(searchQuery).then((res) => {
            data.list = [...data.list, ...res.data.list]
            data.totalRow = res.data.totalRow
            data.pageNo = data.pageNo + 1 ;
            data.loading = false;
            data.finished = res.data.lastPage;
        }).catch((err) => {
            data.finished = true;
        });
    }
    const init = () =>{
        getDataPage()
    }
    const initEmpty =() =>{
        data.initEmpty()
    	getDataPage(1)
    }
    
    onMounted(() => {
    	init()
    })
    
    onReachBottom(() => {
    	console.log('onReachBottom')
    	if(!data.finished) getDataPage();
    })
    onShow(() => {
    	uni.$once("refresh", (data) => {
    		initEmpty()
    	})
    })
	
    const deleteRecord = (id)=>{
		uni.showModal({
		    title: '提示',
		    content: '是否删除该记录？',
			success: function (unires) {
				if (unires.confirm) {
					deleteReim(id).then((res) => {
						uni.showModal({
						    title:'提示',
						    showCancel:false,
						    content: '操作成功',
						}).then((aa) => {
						    initEmpty()
						})
						
					}).catch((err) => {
					});
				}
			}
		})
		
    }
    const submitRecord = (id)=>{		
		uni.showModal({
		    title: '提示',
		    content: '是否提交该记录？',
			success: function (unires) {
				if (unires.confirm) {
					submitReim(id).then((res) => {
					    uni.showModal({
					        title:'提示',
					        showCancel:false,
					        content: '操作成功',
					    }).then((aa) => {
					        initEmpty()
					    })
					}).catch((err) => {
					});
				}
			}
		})
		
    }
</script>
<style lang="scss" scoped>
    @import '@/styles/list.scss';
</style>