
page {
    --main-color: #364250;
    --van-picker-confirm-action-color:#849EB2;;
    --hei-color: #e7e7e7;
    --main-txt-color: #364250;
    --el-text-color-secondary: #3d3d3d;
    --main-bg-c:#F5F6F8;
    --main-c:#364250;
    --main-selected-c:#849EB2;
    --el-calendar-cell-width:50px;
    --el-color-primary:#364250;
    --el-font-family:"Noto Sans Display";
    --el-table-border:0;
    --van-primary-color:#849EB2;
    
    --el-color-primary:#849EB2 !important;
    
    --ios-safe-bottom-low: constant(safe-area-inset-bottom); /* 兼容 iOS<11.2 */
    --ios-safe-bottom-higt: env(safe-area-inset-bottom); /* 兼容iOS>= 11.2 */
    
    --card-border-radius:5px;
    --van-divider-line-height:1px;
    --van-divider-margin:0;
    
    --van-padding-md:0;
    --van-tabs-default-color:#373D5C;
    --van-border-radius-sm: none;
    --van-picker-confirm-action-color:#849EB2;;
    
    --el-table-border:0;
    
    --placeholder-color:#c0c4cc;
    
    --van-padding-base:8px;
    --van-tab-text-color:rgb(55, 61, 92);
    --van-tab-act-text-color:rgb(132, 158, 178);
    --van-tab-line-height:30px;
	
}
body,
html,
page,
#app {
  width: 100%;
  height: 100%;
  font-size: 12px;
    font-family: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
	overscroll-behavior-y: contain !important;
}
body { /* IOS禁止微信调整字体大小 */
    -webkit-text-size-adjust: 100% !important;
    text-size-adjust: 100% !important;
    -moz-text-size-adjust: 100% !important;
    font-size: 12px;
}

.txtrigth {
  text-align: right;
}
.ico {
  display: flex;
  align-items: center;
}
.flex {
  display: flex;
}
.flex-space-between {
  display: flex;
  justify-content: space-between;
}
.align-items_center {
  display: flex;
  align-items: center;
}
.justify-content_center {
  display: flex;
  justify-content: center;
}
input::-webkit-input-placeholder {
  color: var(--placeholder-color);
}
input::-moz-input-placeholder {
  color: var(--placeholder-color);
}
input::-ms-input-placeholder {
  color: var(--placeholder-color);
}
.u-modal{
	.u-modal__content{
		padding: 12px 0 0 0 !important;
	}
}
.page{
    background: #F5F6F8;
    min-height: 100%;
    padding: 15px 10px;
	// display: flex;
	// flex-direction: column;
	
	.btn{
		min-width: 120px;
		font-size: 14px;
		font-weight: 600;
		min-height: 40px;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 10px 15px;
		cursor: pointer;
		color: #FFFFFF;
		background: #FF843A;
		border-radius: 4px;
		text-align: center;
	}
	
    .uploader_list{
        display: flex;
        .infoItem{
            position: relative;
            margin-right: 10px;
        }
        .pdf{
            padding: 15px;
        }
    }
}



.pd100{
    padding-bottom: 80px !important;
}
.mt15{
    margin-top: 15px;
}
.mt20{
    margin-top: 20px;
}
.mt10{
    margin-top: 10px;
}
.ml20{margin-left: 20px;}
.mr20{margin-right: 20px;}

.btn_add{
    min-width: 132px;
    height: 38px;
    background: #849EB2;
    box-shadow: 0px 4px 20px 0px rgba(15,66,105,0.3);
    border-radius:4px;
    color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    margin: 0 auto;
    width:fit-content;
    padding: 0 15px;
	margin-top: 10px;
    text{
        margin-left: 5px;
    }
}


.gmRead{
    font-size: 14px; line-height: 16px;
}

.sel_add{
    text-align: center;
    padding: 10px 0;
}

.iframe_tit{
    font-size: 18px;
    font-weight: 600;
    color: #333333;
    padding: 15px 0 10px 0;
    text-align: center;
    position: sticky;

}
.iframe_Info{
    height: calc(100% - 50px);
    overflow-y: scroll;
	-webkit-overflow-scrolling: touch
}
.head_status{
    images{
        width: auto;
    }
	&.bg{
		background: #F5F6F8;
		border-radius: 0 0 0 10px;
		padding: 4px 10rpx;
		color: #666;
	}
}

view,div{
	box-sizing: border-box;
}

.u-form-item__body__left__content__required{
	position: relative !important;
	left: 0 !important;
}
.u-form-item__body__right{
	box-shadow: 0 1px 4px #0000000d;
	border-radius: 4px;
	opacity: 1;
	border: 1px solid #E2E6F6;
	padding: 4px 8px;
	min-height: 45px;
}
.u-search__content__input--placeholder{
	font-size: 12px;
}
.showarea{
	padding: 6px 9px;
}
.readonly{
	color: var(--placeholder-color);
	font-size: 14px;
}
.u-list__finished-text{
	// font-size: 12rpx;
	text-align: center;
	margin: 10px auto;
	color: #999;
}
.u-divider{
	margin: 0 !important;
}
.u-modal__content{
	width: 100%;
}
.u-popup__content{
	overflow: hidden;
}
.u-transition.u-fade-enter-to.u-fade-enter-active {
    z-index: 90 !important;
}
.u-transition{
	z-index: 990 !important;
}

   
    .pop_info{
        background: #F5F6F8;
        height: 100%;
        &_tit{
            font-size: 18px;
            font-weight: 600;
            color: #050D33;text-align: center;
            padding: 30px 0 10px 0;
        }
    }
	
.card_head_tit2{
	font-size: 14px;
	font-weight: 600;
	border-left: 3px solid #849EB2;
	padding-left: 10rpx;
	line-height: 1;
	margin-bottom: 10px;
}