.page{
    background: #F5F6F8;
    min-height: 100%;
    padding: 0;
    --van-cell-group-inset-padding:0;
    --card-border-radius:5px;
    --card-padding:0 10px;
    --van-divider-line-height:1px;
    --van-divider-margin:0;

    --el-table-border:0;

    .card_info{
        max-width: 100%;
        padding: 10px;
        .van-swipe-cell{
            background: #669DF0;
            border-radius: var(--card-border-radius);
        }
        .card{
            background: #fff;
            padding: var(--card-padding);
            position: relative;
            border-radius: var(--card-border-radius);
            line-height: 1.4;
            .card_head{
                display: flex;
                align-items: center;
                // border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
                padding: 15px 0 10px 0;
                &_tit{
                    font-size: 18px;
                    font-weight: 600;
                    color: #333333;
                    label{
                        font-weight: 700;
                        font-size: 16px;
                        color: #000000;
                        margin-right: 5px;
                    }
                    .txt1{
                        font-size: 16px;
                    }
                    .txt2{
                        font-size: 12px;
                        color: #999999;
                    }
                }
                &_tit2{
                    display: flex;
                    align-items: center;
                    font-size: 16px;
                    font-weight: 600;
                    color: #333333;
                    margin: 15px 0 10px 0;
                }
                &_name{
                    background: #849EB2;
                    border-radius: 4px;
                    font-weight: 600;
                    color: #FFFFFF;
                    font-size: 14px;
                    width: 34px;
                    height: 34px;
                    text-align: center;
                    line-height:34px;
                    margin-right: 10px;
                }
                .head_status{
                    position: absolute;
                    right: 0;
                    top: 0;
                    .van-icon__image{
                        height: auto;
                    }
                }
            }
            .item{
                font-size: 14px;
                margin: 5px 0;
                display: flex;
                flex-direction:row;
                color: #333333;
                width: 100%;
                align-items: center;
                line-height: 1.5;
                .left{
                    // width: 140px;
                    color: #999999;
                    flex: 1;
					word-wrap: break-word;
					overflow: auto;
                }
                .rigth{
                    margin-left: auto;
                    flex: 1.5;
					word-wrap: break-word;
					overflow: auto;
					&.inlineflex{
						display:inline-flex;
					}
                    image{
                        width: 100%;
                    }
                    .tag_list{
                        // display: flex;
                        .tag{
                            display: inline-flex;
                            align-items: center;
                            margin-right: 10px;
                            line-height: 16px;
                            &_icon{
                                width: 16px;
                                height: 16px;
                                color: #fff;
                                background: #849EB2;
                                border-radius: 4px;
                                margin-right: 2px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                            }
                        }
                    }
                }
                .link{
                    color: #849EB2;
                    border-bottom: 1px solid #849EB2;
                    padding-bottom: 2px;
                }
            }
            
			.card_main{
                padding: 10px 0;
                // border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
                .tel{
                    display: inline-flex;
                    align-items: center;
                    span{
                        font-weight: 700;
                        font-size: 16px;
                        color: #000000;
                        margin-right: 5px;
                    }
                }
            }
            .card_footer{
                padding: 5px 0;
                position: relative;
                .link{
                    position:absolute;
                    color: #849EB2;
                    right: 10px;
                    top:50%;
                    transform:translate(0%,-50%);
                    -webkit-transform:translate(0%,-50%);
                    -moz-transform:translate(0%,-50%);
                }
            }
        }
        .card_right{
            display: flex;
            align-items: center;
            justify-items: center;
            height: 100%;
            padding: 0 30px;
            .card_right_btn{
                text-align: center;
                font-size: 12px;
                color: #FFFFFF;
                .txt{
                    margin-top: 5px;
                }
            }
        }
    }

}
 //相关列表
    .other{
        padding: 10px;
        margin-top: 10px;
        &_tit{
            margin-bottom: 10px;
            font-weight: 600;
            color: #333333;
            font-size: 18px;
        }
        &_list{
            padding-top: 5px;
            &_itme{
                margin-bottom: 10px;
                background-color: #fff;
                border-radius: var(--card-border-radius);
                padding: 10px;
                display: flex;
                align-items: center;
                font-weight: 600;
                color: #333333;
                font-size: 16px;
                min-height: 45px;
                label{
                    margin-left: 10px;
                }
                .link{
                    margin-left: auto;
                    color: #E0E0E0;
                }
            }
        }
    }

    //相关列表弹出
    .related{
        background: #F5F6F8;
        // min-height: 100%;
        height: 100vh;
		width: 80vw;
		position: relative;
		display: flex;
		flex-direction: column;
        .addbox{
            // position: fixed;
            margin-bottom: 20px;
            width: calc(100% - 20px);
            text-align: center;
			display: flex;
			margin-top: auto;
        }
        &_tit{
            padding: 25px 0 15px 0;
            background: #F5F6F8;
            font-size: 16px;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
            color: #333333;
            text-align: center;
        }
		.scrollHeight{
			height:100% ;
			// height: calc(100vh - 145px);
		}
        &_list{
           
            line-height: 1.6;
            max-height: calc(100% - 65px);
            overflow-y: hidden;
            // padding-bottom: 70px;
			position: relative;
            &.iframe{
                height: calc(100% - 70px );
            }
            &_itme{
                padding: 10px;
                display: flex;
                align-items: center;
				background: #fff;
                &_tit{
                    color: #333333;
                    font-size: 15px;
                    font-weight: 600;
                }
                &_left{
                    font-weight: 400;
                    color: #999999;
                    font-size: 12px;
                }
                &_rigth{
                    margin-left: auto;
                }
                &_name{
                    background: #849EB2;
                    border-radius: 4px;
                    font-weight: 600;
                    color: #FFFFFF;
                    font-size: 14px;
                    width: 34px;
                    height: 34px;
                    text-align: center;
                    line-height:34px;
                    margin-right: 10px;
                }
            }
        }
    }
