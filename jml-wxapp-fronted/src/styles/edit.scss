.page{
    background: #F5F6F8;
    min-height: 100%;
    padding: 0;
    --van-cell-group-inset-padding:0;
    --card-border-radius:5px;
    --card-padding:0 10px;
    --van-divider-line-height:1px;
    --van-divider-margin:0;
    --van-field-label-width:auto;
    --el-table-border:0;
    .card_info{
        max-width: 100%;
        padding: 10px; 
        .card{
            background: #fff;
            padding: var(--card-padding);
            position: relative;
            border-radius: var(--card-border-radius);
            line-height: 1.4;
            .card_head{
                display: flex;
                align-items: center;
                // border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
                padding: 15px 0 10px 0;
                &_tit{
                    display: flex;
                    align-items: center;
                    font-size: 18px;
                    font-weight: 600;
                    color: #333333;
                    span{
                        font-weight: 700;
                        font-size: 16px;
                        color: #000000;
                        margin-right: 5px;
                    }
                }
                .head_status{
                    margin-left: auto;
                    color: #3D3D3D;
                    font-size: 12px;
                    font-weight: 600;
                    &.through{
                        color: #00A822;
                    }
                    &.refused{
                        color: #FF0000;
                    }
                }
            }
            .item{
                font-size: 14px;
                margin: 5px 0;
                display: flex;
                flex-direction:row;
                color: #333333;
                width: 100%;
                align-items: center;
                line-height: 1.5;
                .left{
                    width: 100px;
                    color: #999999;
                }
                .rigth{
                    margin-left: auto;
                    flex: 1;
                    .tag_list{
                        display: flex;
                        .tag{
                            display: flex;
                            align-items: center;
                            margin-right: 10px;
                            line-height: 16px;
                            &_icon{
                                width: 16px;
                                height: 16px;
                                color: #fff;
                                background: #849EB2;
                                border-radius: 4px;
                                margin-right: 2px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                            }
                        }
                    }
                }
                .link{
                    color: #849EB2;
                    border-bottom: 1px solid #849EB2;
                    padding-bottom: 2px;
                }
            }
            .info{
                font-size: 14px;
                // margin: 5px 0;
                display: flex;
                flex-direction:row;
                color: #333333;
                width: 100%;
                align-items: center;
                line-height: 1.5;
                .left{
                    // width: 60px;
                    color: var(--van-field-label-color);
                    &.required{
                        &:before {
                            margin-right: 2px;
                            color: var(--van-field-required-mark-color);
                            content: "*";
                        }
                    }
                }
                .rigth{
                    margin-left: auto;
                    flex: 1;
                }
            }
            .card_main{
                padding: 10px 0;
                // border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
                .tel{
                    display: inline-flex;
                    align-items: center;
                    span{
                        font-weight: 700;
                        font-size: 16px;
                        color: #000000;
                        margin-right: 5px;
                    }
                }
            }
            .card_footer{
                padding: 5px 0;
                position: relative;
                .link{
                    position:absolute;
                    color: #849EB2;
                    right: 10px;
                    top:50%;
                    transform:translate(0%,-50%);
                    -webkit-transform:translate(0%,-50%);
                    -moz-transform:translate(0%,-50%);
                }
            }
        }
        .card_right{
            display: flex;
            align-items: center;
            justify-items: center;
            height: 100%;
            padding: 0 30px;
            .card_right_btn{
                text-align: center;
                font-size: 12px;
                color: #FFFFFF;
                .txt{
                    margin-top: 5px;
                }
            }
        }
    }
    .detail{
		--van-cell-background:none;
		.van-collapse{
			background: #fff;
			border-radius: 5px;
		}
		.detail_tit{
			font-size: 18px;
			font-weight: 600;
			color: #333333;
			padding: 10px;
			
		}
		.detail_list{
	
		}
		.detail_add{
			color: #849EB2;
			text-align: center;
			margin-top: 10px;
		}
	}
    .detail_add{
        color: #849EB2;
        text-align: center;
        margin-top: 10px;
		width: 100%; padding: 10px;
		display: flex;
		align-items: center;
		justify-content: center;
    }
    .addInfo{
        padding: 10px 0;
        position: relative;
        border-radius: var(--card-border-radius);
        line-height: 1.4;
		width: 100%;
        .card{
            padding: 0 20px;
            background: none;
            // padding: var(--card-padding);
            position: relative;
            border-radius: var(--card-border-radius);
            line-height: 1.4;
            max-height: calc(100vh - 100px);
            overflow-y: scroll;
            .card_head{
                display: flex;
                align-items: center;
                justify-content: center;
                // border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
                padding: 15px 0 10px 0;
                &_tit{
                    display: flex;
                    align-items: center;
                    font-size: 18px;
                    font-weight: 600;
                    color: #333333;
                    span{
                        font-weight: 700;
                        font-size: 16px;
                        color: #000000;
                        margin-right: 5px;
                    }
                }
            }

            .info{
                font-size: 14px;
                // margin: 5px 0;
                display: flex;
                flex-direction:row;
                color: #333333;
                width: 100%;
                align-items: center;
                line-height: 1.5;
                .left{
                    // width: 60px;
                    color: var(--van-field-label-color);
                    &.required{
                        &:before {
                            margin-right: 2px;
                            color: var(--van-field-required-mark-color);
                            content: "*";
                        }
                    }
                }
                .rigth{
                    margin-left: auto;
                    flex: 1;
                }
            }
        }
        :deep(){
            .int{
                background: none !important;
                .van-field__value{
                    background: #fff;
                }
                // &.van-cell:after{
                //     content: none;
                // }
            }
			
        }

    }
    .iframeAddInfo{
        height: 100%;
        // position: relative;
        z-index: 9;
        overflow: scroll;
        position: absolute; 
		width: 100%; 
		height: 100%; top: 0;left:0;
        &.iframe{
            // height: calc(100% - 70px );
        }
    }
    :deep(){
        .int{
            padding: 0;
            padding-top: 20px;
            &:first-child{
                padding-top: 0px;
            }
            &:last-child{
                padding-bottom: 20px;
            }
            &.pt0{
                padding-top: 0px;
            }
            .van-field__value{
                box-shadow: 0px 1px 4px 0px rgba(0,0,0,0.05);
                border-radius: 4px;
                opacity: 1;
                border: 1px solid #E2E6F6;
                padding: 8px;
            }
            &.van-cell:after{
                content: none;
            }
        }
    }
}
