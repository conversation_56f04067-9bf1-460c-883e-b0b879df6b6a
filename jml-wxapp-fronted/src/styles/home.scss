
.container {
	background: #F5F6F8;
	padding:0 10px;
	.leftmenu{
		background: #F5F6F8;
		height: 100%;
		width: 440upx;
		padding-top: 80rpx;
		&_tit{
			font-size: 16px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 600;
			color: #1F1F1F;
			text-align: center;
			height: 80upx;
			line-height: 80upx;
			
		}
		.leftmenu_box{
			background: #fff;
			padding: 10px 10upx 10upx 50upx;
			
			.leftmenu_item{
				display: flex;
				padding: 30upx 16upx;
				border-bottom: 1px solid #E0E0E0;
				text{
					font-weight: 600;
					color: #333333;
					margin-left: 20upx;
					font-size: 16px;
				}
				.menu_left{
					margin-left: auto;
				}
			}
		}
	}
	.notice{
		padding: 10px 10px;
		.tit{
			font-weight: 600;
			color: #8EA0B6;
			padding: 10px 0 15px 0;
			font-size: 16px;
			
		}
		.list{
			margin: 0px;
			.item{
				color: #8EA0B6;
				padding: 10px 0;
				font-size: 14px;
				display: flex;
				&.act{
					color: #364250;
				}
				&.link{
					// border-bottom: 1px solid #C8D2E3;
					// display: inline-block;
				}
				// border-bottom: 1px solid #C8D2E3;
				span,label{
					margin-left: 10px;
				}
				.content{
					margin-left: 20px;
				}
			}
		}
	}
	// 日历样式
	.calendar{
	    padding: 0 10px;
	    background: #FFFFFF;
	    border-radius: 8px;
	    margin-top: 20px;
	    :deep(.calendar_list){
			.calendar_title{
			    padding: 5px 15px;
			    color: #000;font-size: 16px;font-weight: 600;
			    display: flex;
			    justify-content: center;
			    align-items: center;
			    .add_btn{
			        margin-left: auto;
			        // padding: 10px 15px;
			        display: flex;
			        justify-content: center;
			        align-items: center;
			        text{
			            font-size: 14px;
			            margin-left: 5px;
			            line-height: 24px;
			        }
			    }
			}
		}
	}
	.calendar{
		:deep(.uni-calendar__content){
			background: none;
			.calendar_tool{
				margin-right: auto;
			}
			.uni-calendar-item__weeks-box-item{
				height: 3.75rem;
			}
			.uni-calendar-item--isDay,.uni-calendar-item--checked{
				background: var(--main-selected-c);
				border-radius: 8px;
				color: #fff;
				.uni-calendar-item--extra{
					color: var(--main-txt-color);
				}
				.uni-calendar-item--isDay-text{
					color: #fff;
				}
			}
			.uni-calendar-item--isDay-text{
				font-weight: 600;color: var(--main-txt-color);
			}
			.uni-calendar__header{
				color: var(--main-txt-color);
				color: #000;font-size: 16px;font-weight: 600;
			}
			.uni-calendar-item--extra{
				color: var(--main-txt-color);
			}
			.uni-calendar-item__weeks-box-circle{
				background-color: green;
			}
			.uni-calendar__backtoday{
				// position: relative;
				top: auto;
			}
			.uni-calendar-item__weeks-box-item{
				width: auto;
			}
		}
	}
	.title{
		padding: 5px 0px;
		color: #364250;
		font-size: 16px;font-weight: 600;
	}
	
}

	.menu{
		background: #FFFFFF;
		border-radius: 8px;
		padding: 20px 20px 0 20px;
		&_title{
			padding: 5px 0px;
			color: #364250;
			font-size: 16px;font-weight: 600;
		}
		&_list{
			display:flex;
			align-items:center;
			// justify-content: space-between;
			flex-wrap:wrap;

			.menu_item {
				// float: left;
				width: 33.33%;
				padding: 20px 0;
				text-align: center;
				border: 1px solid #ddd;
				border-top: 0;
				border-left: 0;
				background-color: #fff;
				position: relative;
				&.last{
					border-bottom: 0;
				}
				&:nth-child(3n){
					border-right: 0;
				}
				.menu_info_title{
					margin: 10px auto;
					font-size: 14px;
					font-weight: 600;
					color: #1F1F1F;
				}
			}
		}
	}


	.img-info {
		image {
			// width:100%;
			// height:auto;
			// margin-bottom: 20rpx;
		}
	}
	.tool{
		background-color: #fff;
		text-align: center;
		// position: fixed;
		padding-top: 80upx;
		left: 0;
		width: 100%;
		z-index: 9;
		.tool_main{
			// border-top: 1upx solid #999;
			width: 100%;
			height: 80upx;
			// flex-direction: column;
			justify-content: flex-start;
			margin-left: 15px;
			align-items: center;
			display: flex;
		}
		.menu{
			width: 100upx;
			display: flex;
			justify-content: center;
			height: 100%;
		}
		.title{
			font-size: 16px;
			font-family: PingFang SC, PingFang SC;
			font-weight: 600;
			color: #1F1F1F;
			text-decoration: none;
			align-self:center;
			flex: 1;
			padding-right: 100upx;
		}
	}
	
	.powered{
	
		padding:20px 0px;
		font-size: 12px;
		color: #ccc;
		text-align: center;
		margin: 0 auto;
	}
	.check-in{
		border: 1px solid #DCDCDC;
		width: 66px;
		height: 46px;
		background: #FFFFFF;
		border-radius: 24px 0 0 24px;
		display: flex;
		align-items: center;
		justify-content: center;
		.btn_txt{
			font-size: 12px;
			font-weight: bold;
			line-height: 1.3;
			color: rgba(0,0,0,0.9);
			margin-left: 3px;
		}
	}
