/* #ifdef H5 */
uni-page-head[uni-page-head-type='default'] ~ uni-page-wrapper{
	.page{
		height: 100%
	}
	.card_list{
		height: calc(100% - 65px ) !important
		// height: calc(100% - 65px - 50px - var(--window-bottom) ) !important;
	}
}
/* #endif */

.page{
    background: #F5F6F8;
    min-height: 100%;
    padding: 0;
    
    --van-search-padding: 0px;
    --main-bg-c: #fff;

    .head{
        display: flex;
        font-size: 14px;
        font-weight: 400;
        color: #3D3D3D;
        align-items: center;
        padding:10px 15px;
        background: var(--main-bg-c);
		position:sticky;z-index:970;top:0px;
        .title{
            font-size: 18px;
            color: #000;
            font-weight: 600;
        }
        .qty{
            margin-right: 10px;
        }
		.tool{
			width: auto;
			padding: 0 10upx;
            .item{
                margin-right: 10px;
            }
		}
        .search{
            flex: 1;
        }
        .search_btn{
            margin-left: 15px;
            display: flex;
            justify-content: center;
            font-size: 14px;
            font-weight: 600;
            height: 100%;
        }
        text{
            margin: 0 5px;
        }
        .right{
            margin-left: 10px;
        }
    }

    .card_list{
        max-width: 100%;
        margin:  10px 0;
        padding: 10px 0;
		flex-grow: 1;
        position: relative;
		width: 100%;
		overflow: hidden;
		height: calc(100vh - 65px - var(--window-bottom));
		.swiper-item{
			height: 100%;
		}
		&.isComponent{
			height: calc(100% - 65px - var(--window-bottom))
		}
		// height: calc(100vh - 100px - var(--window-bottom));
        .van-swipe-cell{
            background: #669DF0;
            border-radius: var(--card-border-radius);
			margin: 0 10px;
            margin-bottom: 10px;
            &.approve-swipe-cell{
                background: transparent !important;
            }
        }
        .tool{
            // position: absolute;
            // right: 0px;
            display: flex;
            font-size: 12px;
            height: var(--van-tab-line-height);
            align-items: center;
            justify-content: flex-end;
            color: #373D5C;
            font-weight: 600;
            z-index: 99;
			position: relative;
            .item{
                margin-right: 10px;
                align-items: center;
                display: flex;
				justify-content: center;
                .text{
                     margin-right: 3rpx;
                }
                &:last-child{
                    margin-right: 0;
                }
            }
        }
        .card{
            background: #fff;
            padding:0 10px;
            position: relative;
            border-radius: var(--card-border-radius);
            line-height: 1.4;
            .card_head{
                display: flex;
                align-items: center;
                // border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
                padding: 15px 0 10px 0;
                &_tit{
                    font-size: 18px;
                    font-weight: 600;
                    color: #333333;
                    text{
                        font-weight: 700;
                        font-size: 16px;
                        color: #000000;
                        margin-right: 5px;
                    }
                    .txt1{
                        font-size: 16px;
                    }
                    .txt2{
                        font-size: 12px;
                        color: #999999;
                    }
                }
                &_name{
                    background: #849EB2;
                    border-radius: 4px;
                    font-weight: 600;
                    color: #FFFFFF;
                    font-size: 14px;
                    width: 34px;
                    height: 34px;
                    text-align: center;
                    line-height:34px;
                    margin-right: 10px;
                }
                .head_status{
                    position: absolute;
                    right: 0;
                    top: 0;
                    .van-icon__image{
                        height: auto;
						width: 40px;
                    }
                }
            }
            .item{
                font-size: 14px;
                margin: 5px 0;
                display: flex;
                flex-direction:row;
                color: #333333;
                width: 100%;
                align-items: center;
                .left{
                    width: 100px;
                    color: #999999;
                }
                .rigth{
                    margin-left: auto;
                    flex: 1;
					display: inline-flex;
					.u-transition{
						display: inline-flex !important;
					}
                }
            }
            .card_main{
                padding: 10px 0;
                // border-bottom: 1px rgba(216, 216, 216, 0.5) solid;
                .tel{
                    display: inline-flex;
                    align-items: center;
                    text{
                        font-weight: 700;
                        font-size: 16px;
                        color: #000000;
                        margin-right: 5px;
                    }
                }
            }
            .card_footer{
                padding: 5px 0;
                position: relative;
                .link{
                    position:absolute;
                    color: #849EB2;
                    right: 0px;
                    bottom:10px;
					display: flex;
					view{
						margin-left: 10upx;
					}
                    // transform:translate(0%,-50%);
                    // -webkit-transform:translate(0%,-50%);
                    // -moz-transform:translate(0%,-50%);
                }
            }
        }
        .card_right{
            display: flex;
            align-items: center;
            justify-items: center;
            height: 100%;
            
			background: #669DF0;
			border-radius:0 var(--card-border-radius) var(--card-border-radius) 0;
            flex-direction: column;
            .card_right_btn{
                display: flex;
                flex-direction: column;
                justify-content: center;
                text-align: center;
                font-size: 12px;
                color: #FFFFFF;
				padding: 30px;
                flex: 1;
                .txt{
                    margin-top: 5px;
                }
            }
        }
        .card_left{
            display: flex;
            align-items: center;
            justify-items: center;
            height: 100%;
            
            background: #EABBAC;
			border-radius:var(--card-border-radius) 0 0 var(--card-border-radius);
            .card_left_btn{
                text-align: center;
                font-size: 12px;
                color: #FFFFFF;
				padding: 0 30px;
                .txt{
                    margin-top: 5px;
                }
            }
        }
        .card_approve{
            display: flex;
            align-items: center;
            justify-items: center;
            height: 100%;
            padding: 0px;
            background: #88D39B;
            border-radius: var(--card-border-radius);
            .card_right_btn{
                display: flex;
                flex-direction: column;
                justify-content: center;
                text-align: center;
                font-size: 12px;
                color: #FFFFFF;
                height: 100%;
                padding: 30px;
                border-radius:0 var(--card-border-radius) var(--card-border-radius) 0;
                &.refuse{
                    background: #EB7155;
                }
                &.agree{
                    // background: #88D39B;
                }
				&.accept{
				    background: #4085CB;
				}
                .txt{
                    margin-top: 5px;
                }
            }
        }
    }
    .card_tool{
        // height: 40px;
        position: relative;
		padding: 0 10px;
		.tool{
			padding: 0;
			background-color: transparent;
		}
    }

}
