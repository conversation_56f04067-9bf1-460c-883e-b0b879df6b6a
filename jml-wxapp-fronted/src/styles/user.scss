.page{
		background-color: rgba(244, 247, 250, 1);
		padding: 40upx 56upx 0upx 56upx;
		min-height: 100%;
		.head{
			padding: 100rpx 0;
			.title{
				font-size: 60rpx;
				font-weight: 900;
				color: #333333;
				position: relative;
				z-index: 9999;
			}
			.logo{
				position: absolute;
				right: -50upx;
				width: 400upx;
				top: 50upx;
				z-index: 1;
			}
		}
		.mian{
			position: relative;
			// margin-top: -15px;
			// background-color: #fff;
			border-radius: 30upx;
			z-index: 9999;
			
			text-align: center;
			flex: 1;
			display: flex;
			flex-direction: column;
			.itme{
				margin: 30upx auto;
				text-align: left;
				width: 100%;
				.lable{
					font-size: 32upx;
					font-family: PingFang SC-Regular, PingFang SC;
					font-weight: 400;
					color: #232A51;
					line-height: 14px;
				}
				.inp{
					margin-top: 20rpx;
					.inp_txt{
						color: #232A51;
						font-size: 14px;
						background: #fff;
						border-radius: 10px 10px 10px 10px;
						padding:30rpx 30rpx;
						height: auto;
					}
				}
				.check{
					color: #101010;
				}
			}
			.btn_login{
				width: 100%;
				height: 54px;
				background: rgba(132, 158, 178, 0.30);
				box-shadow: 0px 6px 10px 0px rgba(132,158,178,0.1);
				border-radius: 10px 10px 10px 10px;
				margin: 10% auto 0 auto;
				line-height: 54px;
				font-size: 16px;
				font-family: PingFang SC-Semibold, PingFang SC;
				font-weight: 600;
				color: #FFFFFF;
				text-align: center;
				&.isOK{
					background: #849EB2;
					box-shadow: 0px 16px 20px 0px rgba(132,158,178,0.35);
				}
			}
		}
		.resetpwd_btn{
			font-family: PingFang SC, PingFang SC;
			font-weight: 400;
			color: #849EB2;
			line-height: 20px;
			margin-top: 70upx;
		}
	}