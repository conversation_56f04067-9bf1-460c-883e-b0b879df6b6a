import { createRouter } from 'uni-mini-router'
import pagesJson from '../pages.json'
import pagesJsonToRoutes from 'uni-parse-pages'


const router = createRouter({
  routes: [...pagesJsonToRoutes(pagesJson)]
})
// const router = createRouter({
//   routes: [...ROUTES] // 路由表信息
// })

//全局路由前置守卫
// debugger
// console.log('router',ROUTES)
//@#ifdef MP-WEIXIN
router.beforeEach(async(to:any, from:any, next) => {
	// debugger
	const userStore = useUserStore();
	const appStore = useAppStore();
	const userInfo = storeToRefs(userStore)
	const { userId,token } = userStore;
	// const { keepAliveExcludes } = storeToRefs(appStore)
	// if(from.meta && from.meta.keepAlive && !keepAliveExcludes.value.includes(from.name)){
	//       keepAliveExcludes.value.push(from.name)
	// }
	// if(to.name!='home'){
	// 	  appStore.clearAllExcludes()
	// }
	if ((to.meta.workAuth && (!userId || userId=='') && to.name !== 'login')) {
	  // 如果没有登录信息且目标路由不是登录页面则跳转到登录页面
		next({ name: 'login', navType: 'replaceAll' })
	} else if (userId && to.name === 'login') {
	  // 如果已经登录且目标页面是登录页面则跳转至首页
		next({ name: 'home', navType: 'replaceAll' })
	}else if( to.name === 'login'){
		try{
			const miniUserInfo = await userStore.getMiniUserInfo();
			if(!miniUserInfo || !miniUserInfo.userInfo){
				next()
			}else{
				next({ name: 'home', navType: 'replaceAll' })
			}
		}catch{
			next()
		}
	}else{
		await goto(to, from, next)
	}
})

// 全局路由后置守卫
router.afterEach(async(to:any, from:any) => {
	// debugger
	const userStore = useUserStore();
	const appStore = useAppStore();
	const { userId,token,menuList } = userStore;
	if ((to.meta.workAuth && (!userId || userId=='') && to.name !== 'login')) {
	  // 如果没有登录信息且目标路由不是登录页面则跳转到登录页面
	  router.replaceAll({ name: 'login' })
	} else if (userId && to.name === 'login') {
	  // 如果已经登录且目标页面是登录页面则跳转至首页
	  router.replaceAll({ name: 'home' })
	}else if( to.name === 'login'){
		const miniUserInfo = await userStore.getMiniUserInfo();
		// debugger
		if(miniUserInfo && miniUserInfo.userInfo){
			router.replaceAll({ name: 'home' })
		}
	}
    console.log('跳转结束')
})

const goto = async(to, from, next) => {
	const userStore = useUserStore();
	const appStore = useAppStore();
	// debugger
	const { userId,token,menuList,userInfo } = userStore;
	if(!menuList){
		try {
			let a = await userStore.getUserRole()
			if(userInfo?.isFactory==1){
				let fa =	await userStore.getFactoryMenuList();
				console.log('fa',fa)
			}
			next()
		} catch (error) {
			next(false)
			return uni.showToast({ title: '获取个人权限失败~', icon: 'none' })
		}
	}else{
		next()
	}
}
//@#endif

export default router