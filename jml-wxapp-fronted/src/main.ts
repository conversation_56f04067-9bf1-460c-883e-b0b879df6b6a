import { createSSRApp } from "vue";
import App from "./App.vue";
import router from './router'
import pinia from '@/store/pinia';
import uviewPlus from 'uview-plus';
import util from "./utils/util";
import * as lodash from 'lodash';
import config from '@/common/config.js'
import { comomMixin } from "@/mixin/comom.mixin.js";
import '@/styles/base.scss';
export function createApp() {
  const app = createSSRApp(App);
  app.mixin(comomMixin)
  .use(router)
  .use(pinia)
  .use(uviewPlus, () => {
		return {
			options: {
				config: {
					// 修改默认单位为rpx，相当于执行 uni.$u.config.unit = 'rpx'
					color: {
						'u-primary': '#19be6b',
						'up-primary': '#19be6b',
					}
				}
			}
		}
	});
  app.provide('util',util)
  app.provide('lodash',lodash)
  app.config.globalProperties.config = config;
  app.config.globalProperties.util = util;
  
  // app.config.errorHandler = (err, instance, info) => {
	 //  return uni.showToast({ title:`出现错误拉，${info}`, icon: 'none' })
  // }
  
  return {
    app,
	pinia
  };
}


