import config from '@/common/config.js'
export const comomMixin  = {
    data() {
        return{
            imgBase:import.meta.env.VUE_APP_BASE_IMG,
			baseURL:import.meta.env.VITE_BASE_URL,
        }
    },
	created() {

	},
	methods: {
		showImagePreview:function(imgs,index=0){
			uni.previewImage({
				urls:imgs,
				current:index
			})
		}
	},
	computed: {
        ...mapState(useUserStore, ['token','userId','userInfo','factoryMenuList']),
        ...mapState(useDictStore, ['dict']),
		...mapState(useUserStore, ['miniInfo']),
        // ...mapState(useAppStore, ['keepAliveExcludes']),
    //     ...mapState(useDictStore, {
    //         // 这里有问题【vue-async-computed】
    //         dictGet: store => async (key) =>{
				// debugger
    //             return await store.getDict(key)
    //         }
    //     }),
		// dictGet: async()=>{
		// 	const store = useDictStore();
		// 	return await store.getDict(key)
		// },
		// ...mapState($uStoreKey),
        hasPermission : () => {
            return function (menu_code,function_code) {
				// if(!obj) return false;
				const store = useUserStore();
				const {userId,token,menuList } = store;
				//#ifdef H5
				//方便调试放这里的
				return true;
				//#endif
				// const {menu_code,function_code} = obj
				if (!userId || !menuList) {
					return false;
				}else{
					let check = menuList.find(item => item.menu_code == menu_code && item.function_code == function_code)
					return check ? check : false;
				}
                // if(Array.isArray(value)){
                //     return value.some(item => store.getters.permissions.hasOwnProperty(item))
                // }else{
                //     return store.getters.permissions.hasOwnProperty(value)
                // }
            }
        },
		hasFactoryPermission : () => {
		    return function (key,function_code) {
				const userStore = useUserStore();
				const {factoryMenuObj } = userStore;
				// const {menu_code,function_code} = obj
				// debugger
				if (!factoryMenuObj) {
					return false;
				}else{
					let check = factoryMenuObj[key]?.func.split(',').includes(function_code)
					return check ? check : false;
				}
		    }
		}
	},
    mounted () {
    }
}