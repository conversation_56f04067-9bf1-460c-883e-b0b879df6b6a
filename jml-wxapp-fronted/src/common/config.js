console.log(import.meta.env)

	let baseUrl = import.meta.env.VITE_BASE_URL;
	let imgBase = import.meta.env.VUE_APP_BASE_IMG;
	let staticUrl = import.meta.env.VUE_APP_BASE_STATIC;
	//#ifndef  H5
	const sysInfo = uni.getAccountInfoSync();
	console.log('sysInfo',sysInfo)
		debugger
	if(sysInfo?.miniProgram?.envVersion=='release'){
		baseUrl = 'https://wxapp-crm.chameleon-artec.com/jml'
		imgBase = "";
		staticUrl = "https://wxapp-crm.chameleon-artec.com/jml/assets/";
	}
	//#endif
let config = {
    baseURL:baseUrl,
    imgBase:imgBase,
	// static:'https://qa-hogo.rtm2.cn/hogo/upload/static/',
	static: staticUrl,
}
console.log(config)
export default config