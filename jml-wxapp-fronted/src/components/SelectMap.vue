
<template>
    <!--根据微信当前定位经纬度， 获取百度地图选择地址 -->
    <u-modal class="u-modal-aa" confirmColor="#849EB2" v-bind="$attrs" @confirm="confirm" @cancel="emit('cancel')" :show-confirm-button="!withUploader || (withUploader && resultInfo.files.length>0) ">
        <div  class="map_box" style="max-height: 70vh; overflow-y: aoto; overscroll-behavior: contain; padding-bottom: 100px;">
			<!-- <scroll-view class="map_box" scroll-y="true" style="max-height: 60vh; overflow-y: aoto; overscroll-behavior: contain; padding-bottom: 100px;"> -->
			<map 
				:enable-3D="enable3D"
				:show-compass="showCompass" 
				:enable-traffic="enableTraffic" 
				:show-location="showLocation" 
				:enable-indoorMap="enableIndoorMap"
				:enable-satellite="enableSatellite"
				:style="{ width: width , height: height + 'px' }" :latitude="resultInfo.location.latitude" :longitude="resultInfo.location.longitude">
				<!-- <cover-view class="myMap_map__cover-view">
					<cover-view class="myMap_map__cover-view_mapControls">
						<cover-view class="myMap_map__cover-view_mapControls_drawControl"></cover-view>
					</cover-view>
				</cover-view> -->
				
			</map>
            <div class="content">
				<u--form labelPosition="top" labelWidth="auto" ref="form1" >
					<u-form-item label="地区" prop="district" required>
						<zxz-uni-data-select clearable placeholder="请选择"  v-model="resultInfo.district" dataText="district" @change="areaChange" dataValue="district"  :localdata="userInfo.staffAreaList"></zxz-uni-data-select>
					</u-form-item>
					<u-form-item  v-if="withUploader" label="签到照片" >
						<u-upload
						    :maxCount="maxCount"
						    :capture="capture"
							:sizeType="['compressed']"
							@afterRead="afterRead"
							:previewFullImage="true"
						    :deletable="true"
							@delete="beforeDelete"
						    :fileList="uploader" >
						</u-upload>
					</u-form-item>
				</u--form>
            </div>
        <!-- </scroll-view> -->
		</div>
    </u-modal>
</template>
<script>
	export default {
		options: {
			// #ifdef MP-WEIXIN
			// 微信小程序中 options 选项
			multipleSlots: true, //  在组件定义时的选项中启动多slot支持，默认启用
			styleIsolation: "shared", //  启动样式隔离。当使用页面自定义组件，希望父组件影响子组件样式时可能需要配置。具体配置选项参见：微信小程序自定义组件的样式
			addGlobalClass: true, //  表示页面样式将影响到自定义组件，但自定义组件中指定的样式不会影响页面。这个选项等价于设置 styleIsolation: apply-shared
			virtualHost: true, //  将自定义节点设置成虚拟的，更加接近Vue组件的表现。我们不希望自定义组件的这个节点本身可以设置样式、响应 flex 布局等，而是希望自定义组件内部的第一层节点能够响应 flex 布局或者样式由自定义组件本身完全决定
			// #endif
		}
	}
</script>
<script setup>
import {asyncUpload,upload, getAddrByLngAndLat}  from  '@/api/common';
	const util = inject("util");
	const appStore = useAppStore();
	const prop =  defineProps({
		width: {
			type: [Number,String],
			default: '100%',
		},
		height: {
			type: [Number,String],
			default: 200,
		},
		withUploader:{
			type:Boolean,
			default:false
		},
		capture:{
			type:[Array,String],
			default:['album', 'camera']
		},
		showCompass: { //	是否显示指南针
			type: Boolean,
			default: true
		},
		enableTraffic: { //	是否开启实时路况
			type: Boolean,
			default: true
		},
		showLocation: { //	是否显示带有方向的当前定位点
			type: Boolean,
			default: true
		},
		enableIndoorMap: { //是否展示室内地图
			type: Boolean,
			default: false
		},
		enableSatellite: { //是否开启卫星图
			type: Boolean,
			default: true
		},
		enable3D: { //是否显示3D楼块
			type: Boolean,
			default: false
		},
	})
	const emit = defineEmits(['confirm','cancel'])
	const { userInfo } = useUserStore();
	const resultInfo = reactive({
		location:{
		},
		address:'',
		area_code:userInfo.staffAreaList.length==1 ? userInfo.staffAreaList[0].areaCode : null ,
		district:userInfo.staffAreaList.length==1 ? userInfo.staffAreaList[0].district : null ,
		files:[]
	})
	const areaChange =(value)=>{
		resultInfo.area_code = userInfo.staffAreaList.find(i=>i.district===value)?.areaCode;
	}
	const confirm = () => {
		if(prop.withUploader && resultInfo.files.length==0) return uni.showToast({
			title: '请上传附件',
			duration: 1500,
			icon:'none'
		}); 
		if(!resultInfo.area_code) return uni.showToast({
			title: '请选择区域',
			duration: 1500,
			icon:'none'
		}); 
		emit('confirm',resultInfo);
	}
	const reset = ()=>{
		resultInfo.files=[]
		uploader.value = []
	}
	const uploader = ref([])
	watch(() => prop.show,val => {
			val && open()
		},{ immediate: true }
	)
	const open = () =>{
		nextTick(()=>{
			uni.getLocation({
				type: 'gcj02',
				altitude:true,
				geocode:true,
				isHighAccuracy:true,
				success: function (res) {
					console.log('当前位置的经度：' + res.longitude);
					console.log('当前位置的纬度：' + res.latitude);
					resultInfo.location = res
					getAddrByLngAndLat(res.longitude,res.latitude).then((addres)=>{
						resultInfo.address = addres.data
					})
				},
				fail:()=>{
							// 如果用uni.chooseLocation没有获取到地理位置，则需要获取当前的授权信息，判断是否有地理授权信息
					uni.getSetting({
						success: (res) => {
							console.log(res);
							var status = res.authSetting;
							if(!status['scope.userLocation']){
							// 如果授权信息中没有地理位置的授权，则需要弹窗提示用户需要授权地理信息
								uni.showModal({
									title:"是否授权当前位置",
									content:"需要获取您的地理位置，请确认授权，否则地图功能将无法使用",
									success:(tip)=>{
										if(tip.confirm){
										// 如果用户同意授权地理信息，则打开授权设置页面，判断用户的操作
											uni.openSetting({
												success:(data)=>{
												// 如果用户授权了地理信息在，则提示授权成功
													if(data.authSetting['scope.userLocation']===true){
														uni.showToast({
															title:"授权成功",
															icon:"success",
															duration:1000
														})
														// 授权成功后，然后再次chooseLocation获取信息
														uni.getLocation({
															type: 'gcj02',
															altitude:true,
															geocode:true,
															isHighAccuracy:true,
															success: function (res) {
																console.log('当前位置的经度：' + res.longitude);
																console.log('当前位置的纬度：' + res.latitude);
																resultInfo.location = res
																getAddrByLngAndLat(res.longitude,res.latitude).then((addres)=>{
																	resultInfo.address = addres.data
																})
															},
														})
													}else{
														uni.showToast({
															title:"授权失败",
															icon:"none",
															duration:1000
														})
													}
												}
											})
										}
									}
								})
							}
						},
						fail: (res) => {
							uni.showToast({
								title:"调用授权窗口失败",
								icon:"none",
								duration:1000
							})
						}
					})
				}
			});
				
		})

	}

    const maxCount = 2
    const afterRead = async(event) =>{

        let files = [].concat(event.file)
		if(!uploader.value) uploader.value = [];
        let fileListLen = uploader.value.length
        files.map((item) => {
        	uploader.value.push({
        		...item,
        		status: 'uploading',
        		message: '上传中'
        	})
        })
		for (let i = 0; i < files.length; i++) {
			// 第一种得先转文件流
			// 第二种使用uni自带的
			try {
				const {data} = await asyncUpload(files[i].url)
				let item = uploader.value[fileListLen];
				uploader.value.splice(fileListLen, 1, {
				  ...item,
				  status: 'success',
				  message: '',
				  url: data.url,
				});
				fileListLen++;
				if(files.length==i+1)	resultInfo.files = uploader.value
			} catch (e) {
				console.log(e)
				uni.showToast({
					title: "图片上传失败",
					icon: "none"
				})
			}
		}
		  
   //      files.forEach(async(item,index) => {
			// try {
			// 	debugger
			// 	// 第一种得先转文件流
			// 	// 第二种使用uni自带的
			// 	const {data} = await asyncUpload(item.url)
			// 	let item = uploader.value[fileListLen];
			// 	uploader.value.splice(fileListLen, 1, {
			// 	  ...item,
			// 	  status: 'success',
			// 	  message: '',
			// 	  url: data,
			// 	});
			// 	fileListLen++;
			// 	resultInfo.files.push(data.url)
			// } catch (e) {
			// 	console.log(e)
			// 	uni.showToast({
			// 		title: "图片上传失败",
			// 		icon: "none"
			// 	})
			// }
   //      });
    }


    const beforeRead = (files) => {
		//好像不需要了
        let quality=1
        if(files.size<3000*1000){ //小于1M
            quality=0.8
        }else if(files.size<5000*1000){ //小于5M
            quality=0.5
        }else if(files.size<10000*1000){ //小于10M
            quality=0.3
        }else if(files.size>10000*1000){ //大于10M
            quality=0.1
        }
        return new Promise((resolve,reject) => {
			uni.compressImage({
			  src: files,
			  quality: 80,
			  success: (result) => {
				  debugger
					resolve(result)
			  },
			  error(err) {
			      reject(err)
			      console.log(err.message);
			  },
			})
        });
    };
    const beforeDelete = ({file,index}) =>{
		debugger
		uni.showModal({
			title: '提示',
			content: '是否删除该附件?',
			success: function (res) {
				if (res.confirm) {
					uploader.value.splice(index, 1)
					resultInfo.files.splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
    }

	onMounted(() => {
		// uni.chooseLocation({
		// 	success:(res)=> {
		// 		console.log(res);
		// 		// this.getRegionFn(res);
		// 	},
		// 	fail:(err)=>{
		// 		debugger
		// 	}
		// })
		// return

	})
	defineExpose({
		reset
	})
</script>

<style lang="scss">
	.u-modal{
		padding: 0 !important;
		.u-modal__content{
			padding: 0 !important;
		}
	}
    .map_box{
        padding: 10px;
		width: 100%;
    }
</style>
