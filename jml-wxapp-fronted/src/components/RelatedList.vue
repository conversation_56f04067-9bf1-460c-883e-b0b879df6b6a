
<template>
    <!-- 相关列表 -->
    <div class="other" v-if="relatedList && relatedList.length>0">
        <div class="other_tit">{{title}}</div>
        <div class="other_list">
            <div v-for="(item,index) in relatedList" :key="index" class="other_list_itme" @click="showRelated(item)">
                <up-icon size="20" v-if="item.icon_img"  :name="item.icon_img" />
                <label>{{ item.related_label }}({{ item.cnt }})</label>
                <view class="link"><up-icon name="arrow-right"  size="12" /></view>
            </div>
        </div>

        <u-popup
        v-model:show="showLeft"
        mode="left"
		closeable
		closeOnClickOverlay
        :customStyle="{ height: '100%' }"
		@close="showLeft=false"
        >
            <div class="related">
                <div class="related_tit">{{ current.related_label }}({{ current.cnt }})</div>
                <div class="related_list">
                    <!-- <u-list
                        v-model:loading="recordData.loading"
                        :finished="recordData.finished"
                        :finished-text="finishedText"
                        :immediate-check="false"
						@scrolltolower="getDataPage"
                    > -->
					<scroll-view
						@scrolltolower="getDataPage()" 
						class="scrollHeight"
						scroll-y="true"
						>
                        <template  v-for="(item,index) in recordData.list" :key="index">
                            <div class="related_list_itme" @click="handDetail(item)">
                                <div v-if="current.relatedHeadJson && item[current.relatedHeadJson.field] && item[current.relatedHeadJson.field].length>0" class="related_list_itme_name">{{ item[current.relatedHeadJson.field].substr(0,1) }}</div>
                                <div class="related_list_itme_left">
                                    <div v-if="current.relatedTitleJson" class="related_list_itme_tit">
                                        {{ current.relatedTitleJson.label }}：{{ item[current.relatedTitleJson.field] }}
                                    </div>
                                    <template v-if="current.relatedFieldJsonArray">
                                        <div v-for="(info,key) in current.relatedFieldJsonArray" :key="key" class="related_list_itme_txt">{{info.label}}：{{item[info.field]}}</div>
                                    </template>
                                </div>
                                <div class="related_list_itme_rigth"><u-icon name="arrow-right" size="12" /></div>
                            </div>
                            <u-divider></u-divider>
                        </template>
						<div v-if="recordData.finished" class="u-list__finished-text">{{finishedText}}</div>
						<div class="empty_box" v-if="!recordData.loading && (!recordData.list ||  recordData.list.length<=0)">
							<u-empty text="暂时没有数据"  :icon="`${config.static}images/empty/data.png`" ></u-empty>
						</div>
                    <!-- </u-list> -->
					</scroll-view>
                    
                </div>
				<div v-if="current.add_route_url"  class="addbox">
				    <div class="btn_add" @click="handAdd"><u-icon size="20" name="/static/icon/btn_add_icon.png" />  <text class="span">新建{{ current.related_label }}</text></div>
				</div>
            </div>
        </u-popup>
    </div>
</template>
<script>
	export default {
		options: {
			// #ifdef MP-WEIXIN
			// 微信小程序中 options 选项
			multipleSlots: true, //  在组件定义时的选项中启动多slot支持，默认启用
			styleIsolation: "shared", //  启动样式隔离。当使用页面自定义组件，希望父组件影响子组件样式时可能需要配置。具体配置选项参见：微信小程序自定义组件的样式
			addGlobalClass: true, //  表示页面样式将影响到自定义组件，但自定义组件中指定的样式不会影响页面。这个选项等价于设置 styleIsolation: apply-shared
			virtualHost: true, //  将自定义节点设置成虚拟的，更加接近Vue组件的表现。我们不希望自定义组件的这个节点本身可以设置样式、响应 flex 布局等，而是希望自定义组件内部的第一层节点能够响应 flex 布局或者样式由自定义组件本身完全决定
			// #endif
		},
	}
</script>
<script setup>
import {getRecordData}  from  '@/api/common'
import { useDataList } from '@/vueUse/dataList'
import { useRelatedList } from '@/vueUse/relatedList'
const router = useRouter();
const prop =  defineProps({
    id: {
        type:[String,Number],
        default: null
    },
    title: {
        type:String,
        default: '市场活动相关列表'
    },
    data:{
        type: [Array],
        default:[]
    },
    baseInfo:{
        type: [Object],
        default:{}
    },
})
const relatedList = ref(prop.data);
const recordData = ref({})
const current = ref({})
const showLeft = ref(false)

const showRelated = (item) => {
    current.value = item
    getDataPage()
}
const getDataPage = () =>{
	// debugger
    let item = current.value
    if(!item.loadData){
        item.loadData = useRelatedList({ url:item.list_api_url})
        let othen = {};
        othen[item.list_param] = prop.id
        item.loadData.query = othen;
    }
    if(item.loadData.finished){
        recordData.value = item.loadData;
        showLeft.value = true
        return;
    }
    getRecordData(item.list_api_url,item.loadData.payload).then((res) => {
        item.loadData.list = [...item.loadData.list, ...res.data.list]
        item.loadData.totalRow = res.data.totalRow
        item.loadData.pageNo = item.loadData.pageNo + 1 ;
        item.loadData.loading = false;
        item.loadData.finished = res.data.lastPage;

        recordData.value = item.loadData
        console.log('current',current)
        showLeft.value = true
    }).catch((err) => {
        item.loadData.finished = true;
    });
}
watch(() => prop.data,(newValue, oldValue) => {
    if(newValue){
        relatedList.value = newValue
    }
});
const finishedText = computed(() => {
    return recordData.value.list && recordData.value.list.length>0?'没有更多了':'';
})
//点击明细
const handDetail =(item)=>{
    if(current.value.details_route_url){
		showLeft.value = false
		router.push({'name': current.value.details_route_url,params: {id: item.id}})
	}
}
//点击添加
const handAdd =()=>{
    if(current.value.add_route_url){
		showLeft.value = false
        let add_param = {}
        // let param_fild_arr = current.value.add_param.split(',');
        // param_fild_arr.forEach(item => {
        //     add_param[item] = baseInfo[item]
        // });
        add_param[current.value.add_param] = prop.id;
        add_param.name = prop.baseInfo.name;
        router.push({name: current.value.add_route_url,params: add_param})
    }
}
const reset=()=>{
	recordData.value = {}
	current.value = {}
	showLeft.value = false;
}
onMounted(()=>{
})
defineExpose({
    reset
})
</script>

<style lang="scss">
	//相关列表
	.other{
	    padding: 10px;
	    margin-top: 10px;
	    &_tit{
	        margin-bottom: 10px;
	        font-weight: 600;
	        color: #333333;
	        font-size: 18px;
	    }
	    &_list{
	        padding-top: 5px;
	        &_itme{
	            margin-bottom: 10px;
	            background-color: #fff;
	            border-radius: var(--card-border-radius);
	            padding: 10px;
	            display: flex;
	            align-items: center;
	            font-weight: 600;
	            color: #333333;
	            font-size: 16px;
	            min-height: 45px;
	            label{
	                margin-left: 10px;
	            }
	            .link{
	                margin-left: auto;
	                color: #E0E0E0;
	            }
	        }
	    }
	}
	
	//相关列表弹出
	.related{
	    background: #F5F6F8;
	    // min-height: 100%;
	    height: 100vh;
		width: 80vw;
		position: relative;
		display: flex;
		flex-direction: column;
	    .addbox{
	        // position: fixed;
	        margin-bottom: 20px;
	        width: calc(100% - 20px);
	        text-align: center;
			display: flex;
			margin-top: auto;
	    }
	    &_tit{
	        padding: 25px 0 15px 0;
	        background: #F5F6F8;
	        font-size: 16px;
	        font-family: PingFang SC-Semibold, PingFang SC;
	        font-weight: 600;
	        color: #333333;
	        text-align: center;
	    }
		.scrollHeight{
			height:100% ;
			// height: calc(100vh - 145px);
		}
	    &_list{
	       
	        line-height: 1.6;
	        max-height: calc(100% - 65px);
	        overflow-y: hidden;
	        // padding-bottom: 70px;
			position: relative;
	        &.iframe{
	            height: calc(100% - 70px );
	        }
	        &_itme{
	            padding: 10px;
	            display: flex;
	            align-items: center;
				background: #fff;
	            &_tit{
	                color: #333333;
	                font-size: 15px;
	                font-weight: 600;
	            }
	            &_left{
	                font-weight: 400;
	                color: #999999;
	                font-size: 12px;
	            }
	            &_rigth{
	                margin-left: auto;
	            }
	            &_name{
	                background: #849EB2;
	                border-radius: 4px;
	                font-weight: 600;
	                color: #FFFFFF;
	                font-size: 14px;
	                width: 34px;
	                height: 34px;
	                text-align: center;
	                line-height:34px;
	                margin-right: 10px;
	            }
	        }
	    }
	}
	
</style>