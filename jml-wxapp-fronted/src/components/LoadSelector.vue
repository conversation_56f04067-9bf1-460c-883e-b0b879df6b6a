
<template>
	<zxz-uni-data-select
	     :filterable="filterable" v-model="value" v-bind="$attrs"
	     ref="select"
	     clearable
		 :localdata="options"
		 :placeholder="placeholder"
		 :dataText="dataText" 
		 :dataValue="dataValue"
		  @change="change"
		  @showSelector="showSelector"
	     @visible-change="(value) => cancalReadOnly(value, 'select')"
	>
	    <template #empty>
	        <slot name="empty"></slot>
	    </template>
	</zxz-uni-data-select>
    <!-- 远程加载 ele-select -->
    <!-- <piaoyi-select
         :filterable="filterable" v-model="value" v-bind="$attrs"
         ref="select"
         clearable
		 :options="options"
		 :placeholder="placeholder"
		 :valueType="valueType"
         @visible-change="(value) => cancalReadOnly(value, 'select')"
    >
        <template #empty>
            <slot name="empty">
            </slot>
        </template>
    </piaoyi-select> -->
</template>
<script>
	export default {
		options: {
			// #ifdef MP-WEIXIN
			// 微信小程序中 options 选项
			multipleSlots: true, //  在组件定义时的选项中启动多slot支持，默认启用
			styleIsolation: "shared", //  启动样式隔离。当使用页面自定义组件，希望父组件影响子组件样式时可能需要配置。具体配置选项参见：微信小程序自定义组件的样式
			addGlobalClass: true, //  表示页面样式将影响到自定义组件，但自定义组件中指定的样式不会影响页面。这个选项等价于设置 styleIsolation: apply-shared
			virtualHost: true, //  将自定义节点设置成虚拟的，更加接近Vue组件的表现。我们不希望自定义组件的这个节点本身可以设置样式、响应 flex 布局等，而是希望自定义组件内部的第一层节点能够响应 flex 布局或者样式由自定义组件本身完全决定
			// #endif
		},
	}
</script>
<script setup>
import {getRecordData}  from  '@/api/common'
const prop =  defineProps({
    modelValue:{
        type:[String,Number,Boolean,Array],
    },
	dataText:{
	    type:[String,Number,Boolean],
		default: 'name'
	},
	dataValue:{
	    type:[String,Number,Boolean],
		default: 'id'
	},
    load_url: {
        type:[String],
        default: null
    },
    placeholder: {
        type:String,
        default: '请选择'
    },
    payload:{
        type:Object,
        default:{}
    },
    filterable:{
        type:[String,Number,Boolean],
        default:false
    },
})
const emit = defineEmits(['update:modelValue','change'])
const options = ref([])
const value = computed({
    get() {
      return prop.modelValue
    },
    set(value) {
      emit('update:modelValue', value)
    }
})
const getDataPage = () =>{
    if(!prop.load_url){
		return uni.showToast({
			title:`load_url 为空`,
			icon:'none',
			duration:1500
		}) 
	}
    getRecordData(prop.load_url,prop.payload).then((res) => {
        options.value = res.data
    }).catch((err) => {
    });
}

const _this = getCurrentInstance()
const cancalReadOnly = (i,k)=>{
    // ios 不弹软键盘
    // if((prop.filterable || prop.filterable=='')){
    //     let input = select.value?.$el?.querySelector('input')
    //     input?.removeAttribute('readOnly');
    //     if(i){
    //         _this?.refs[k].focus()
    //     }else{
    //         _this?.refs[k].blur()
    //     }
    // }
}
const select = ref()
const blur =() =>{
    select.value.blur()
    // debugger
}
nextTick(()=>{
    if((prop.filterable || prop.filterable=='')){
        // ios 不弹软键盘
        // let input = select.value?.$el?.querySelector('input')
        // input?.removeAttribute('readOnly');
        // input.onblur = () => {
        //     // debugger
        //     setTimeout(() => {
        //         input.removeAttribute('readOnly')
        //     }, 200)
        // }
    }
})
const change = (info)=>{
	emit('change',info);
}
const toggleSelector=()=>{
	select.value.toggleSelector()
}
const showSelector = (val)=>{
	if(val && options.value.length==0){
		getDataPage()
	}
}
onMounted(()=>{
    getDataPage()
})
defineExpose({
    getDataPage,blur,toggleSelector
})
</script>

<style scoped lang="scss">
    .selector{
        display: flex;
        flex-direction: row;
        $progress_item_height: 30px;
        $progress_item_san:10px;
        --progress_item_bgcolor: #DBDFE2;
        width: 100%;
        .show_tit{
            width: 100%;
        }
    }
    //相关列表弹出
    .selector_pop{
        height: 100%;
        .pop_tit{
            padding: 25px 0 15px 0;
            font-size: 16px;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
            color: #333333;
            text-align: center;
        }
        .pop_list{
            padding: 0 10px;
            line-height: 1.6;
            background: #fff;
            max-height: calc(100% - 65px);
            overflow-y: scroll;
            padding-bottom: 70px;
            &_itme{
                padding: 10px 0;
                display: flex;
                align-items: center;
                &_tit{
                    color: #333333;
                    font-size: 16px;
                }
                &_left{
                    font-weight: 400;
                    color: #999999;
                    font-size: 12px;
                }
                &_rigth{
                    margin-left: auto;
                }
                &_name{
                    background: #849EB2;
                    border-radius: 4px;
                    font-weight: 600;
                    color: #FFFFFF;
                    font-size: 14px;
                    width: 34px;
                    height: 34px;
                    text-align: center;
                    line-height:34px;
                    margin-right: 10px;
                }
            }
        }
    }
</style>
