<template>	
	<movable-area class="movable-area">
		<movable-view class="movable-view" :x="x" :y="y" direction="all" out-of-bounds>
			<view class="floatingWindow">
				<div v-if="showHome" @click="click_home" class="item">
					<u-icon size="20" name="/static/icon/home.png"></u-icon>
				    <div class="lable">回首页</div>
				</div>
				<slot></slot>
			</view>
		</movable-view>
	</movable-area>
</template>
<script>
	export default {
		options: {
			// #ifdef MP-WEIXIN
			// 微信小程序中 options 选项
			multipleSlots: true, //  在组件定义时的选项中启动多slot支持，默认启用
			styleIsolation: "shared", //  启动样式隔离。当使用页面自定义组件，希望父组件影响子组件样式时可能需要配置。具体配置选项参见：微信小程序自定义组件的样式
			addGlobalClass: true, //  表示页面样式将影响到自定义组件，但自定义组件中指定的样式不会影响页面。这个选项等价于设置 styleIsolation: apply-shared
			virtualHost: true, //  将自定义节点设置成虚拟的，更加接近Vue组件的表现。我们不希望自定义组件的这个节点本身可以设置样式、响应 flex 布局等，而是希望自定义组件内部的第一层节点能够响应 flex 布局或者样式由自定义组件本身完全决定
			// #endif
		},
	}
</script>
<script setup>
	const prop =  defineProps({
		showHome:{
		    type: Boolean,
		    default:true
		},
		showAdd:{
		    type: Boolean,
		    default:false
		}
	})
	const emit = defineEmits(['click_add', 'click_home'])
	const router=useRouter()
	const click_home =()=>{
		emit('click_home')
		router.replaceAll({name:'home'})
	}
	const click_add =()=>{
		emit('click_add')
	}
	const x = ref(355);
	const y = ref(450);
</script>

<style  lang="scss">
	$all_width: 70px;
	$all_height: 60rpx;
	.movable-area {
		height: 100vh;
		width: 100vw;
		left: 0;
		// top: 0;
		bottom: 40px;
		position: fixed;
		z-index: 99;
		pointer-events: none;		//此处要加，鼠标事件可以渗透
		.movable-view {
			width: 50px;
			height: auto;
			// height: $all_height;
			right: 0 !important;
			left: auto;
			pointer-events: auto;	//恢复鼠标事件
		}
	}
    .floatingWindow{
        // position:fixed;
        // bottom: 10%;
        // right:  20px;
        // background: linear-gradient(130deg, #FFFFFF 4%, #E1E1E1 100%);
        // box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.1), inset 0px 0px 4px 1px rgba(238,238,238,0.69);
        // border-radius: 6px 6px 6px 6px;
        // border: 1px solid #E8E8E8;
        // padding:4px 6px;
        max-width: $all_width;
        // padding-bottom: 30px;
        .item{
            box-shadow: 0px 4px 10px 0px rgba(153,153,153,0.2);
            border: 1px solid #DCDCDC;
            border-radius: 50%;
            display: flex;
            width: 50px;
            height: 50px;
            flex-direction: column;
            align-content: center;
            justify-content: center;
            flex-flow: wrap;
            cursor: pointer;
            background: #fff;
            border-bottom: 1px solid #E1E1E1;
            margin-bottom: 20px;
            &.add_btn{
                background: #849EB2;
            }
            .lable{
                font-size: 12px;
                padding-bottom: 5px;
                margin-top: 3px;
            }
            &:last-child{
                // margin-bottom: 0;
            }
        }

        :slotted(.item) {
            box-shadow: 0px 4px 10px 0px rgba(153,153,153,0.2);
            border: 1px solid #DCDCDC;
            border-radius: 50%;
            display: flex;
            width: 50px;
            height: 50px;
            flex-direction: column;
            align-content: center;
            justify-content: center;
            flex-flow: wrap;
            cursor: pointer;
            background: #fff;
            border-bottom: 1px solid #E1E1E1;
            margin-bottom: 20px;
            &.add_btn{
                background: #849EB2;
            }
            .lable{
                font-size: 12px;
                padding-bottom: 6px;
                transform: scale(0.85);
            }
        }
    }
</style>
