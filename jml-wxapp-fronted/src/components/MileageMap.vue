
<template>
	<view class="fl">
			
		
    <!--根据微信当前定位经纬度， 获取百度地图选择地址 -->
    <u-modal v-bind="$attrs" @confirm="confirm" confirmColor="#849EB2"  @cancel="emit('cancel')" :title="type=='start'?'开启里程认证':'结束里程认证'" :before-close="beforeClose" :show-confirm-button="show_confirm_button" @open="open">
        <div class="map_box">
            <map
            	:enable-3D="enable3D"
            	:show-compass="showCompass" 
            	:enable-traffic="enableTraffic" 
            	:show-location="showLocation" 
            	:enable-indoorMap="enableIndoorMap"
            	:enable-satellite="enableSatellite"
            	:style="{ width: width , height: height + 'px' }" :latitude="resultInfo.location.latitude" :longitude="resultInfo.location.longitude">
			</map>	
            <div class="content">
				<u--form labelPosition="top" labelWidth="auto" ref="form1" >
					<u-form-item label="地区" v-if="type=='start'" prop="district" required>
						<zxz-uni-data-select clearable placeholder="请选择"  v-model="resultInfo.district" dataText="district" @change="areaChange" dataValue="district"  :localdata="userInfo.staffAreaList"></zxz-uni-data-select>
					</u-form-item>
					<u-form-item  v-if="withUploader" :label="type=='start'?'开始里程图片':'结束里程图片'" >
						<u-upload
						    :maxCount="maxCount"
						    :capture="capture"
							:sizeType="['compressed']"
							@afterRead="afterRead"
							:previewFullImage="true"
						    :deletable="true"
							@delete="beforeDelete"
						    :fileList="uploader" >
						</u-upload>
					</u-form-item>
					<u-form-item :label="type=='start'?'开始里程数':'结束里程数'"  prop="mileage" required >
						<u--input  v-model="resultInfo.mileage"  type="Nunber"   placeholder="请输入里程数"  />
					</u-form-item>
				</u--form>
            </div>
        </div>
    </u-modal>
	</view>
</template>

<script setup>
import {asyncUpload,upload, getAddrByLngAndLat}  from  '@/api/common'
import { isArray } from 'lodash';
	const util = inject("util");
	const appStore = useAppStore();
	const prop =  defineProps({
		type:{
			type:String,
			enum:['start','end'],
			default: 'start',
		},
		width: {
			type: [Number,String],
			default: '100%',
		},
		height: {
			type: [Number,String],
			default: 200,
		},
		withUploader:{
			type:Boolean,
			default:false
		},
		capture:{
			type:String,
			default:''
		},
		mileageInfo:{
			type:Object,
			default:null
		},
		showCompass: { //	是否显示指南针
			type: Boolean,
			default: true
		},
		enableTraffic: { //	是否开启实时路况
			type: Boolean,
			default: true
		},
		showLocation: { //	是否显示带有方向的当前定位点
			type: Boolean,
			default: true
		},
		enableIndoorMap: { //是否展示室内地图
			type: Boolean,
			default: false
		},
		enableSatellite: { //是否开启卫星图
			type: Boolean,
			default: true
		},
		enable3D: { //是否显示3D楼块
			type: Boolean,
			default: false
		},
	})
	const emit = defineEmits(['confirm','cancel'])
	const { userInfo } = useUserStore();
	const resultInfo = reactive({
		location:{
		},
		address:'',
		files:[],
		mileage:'',
		area_code:userInfo.staffAreaList.length==1 ? userInfo.staffAreaList[0].areaCode : null ,
		district:userInfo.staffAreaList.length==1 ? userInfo.staffAreaList[0].district : null ,
	})
	
	const areaChange =(value)=>{
		resultInfo.area_code = userInfo.staffAreaList.find(i=>i.district===value)?.areaCode;
	}
	const show_confirm_button = computed(()=>{
		return(!prop.withUploader || (prop.withUploader && resultInfo.files.length>0  && resultInfo.mileage!=''))
	})
	const confirm = () => {
		if(prop.withUploader && resultInfo.files.length==0) return uni.showToast({
			title: '请上传附件',
			duration: 1500,
			icon:'none'
		}); 
		emit('confirm',resultInfo);
	}
	const reset = ()=>{
		resultInfo.files  = [], resultInfo.mileage = '',uploader.value = [];
	}
	const uploader = ref([]);
	watch(() => prop.show,val => {
			val && open()
		},{ immediate: true }
	)
	const open = () =>{
		nextTick(()=>{
			uni.getLocation({
				type: 'gcj02',
				altitude:true,
				geocode:true,
				isHighAccuracy:true,
				success: function (res) {
					console.log('当前位置的经度：' + res.longitude);
					console.log('当前位置的纬度：' + res.latitude);
					resultInfo.location = res
					getAddrByLngAndLat(res.longitude,res.latitude).then((addres)=>{
						resultInfo.address = addres.data
					})
				},
				fail:()=>{
							// 如果用uni.chooseLocation没有获取到地理位置，则需要获取当前的授权信息，判断是否有地理授权信息
					uni.getSetting({
						success: (res) => {
							console.log(res);
							var status = res.authSetting;
							if(!status['scope.userLocation']){
							// 如果授权信息中没有地理位置的授权，则需要弹窗提示用户需要授权地理信息
								uni.showModal({
									title:"是否授权当前位置",
									content:"需要获取您的地理位置，请确认授权，否则地图功能将无法使用",
									success:(tip)=>{
										if(tip.confirm){
										// 如果用户同意授权地理信息，则打开授权设置页面，判断用户的操作
											uni.openSetting({
												success:(data)=>{
												// 如果用户授权了地理信息在，则提示授权成功
													if(data.authSetting['scope.userLocation']===true){
														uni.showToast({
															title:"授权成功",
															icon:"success",
															duration:1000
														})
														// 授权成功后，然后再次chooseLocation获取信息
														uni.getLocation({
															type: 'gcj02',
															altitude:true,
															geocode:true,
															isHighAccuracy:true,
															success: function (res) {
																console.log('当前位置的经度：' + res.longitude);
																console.log('当前位置的纬度：' + res.latitude);
																resultInfo.location = res
																getAddrByLngAndLat(res.longitude,res.latitude).then((addres)=>{
																	resultInfo.address = addres.data
																})
															},
														})
													}else{
														uni.showToast({
															title:"授权失败",
															icon:"none",
															duration:1000
														})
													}
												}
											})
										}
									}
								})
							}
						},
						fail: (res) => {
							uni.showToast({
								title:"调用授权窗口失败",
								icon:"none",
								duration:1000
							})
						}
					})
				}
			});
				
		})
	}
	const beforeClose = () =>{
		if(prop.mileageInfo && prop.mileageInfo.id && prop.mileageInfo.start_mileage>=resultInfo.mileage) return false
		else return true
	}
    const maxCount = 1
     const afterRead = async(event) =>{
    
         let files = [].concat(event.file)
		 if(!uploader.value) uploader.value = [];
         let fileListLen = uploader.value.length
         files.map((item) => {
         	uploader.value.push({
         		...item,
         		status: 'uploading',
         		message: '上传中'
         	})
         })
    		for (let i = 0; i < files.length; i++) {
    			// 第一种得先转文件流
    			// 第二种使用uni自带的
    			try {
    				const {data} = await asyncUpload(files[i].url)
    				let item = uploader.value[fileListLen];
    				uploader.value.splice(fileListLen, 1, {
    				  ...item,
    				  status: 'success',
    				  message: '',
    				  url: data.url,
    				});
    				fileListLen++;
    				if(files.length==i+1)	resultInfo.files = uploader.value
    			} catch (e) {
    				console.log(e)
    				uni.showToast({
    					title: "图片上传失败",
    					icon: "none"
    				})
    			}
    		}
     }
    
	const beforeDelete = ({file,index}) =>{
		uni.showModal({
			title: '提示',
			content: '是否删除该附件?',
			success: function (res) {
				if (res.confirm) {
					uploader.value.splice(index, 1)
					resultInfo.files.splice(index, 1)
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
    }

	onMounted(() => {
	})
	defineExpose({
		reset
	})
</script>

<style scoped lang="scss">
    .map_box{
        // padding: 10px;
		width: 100%;	
		.content{
			padding:0 10px;
		}
    }	
</style>

