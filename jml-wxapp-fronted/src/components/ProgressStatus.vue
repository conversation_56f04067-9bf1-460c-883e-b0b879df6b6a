
<template>
    <!-- 状态更改组件 -->
    <div  class="progress_box">
        <div v-for="(item,index) in status_list" @click.stop="change(item)" :key="index"  class="progress_item" :class="{'act':((active == item[dataKey]) || ((!active || active=='') && index==0)),'done':(isClosed == '1' || ahead>index) ,'readonly':readonly}">
            <!-- <van-icon v-if="ahead == index" name="success" size="20" /> -->
            <span >{{ item[dataLabel] }} </span>
        </div>
    </div>
</template>

<script setup>
const util = inject("util");
const prop = defineProps({
    data:{
        type:[Object,Number,String,Array],
        default: null
    },
    active: {
        type:[String,Number],
        default: null
    },
    readonly:{
        type:[Boolean,String,Number],
        default:false
    },
    confirmMsg: {
        type:String,
        default: '是否切换该状态？'
    },
    status_list:{
        type: [Array],
        default:[
            // {case_stage_name:'状态',case_stage_code:'1'},
            // {case_stage_name:'状态2',case_stage_code:'2'},
        ]
    },
    customChange:{
        type: [Function],
        default:null
    },
	dataKey:{
	    type:[String,Number,Boolean],
		default: 'case_stage_code'
	},
	dataLabel:{
	    type:[String,Number,Boolean],
		default: 'case_stage_name'
	},
	isClosed: {
	    type:[String,Number],
	    default: 0
	},
})
const emit = defineEmits(['update:active','change'])
const defaultActive = ref(prop.active);
const change = (item) => {
    if(prop.readonly) return false
    if(prop.customChange){
        const response = prop.customChange(item[prop.dataKey],prop.data);
        if (!response) {
            emit('change', item[prop.dataKey])
            emit('update:active',item[prop.dataKey])
            return;
        }
        if (util.test.promise(response)) {
            response.then((data) => {
                emit('change', item[prop.dataKey])
                emit('update:active',item[prop.dataKey]);
            }).catch();
            return;
        }
        return;
    }else{
		uni.showModal({
			title: '提示',
			content: prop.confirmMsg,
			success: function (res) {
				if (res.confirm) {
					emit('change', item[prop.dataKey])
					emit('update:active',item[prop.dataKey]);
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			}
		});
    }
}
watch(() => prop.active,(newValue, oldValue) => {
    if(newValue){
        defaultActive[prop.dataKey] = newValue
    }
});
// 计算其他已经通过了的状态
const ahead = computed(() => {
    return   prop.status_list.findIndex(item=>prop.active === item[prop.dataKey])
})
onMounted(()=>{
})
</script>

<style scoped lang="scss">
    .progress_box{
    display: flex;
    flex-direction: row;
    $progress_item_height: 30px;
    $progress_item_san:10px;
    padding: 10px 0;
    --progress_item_bgcolor: #DBDFE2;
    // height: 100px;
    .progress_item{
        // flex: 1;
        flex-grow: 1;
        font-size: 12px;
        position: relative;
        background-color: var(--progress_item_bgcolor);
        margin-right: 3px;
        height: $progress_item_height;
        text-align: center;
        line-height: $progress_item_height;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        span{
            
        }
        text-indent: calc($progress_item_san / 2);
        &::before{
            content: "";
            position: absolute;
            left:  0;
            bottom: 0;
            width: 0;
            height: 0;
            border-left: $progress_item_san solid #fff;
            border-top: calc($progress_item_height / 2) solid transparent;
            border-bottom: calc($progress_item_height / 2) solid transparent;
        }
        &::after{
            content: "";
            position: absolute;
            right:  -$progress_item_san;
            bottom: 0;
            width: 0;
            height: 0;
            border-left: $progress_item_san+1 solid var(--progress_item_bgcolor);
            border-top: calc($progress_item_height / 2) solid transparent;
            border-bottom: calc($progress_item_height / 2) solid transparent;
            z-index: 1;
        }
        &:first-child{
            border-radius: calc($progress_item_height / 2) 0 0 calc($progress_item_height / 2);
            &::before{
                display: none;
            }
        }
        &:last-child{
            border-radius: 0 calc($progress_item_height / 2) calc($progress_item_height / 2) 0;
            &::after{
                display: none;
            }
        }
		&.readonly{
		    cursor:no-drop;
			color: #000;
		}
        &.act{
            // --progress_item_bgcolor: #88D39B;
            // // width: 100px;
            // color: #fff;
            // + div{
            //     --progress_item_bgcolor: #4085CB;
            //     color: #fff;
            // }
            --progress_item_bgcolor: #4085CB;
            color: #fff;
        }
        &.done{
            --progress_item_bgcolor: #88D39B;
            // width: 100px;
            color: #fff;
        }
        
    }
}
</style>
