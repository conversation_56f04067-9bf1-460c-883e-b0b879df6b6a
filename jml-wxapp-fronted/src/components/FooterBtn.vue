
<template>
    <!-- 【底部 确认、取消 按钮组】 -->
    <div  class="footer">
        <div class="footer_btn" :style="footerBtnBoxStyle">
            <div v-if="cancelBtnShow" @click="oncancel" class="cancel_btn btn">{{cancel_btn_text}}</div>
            <div v-if="confirmBtnShow" @click="onconfirm" class="confirm_btn btn">{{confirm_btn_text}}</div>
			<slot></slot>
        </div>
        <div v-if="footer_tip && footer_tip!==''" class="footer_tip">{{footer_tip}}</div>
    </div>
</template>
<script>
import { ref, watch } from 'vue';
export default {
    name: 'FooterBtn',
	options: {
		// #ifdef MP-WEIXIN
		// 微信小程序中 options 选项
		multipleSlots: true, //  在组件定义时的选项中启动多slot支持，默认启用
		styleIsolation: "shared", //  启动样式隔离。当使用页面自定义组件，希望父组件影响子组件样式时可能需要配置。具体配置选项参见：微信小程序自定义组件的样式
		addGlobalClass: true, //  表示页面样式将影响到自定义组件，但自定义组件中指定的样式不会影响页面。这个选项等价于设置 styleIsolation: apply-shared
		virtualHost: true, //  将自定义节点设置成虚拟的，更加接近Vue组件的表现。我们不希望自定义组件的这个节点本身可以设置样式、响应 flex 布局等，而是希望自定义组件内部的第一层节点能够响应 flex 布局或者样式由自定义组件本身完全决定
		// #endif
	},
    props: {
        footer_tip: {
            type:String,
            default: null
        },
        footerBtnBoxStyle: {
            type:  [Object, String],
            default:"justify-content:space-evenly"
        },
        cancel_btn_text: {
            type:String,
            default: '取消'
        },
        confirm_btn_text: {
            type:String,
            default: '确认'
        },
        confirmBtnShow:{
            type:Boolean,
            default:true
        },
        cancelBtnShow: {
            type: Boolean,
            default:true,
        },
    },
    setup(prop,context) {
        const oncancel = (e) => {
            context.emit('oncancel')
        };
        const onconfirm = () => {
            context.emit('onconfirm')
        };
        return {
            onconfirm,
            oncancel,
        };
    },
};
</script>

<style lang="scss">
    .footer{
        flex: 0 0 auto; 
        // height:70px;
        padding:0px;
        margin-top: auto;
        position: fixed;
        left: 0;
        bottom: 0;
        width: 100%;
        background: #fff;
        z-index: 100;
        // bottom: calc(var(--ios-safe-bottom-low));
        // bottom: calc(var(--ios-safe-bottom-higt));


        .footer_btn{
            display: flex;
            // justify-content: center;
            // justify-content: space-between;
            width: 100%;
            justify-content: space-evenly;
            .btn{
                flex: 1;
                padding: 0;
                // height: calc(120rpx + var(--ios-safe-bottom-low));
                // line-height: 50px;

                height: calc(50px + calc(var(--ios-safe-bottom-low)/4));
                height: calc(50px + calc(var(--ios-safe-bottom-higt)/4));

                font-size: 16px;
                font-weight: 600;
                cursor: pointer;
                // background: linear-gradient(180deg, #F3F3F3 0%, #D4D4D4 100%);
                // box-shadow: inset 0px 0px 2px 1px #FFFFFF;
                background: #fff;
                border-radius: 0px;
                text-align: center;
                color: #849EB2;
                // margin-right: 30px;
                &.confirm_btn{
                    // margin-left: auto;
                    color: #FFFFFF;
                    // background: linear-gradient(180deg, #FFAF78 0%, #FF6A00 100%);
                    background: #849EB2;
                }
                &:last-child{
                    margin-right: 0px;
                }
                padding-bottom: calc(var(--ios-safe-bottom-low)/4);
                padding-bottom: calc(var(--ios-safe-bottom-higt)/4);
            }
        }
        .footer_tip{
            margin-top: 10px;
            color: rgba(61, 61, 61, 0.5);
            font-size: 12px;
            line-height: 24px;
            text-align: center;
            &::before{
                content: '*';
                color: rgba(255, 2, 2, 1);
                font-size: 14px;
                margin-right: 5px;
                display: inline-block;
                vertical-align: middle;
            }
        }
    }
</style>
