
<template>
    <!-- 待选列表 -->
    <div class="selector">
        <div class="show_tit" @click="showRelated">
            <div v-if="modelValue && modelValue.length>0" >已选{{ modelValue.length }}{{ title_last_txt }}</div>
            <div v-else class="placeholder">{{ placeholder }}</div>
			
			<uni-icons style="right: 0;position: absolute; top: 0px;"  :type="showLeft? 'top' : 'bottom'"
				size="14" color="#999" />
            <!-- {{title}} -->
        </div>
		<u-popup :show="showLeft" :round="10" closeable mode="bottom" @close="showLeft = false" :style="{ height: '85%' }">
            <div class="selector_pop">
                <div class="pop_tit">{{ title }}</div>
                <div class="pop_list">
                    <template >
                        <u-checkbox-group placement="column" v-model="checked">
                        <div v-for="(item,index) in recordData" :key="index" class="list_item">
                            <div class="list_item_head">
                                <div v-if="tltleField" class="list_itme_name">
                                    {{ item[tltleField] }}
                                </div>
                                <div class="tool">
                                    <u-checkbox icon-size="16" :disabled="prop.notEditOld && prop.modelValue.includes(item.id)" :name="item.id" shape="square" checked-color="#849EB2"></u-checkbox>
                                </div>
                            </div>
                            <u-divider></u-divider>
                            <div class="list_item_main">
                                <template v-if="fieldArr">
                                    <div v-for="(info,key) in fieldArr" :key="key" class="item">
                                        <div class="left">{{info.label}}</div>
                                        <div class="rigth">{{item[info.field]}}</div>
                                    </div>
                                </template>
                            </div>
                        </div>
                        </u-checkbox-group>
                    </template>
                    <div class="empty_box" v-if="recordData.length<=0">
						<u-empty  text="暂时没有数据" :icon="`${config.static}images/empty/data.png`" ></u-empty>
                    </div>
                </div>
                <div  class="pop_footer">
                    <div class="btn" @click="handConfirm"> <span>确定</span></div>
                </div>
            </div>
        </u-popup>
    </div>
</template>

<script setup>
import { onMounted, ref, watch,computed,reactive } from 'vue';
import {getRecordData}  from  '@/api/common'
import { useDataList } from '@/vueUse/dataList'
import { useRelatedList } from '@/vueUse/relatedList'
const router = useRouter();
const prop =  defineProps({
    modelValue:{
        type:[String,Array],
        default:[]
    },
    list_api_url: {
        type:[String],
        default: null
    },
    placeholder: {
        type:[String],
        default: '请选择'
    },
    title: {
        type:String,
        default: '请选择'
    },
    title_last_txt: {
        type:String,
        default: '份工作报告'
    },
    data:{
        type: [Array],
        default:[]
    },
    tltleField: {
        type:String,
        default: 'name'
    },
    fieldArr:{
        type:Array,
        default:[]
    },
    //不允许删除原来的
    notEditOld:{
        type:Boolean,
        default:false
    },
    readonly:{
        type:[Boolean,String,Number],
        default:false
    },
    readonlyMsg:{
        type:String,
        default:null
    },
})
const emit = defineEmits(['update:modelValue','change'])
const relatedList = ref(prop.data);
const recordData = ref({})
const current = ref({})
const showLeft = ref(false)
const newValue = computed({
  get() {
    return prop.modelValue
  },
  set(value) {
    emit('update:modelValue', value)
  }
})
const checked =  ref([])
const checkedInfo = ref([])
const showRelated = () => {
    if(!prop.list_api_url) return uni.showToast({
		title: 'list_api_url 为空',
		duration: 1500,
		icon:'none'
	});
    if(prop.readonly) return prop.readonlyMsg? uni.showToast({
		title: prop.readonlyMsg,
		duration: 1500,
		icon:'none'
	}) : false ;
    getDataPage()
}
const getDataPage = () =>{
    getRecordData(prop.list_api_url).then((res) => {
        recordData.value = res.data
        showLeft.value = true
        if(!checked.value || checked.value.length==0){
            checked.value = prop.modelValue?prop.modelValue:[]
        }
    }).catch((err) => {
    });
}
const handConfirm = () =>{
    showLeft.value = false
    newValue.value = checked.value
    emit('change', checkedInfo.value)
}
watch(() => checked.value,(newValue, oldValue) => {
    if(newValue){
        checkedInfo.value = checked.value.map((key)=>{
            let info = recordData.value.find(i => i.id == key )
            return info
        })
    }
});
onMounted(()=>{
})
</script>

<style scoped lang="scss">
    .selector{
        display: flex;
        // flex-direction: row;
        $progress_item_height: 30px;
        $progress_item_san:10px;
        --progress_item_bgcolor: #DBDFE2;
        width: 100%;
        .placeholder{
            color: var(--van-text-color-3);
        }
        .show_tit{
            width: 100%;
			position: relative;
        }
    }
    //相关列表弹出
    .selector_pop{
        height: 100%;
        .pop_tit{
            padding: 25px 0 15px 0;
            font-size: 16px;
            font-family: PingFang SC-Semibold, PingFang SC;
            font-weight: 600;
            color: #333333;
            text-align: center;
            background: #F5F5F5;
        }
        .pop_list{
            padding: 0 10px;
            line-height: 1.6;
            background: #F5F5F5;
            max-height: calc(100% - 65px);
            overflow-y: scroll;
            padding-bottom: 60px;
			width: 100%;
            .list_item{
                padding: 10px;
                background: #fff;
                border-radius: 8px;
                margin: 10px 0;
                &:first-child{
                    margin-top:0 ;
                }
                .list_item_head{
                    display: flex;
                    align-items: center;
                    padding: 5px 0 0 0;
                    font-size: 18px;
                    font-weight: 600;
                    color: #333333;
                    .tool{
                        margin-left: auto;
                    }
                }
                .list_item_main{
                    // display: flex;
                    // align-items: center;
                    padding: 5px 0;
                    .item{
                        display: flex;
                        align-items: center;
                        font-size: 14px;
                        margin-top: 5px;
                    }
                    .left{
                        font-weight: 400;
                        color: #999999;
                        width: 80px;
                    }
                    .rigth{
                        flex: 1;
                        color: #333333;
                    }
                }
                &_tit{
                    color: #333333;
                    font-size: 16px;
                }
                
                &_name{
                    background: #849EB2;
                    border-radius: 4px;
                    font-weight: 600;
                    color: #FFFFFF;
                    font-size: 14px;
                    width: 34px;
                    height: 34px;
                    text-align: center;
                    line-height:34px;
                    margin-right: 10px;
                }
            }
        }
        .pop_footer{
            position: fixed;
            bottom: 15px;
            width: calc(100% - 20px);
            left: 10px;
            text-align: center;
            .btn{
                background: #849EB2;
                border-radius: 4px;
                color: #fff;
            }
        }
    }
</style>
