// // 文件permission.js
 
 
// // 判断按钮权限逻辑
// const checkPermission = (el, binding) => {
 
//     // 获取自定义指令传过来的数组（binding.value）
//     const btnRoles = binding.value
//     // 取一下本地存的账号权限
//     const userRoles = JSON.parse(localStorage.getItem("role"))
 
//     // 判断自定义指令的传值，在账号权限数组中能否找到 
//     if (btnRoles && btnRoles instanceof Array) {
//         if (btnRoles.length) {
//             // 能找到返回true
//             const hasPermission = userRoles.some(v => {
//                 return btnRoles.includes(v)
//             })
//             // 找不到返回false，使用自定义指令的钩子函数，操作dom元素删除该节点
//             if (!hasPermission) {
//                 el.parentNode && el.parentNode.removeChild(el)
//             }
//         }
//         else {
//             throw new Error(`传入关于权限的数组，如 v-permission="['super','normal']"`)
//         }
//     }
// }
 
// // 导出一个对象用作自定义指令的第二个参数
// export default {
//   mounted(el, binding) {
//     checkPermission(el, binding)
//   },
//   updated(el, binding) {
//     checkPermission(el, binding)
//   }
// }
import useUserStore from '@/store/modules/userStore'
export const hasPermission = {
    install(Vue) {
        //自定义指令v-has：
        Vue.directive('has', {
            mounted(el, binding, vnode) {
                const {menu_code,function_code} = binding.value
                if(!menu_code || !function_code) throw new Error(`need a menu_code,function_code`)
                const store = useUserStore();
                const {userId,token,menuList } = store;
                if (!userId || !token || !menuList) {
                    el.parentNode && el.parentNode.removeChild(el)
                }else{
                    let check = menuList.find(item => item.menu_code == menu_code && item.function_code == function_code)
                    if(!check){
                        el.parentNode && el.parentNode.removeChild(el);
                    }
                }
            },
        });
    }
};
export default hasPermission;