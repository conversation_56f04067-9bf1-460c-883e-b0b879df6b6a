import quarterOfYear from 'dayjs/plugin/quarterOfYear'
import dayjs from 'dayjs'
dayjs.extend(quarterOfYear)

var dateUtils = {
    UNITS: {
      年: 31557600000,
      月: 2629800000,
      天: 86400000,
      小时: 3600000,
      分钟: 60000,
      秒: 1000,
    },
    humanize: function (milliseconds) {
      var humanize = "";
      for (var key in this.UNITS) {
        if (milliseconds >= this.UNITS[key]) {
          humanize = Math.floor(milliseconds / this.UNITS[key]) + key + "前";
          break;
        }
      }
      return humanize || "刚刚";
    },
    format: function (dateStr) {
      var date = this.parse(dateStr);
      var diff = Date.now() - date.getTime();
      if (diff < this.UNITS["天"]) {
        return this.humanize(diff);
      }
      var _format = function (number) {
        return number < 10 ? "0" + number : number;
      };
      return (
        date.getFullYear() +
        "/" +
        _format(date.getMonth() + 1) +
        "/" +
        _format(date.getDate()) +
        "-" +
        _format(date.getHours()) +
        ":" +
        _format(date.getMinutes())
      );
    },
    dateTimeParse: function (str) {
      //将"yyyy-mm-dd HH:MM:ss"格式的字符串，转化为一个Date对象
      var a = str.split(/[^0-9]/);
      return new Date(a[0], a[1] - 1, a[2], a[3], a[4], a[5]);
    },
    dateParse: function (str) {
        //将"yyyy-mm-dd格式的字符串，转化为一个Date对象
        var a = str.split(/[^0-9]/);
        return new Date(a[0], a[1] - 1, a[2]);
    },
    differDay: function (startDate, endDate) {
      let oldDay = new Date(startDate).getTime();
      let newDay = new Date(endDate).getTime();
      return parseInt((newDay - oldDay) / (1000 * 60 * 60 * 24));
    },
    getDate: function (type) {
      const date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
  
      if (type === "start") {
        year = year;
      } else if (type === "end") {
        year = year + 2;
      }
      month = month > 9 ? month : "0" + month;
      day = day > 9 ? day : "0" + day;
      return `${year}-${month}-${day}`;
    },

    /**
     * 时间格式化函数, 按照指定格式化字符串格式化传入时间
     * 
     * @param {Date} time 需要格式化的时间
     * @param {String} fmStr 定义时间的格式
     * 		yyyy: 代表四位数年份
     * 		  yy: 代表两位数年份 
     * 		  mm: 代表月份(小于10时补0)
     * 		  dd: 代表日期(小于10时补0)
     * 		  hh: 代表小时(小于10时补0)
     * 		  hh: 代表小时(小于10时补0)
     * 		  MM: 代表分钟(小于10时补0)
     * 		  ss: 代表秒数(小于10时补0)
     * 		 SSS: 代表毫秒数
     * 		   w: 代表周几(数字) 
     * 		   W: 代表周几(中文) 
     * 		  ww: 代表周几(英文) 
     * @returns {String} 返回格式化的时间
     */
    timeFormat : (time, fmStr) =>{
        const weekCN = '一二三四五六日';
        const weekEN = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
        let year = time.getFullYear();
        let month = time.getMonth() + 1;
        let day = time.getDate();
        let hours = time.getHours();
        let minutes = time.getMinutes();
        let seconds = time.getSeconds();
        let milliSeconds = time.getMilliseconds();
        let week = time.getDay();

        month = month >= 10 ? month : ('0' + month);
        day = day >= 10 ? day : ('0' + day);
        hours = hours >= 10 ? hours : ('0' + hours);
        minutes = minutes >= 10 ? minutes : ('0' + minutes);
        seconds = seconds >= 10 ? seconds : ('0' + seconds);

        if (fmStr.indexOf('yyyy') !== -1) {
            fmStr = fmStr.replace('yyyy', year);
        } else {
            fmStr = fmStr.replace('yy', (year + '').slice(2));
        }
        fmStr = fmStr.replace('mm', month);
        fmStr = fmStr.replace('dd', day);
        fmStr = fmStr.replace('hh', hours);
        fmStr = fmStr.replace('MM', minutes);
        fmStr = fmStr.replace('ss', seconds);
        fmStr = fmStr.replace('SSS', milliSeconds);
        fmStr = fmStr.replace('W', weekCN[week==0?6:(week - 1)]);
        fmStr = fmStr.replace('ww', weekEN[week==0?6:(week - 1)]);
        fmStr = fmStr.replace('w', week);

        return fmStr;
    },
    returnDiyDate:(date) => {
        //date = parseInt(date);  //强转整型
        // var tDate = getFormatDate(new Date(), "yyyy/MM/dd");  //当前凌晨时间(string)
        // var tTime = new Date(tDate).getTime(); //当前凌晨时间(long)
        // var eTime = 24 * 60 * 60 * 1000;  //一天时间(long)
        // var yTime = tTime - eTime;  //昨天凌晨时间(long)
        // var byTime = tTime - 2 * eTime;  //前天凌晨时间(long)
        // var formatDate = getFormatDateByLong(date, "hh:mm");    //转成时分格式
        // if (date >= tTime) {
        //     //今天
        //     var result = formatDate;
        // } else if (date < tTime && date >= yTime) {
        //     //昨天
        //     var result = "昨天 " + formatDate;
        // } else if (date < yTime && date >= byTime) {
        //     //前天
        //     var result = "前天 " + formatDate;
        // } else {
        //     //前天之前
        //     var result = getFormatDateByLong(date, "MM-dd hh:mm");
        // }
        // return result;
    },
    //获取当前日期，几天后的 【如果要之前的传负数】
    getAfterDate:(num,type)=>{
		var date = new Date();
		date.setDate(date.getDate() + num)	
		//当使用setDate()之后，date对象就发生了改变，所以之后getDate()就能获取到指定的日期
		//这里的num就是你要加的天数，减也可以。
		let year = date.getFullYear()				//年
		let month = date.getMonth() + 1				//月
		let day = date.getDate();					//天数
        if(type){
            if(month >= 1 && month <= 9) {
                month = `0${month}`
            }
            if(day >= 1 && day <= 9) {
                day = `0${day}`
            }
            return `${year}-${month}-${day}`
        }else{
            return date;
        }
	},
    //获取当[月\季度\年]第一天和最后一天
    getMonthFL :(type)=>{
        let date = new Date();
        var y = date.getFullYear(); //获取年份
        if(type=='year'){
            return [[y,'01','01'].join("-"),[y,'12','31'].join("-")]
        }else if(type=='quarter'){
            var m = date.getMonth() ;
            var fm ,lm
            if(m <3 ){
                fm='01'
                lm ='03'
            }else if(2 < m && m < 6){
                fm='04'
                lm ='06'
            }else if(5 < m && m < 9){
                fm='07'
                lm ='09'
            }else if(8 < m && m < 12){
                fm='10'
                lm ='12'
            }
            // ld = new Date(y, lm, 0).getDate()
            ld = new Date(y, lm, 0).getDate()
            return [[y,fm,'01'].join("-"),[y,lm,ld].join("-")]
        }else{
            var m = date.getMonth() + 1; //获取月份
            var fd = '01'
            var ld = new Date(y, m, 0).getDate(); //获取当月最后一日
            m = m < 10 ? '0' + m : m; //月份补 0
            return [[y,m,fd].join("-"),[y,m,ld].join("-")]
        }
    },
    // 获取 当天 当月 当季 当年 快捷日期选项
    getShortcuts(types=['当天','当月','当季','上季度','当年']){
      const shortcuts = [
        {
          text: '当年',
          value: [dayjs().startOf('year'), dayjs().endOf('year')],
        },
        {
          text: '当季',
          value: [dayjs().startOf('quarter'), dayjs().endOf('quarter')],
        },
        {
          text: '上季度',
          value: [dayjs().startOf('quarter').subtract(1, 'quarter'), dayjs().endOf('quarter').subtract(1, 'quarter')],
        },
        {
          text: '当月',
          value: [dayjs().startOf('month'), dayjs().endOf('month')],
        },
        
        {
          text: '当天',
          value: [dayjs().startOf('day'), dayjs().endOf('day')],
        }, 
      ]
      return shortcuts.filter(v=>types.includes(v.text))
    },
    getAfterYear(yearNum){
        var date = new Date();
		date.setFullYear(date.getFullYear() + yearNum)	
        return date
    }
  };
  export default dateUtils