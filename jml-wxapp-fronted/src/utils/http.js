/**
 * axios 二次封装
 */

import axios from "axios";
import qs from "qs";
import config from '@/common/config.js'
// import {ElMessage,ElLoading} from "element-plus";
import { ACCESS_TOKEN} from '@/store/mutation-types'
import router from '@/router'
import util from '@/utils/util'
export const loginRoutePath = '/login'
import { storeToRefs } from 'pinia'
import useUserStore from '@/store/modules/userStore'
// 小程序axios适配器
import mpAdapter from "axios-miniprogram-adapter";
axios.defaults.adapter = mpAdapter;
// loading对象
let loading,error;
// 请求合并只出现一次loading
// 当前正在请求的数量
let needLoadingRequestCount = 0,errorCount= 0 ;
// import router from "../router";
const TOKEN_INVALID = "Token认证失败，请重新登陆";
const NETWORK_ERROR = "网络请求异常，请稍后重试";
// 创建axios实例对象，添加全局配置
const service = axios.create({
	baseURL: config.baseURL,
	timeout: 100000000,
	blockApiErrer: true,
    showLoading:true,
	loadText: '数据加载中...',
	headers: {
		'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
        // 'Content-Type': 'application/json'
	},
	// validateStatus: function (status) {
	//     return true; // default
	// },
});
// 请求拦截
service.interceptors.request.use((config) => {
    const store = useUserStore();
    const { userId,token } = store;
    // if (token) {
    //     config.headers['token'] = token
    // }
    if (!config.isFile) {
        if (userId) {
            config.headers['staff-id'] = userId
        }
        // 如果是文件 则不做处理
       (config.method.toLowerCase() === 'post' && config.headers['Content-Type'].indexOf('application/json') === -1) ? config.data = qs.stringify({ ...config.data }) : config.params = { ...config.params }
    }
	if (config.showLoading) showLoading(config.loadingTarget, config.loadText);
	return config;
});

const tokenErr = ['-3','-4','-5','-6','-7']

// 响应拦截
service.interceptors.response.use(async (res) => {
	if (res.config.showLoading) {
		hideLoading();
	}
	if (res.status != 200) {
		return uni.showToast({ title: NETWORK_ERROR, icon: 'none' })
	}
	if (res.config.blockApiErrer != true) {
		return Promise.resolve(res.data);
	}
	const {code,data,msg} = res.data;
	if (code === '1') {
		return Promise.resolve(res.data);
	} else if ( tokenErr.includes(code)) {
		const userStore = useUserStore();
		// userStore.isCheck 
		await userStore.userLogout()
		if(code !== '-3') uni.showToast({ title: msg, icon: 'none' }) //-3 可以不弹不。
		router.replaceAll({ name: 'login' })
		return Promise.reject(msg);
	} else {
		uni.showToast({ title: msg, icon: 'none' })
		return Promise.reject(msg);
	}
},
error => {
	if (error.config && error.config.showLoading) {
		hideLoading();
	}
	let errMsg = '';
	if (error.request) {
		console.log(error.request)
	}
	if (error.response) {
		console.log(error.response.data);
		console.log(error.response.status);
		if (error.response.data.msg) errMsg = error.response.data.msg;
	}
	if (error && error.response) {
		switch (error.response.status) {
			case 400:
				error.message = '请求错误(400)';
				break;
			case 401:
				error.message = '未授权，请重新登录(401)';
				break;
			case 403:
				error.message = '拒绝访问(403)';
				break;
			case 404:
				error.message = '请求出错(404)';
				break;
			case 408:
				error.message = '请求超时(408)';
				break;
			case 500:
				error.message = '服务器错误(500)';
				break;
			case 501:
				error.message = '服务未实现(501)';
				break;
			case 502:
				error.message = '网络错误(502)';
				break;
			case 503:
				error.message = '服务不可用(503)';
				break;
			case 504:
				error.message = '网络超时(504)';
				break;
			case 505:
				error.message = 'HTTP版本不受支持(505)';
				break;
			default:
				error.message = '连接出错';
		}
	} else {
		error.message = '服务出错!'
	}
	// showError(error.message + errMsg)
	// ElMessage.error(error.message + errMsg);
	uni.showToast({ title: error.message + errMsg, icon: 'none' })
	return Promise.reject(error)
});

/* 显示loading */
async function  showLoading(target, text){
	if (needLoadingRequestCount === 0 && !loading) {
		loading = await uni.showLoading({
			title: '加载中...',
			mask:true
		});
	}
	needLoadingRequestCount++;
}
/* 显示error */
function showError(text) {
	if (errorCount === 0 && !error) {
		// error = ElMessage.error(text);
	}
	errorCount++;
}
/**
 * 请求核心函数
 * @param {*} options 请求配置
 */
const request = (options) => {
	return new Promise((resolve, reject) => {
		options.method = options.method || "get";
		if (options.method.toLowerCase() === "get") {
			options.params = options.data;
		}
		service(options)
			.then((res) => resolve(res))
			.catch((error) => {
                reject(error)
				console.log(error)
			});
	});
};


/* 隐藏loading */
function hideLoading() {
	if (needLoadingRequestCount <= 0) return;
	needLoadingRequestCount--;
	if (needLoadingRequestCount === 0 && loading) {
		setTimeout(() => {
			uni.hideLoading();
			loading = null;
		}, 1000);
	}
}

export default request;
