import debounce from "./debounce.js";
import throttle from "./throttle.js";
import test from './test.js'
import dateUtils from './dateUtils.js'

function removeUrlCodeQuery() {
  let location = window.location;
  let search = location.search;
  if (search) {
    search = search.substr(1);
  }
  let href = location.origin;
  let pathName = location.pathname;
  if (pathName) {
    href += pathName;
  }
  let searchArr = search.split("&").filter((item) => {
    if (item.indexOf("code=") !== -1) {
      return false;
    }
    if (item.indexOf("state=") !== -1) {
      return false;
    }
    return true;
  });
  if (searchArr.length > 0) {
    href += "?" + searchArr.join("&");
  }
  let hash = location.hash;
  if (hash) {
    href += hash;
  }
  window.location.href = href;
}

function formatTime(time) {
  if (typeof time !== "number" || time < 0) {
    return time;
  }

  var hour = parseInt(time / 3600);
  time = time % 3600;
  var minute = parseInt(time / 60);
  time = time % 60;
  var second = time;

  return [hour, minute, second]
    .map(function (n) {
      n = n.toString();
      return n[1] ? n : "0" + n;
    })
    .join(":");
}
function getUrlCode() {
  // 截取url中的code方法
  var url = location.search;
  // this.winUrl = url;
  var theRequest = new Object();
  if (url.indexOf("?") != -1) {
    var str = url.substr(1);
    var strs = str.split("&");
    for (var i = 0; i < strs.length; i++) {
      theRequest[strs[i].split("=")[0]] = strs[i].split("=")[1];
    }
  }
  return theRequest;
}
function objToUrlCode(obj, prefix = "?") {
  let url = prefix;
  for (let i in obj) {
    url += i + "=" + obj[i] + "&";
  }
  return url.substring(0, url.length - 1);
}
function formatLocation(longitude, latitude) {
  if (typeof longitude === "string" && typeof latitude === "string") {
    longitude = parseFloat(longitude);
    latitude = parseFloat(latitude);
  }

  longitude = longitude.toFixed(2);
  latitude = latitude.toFixed(2);

  return {
    longitude: longitude.toString().split("."),
    latitude: latitude.toString().split("."),
  };
}

function getQueryString(name) {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
  var r = window.location.search.substr(1).match(reg);
  if (r != null) return decodeURIComponent(r[2]);
  return null;
}

function myCache(key, value, seconds = 60 * 60) {
  if (!window.localStorage) {
    alert("浏览器支持localstorage");
    return false;
  } else {
    let nowTime = Date.parse(new Date()) / 1000;
    if (key && value) {
      let expire = nowTime + Number(seconds);
      localStorage.setItem(key, JSON.stringify(value) + "|" + expire);
      console.log("已经把" + key + "存入缓存,过期时间为" + expire);
    } else if (key && !value) {
      let val = localStorage.getItem(key);
      if (val) {
        // 缓存存在，判断是否过期
        let temp = val.split("|");
        if (!temp[1] || temp[1] <= nowTime) {
          localStorage.removeItem(key);
          console.log(key + "缓存已失效");
          return "";
        } else {
          return JSON.parse(temp[0]);
        }
      }
    }
  }
}
function formatSize(cellValue) {
    if (cellValue === 0) return '0 B';
    let k = 1024, // or 1024
        sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'],
        i = Math.floor(Math.log(cellValue) / Math.log(k));
    return (cellValue / Math.pow(k, i)).toPrecision(3) + ' ' + sizes[i];
}

//下载图片
function downloadImg(url) {
  return new Promise((resolve, reject) => {
    const a = document.createElement('a')
    const event = new MouseEvent('click')
    // 自定义下载后图片的名称
    a.download = '二维码'
    a.href = url
    a.dispatchEvent(event)
    setTimeout(resolve,500)
  })
}

// 下载 blob
function downLoadBlob (blob, fileName = 'file.xlsx') {
  // create file link in browser's memory
  const href = URL.createObjectURL(blob)

  // create "a" HTML element with href to file & click
  const link = document.createElement('a')
  link.href = href

  link.setAttribute('download', fileName) //or any other extension
  document.body.appendChild(link)
  link.click()

  // clean up "a" element & remove ObjectURL
  document.body.removeChild(link)
  URL.revokeObjectURL(href)
}

//保存的时候使用的
function addPrefixToProperties(obj, prefix) {
	let data = {}
    Object.keys(obj).forEach((key) => {
        const newKey = `${prefix}${key}`; // 将前缀与属性名称连接起来作为新的属性名称
        data[newKey] = Array.isArray(obj[key])?obj[key].join(';'):obj[key]; // 创建新的属性并赋值
    });
	return data;
}

function extractCode(str) {
    const match = str.match(/\{(.*?):\d+\}/);
    return match ? match[1] : null;
}

export default {
    formatTime: formatTime,
    formatLocation: formatLocation,
    dateUtils,
    getUrlCode: getUrlCode,
    removeUrlCodeQuery: removeUrlCodeQuery,
    objToUrlCode: objToUrlCode,
    getQueryString: getQueryString,
    myCache: myCache,
    formatSize,formatSize,
    test,
    debounce,
    throttle,
    downloadImg,
    downLoadBlob,
	addPrefixToProperties
}
