import { defineConfig,loadEnv } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'
import AutoImport from 'unplugin-auto-import/vite'
// import Components from 'unplugin-vue-components/vite';
import TransformPages from 'uni-read-pages-vite'
import path from 'path'

export default defineConfig({
	base: './',
	envPrefix:  ['VITE', 'VUE'], // 环境变量前缀,默认只会暴露VITE开头变量，定义后可暴露VUE开头变量
	plugins: [
		uni(),
		AutoImport({
			imports: [
				'vue',
				'uni-app',
				'pinia',
				{
				  from: 'uni-mini-router',
				  imports: ['createRouter', 'useRouter', 'useRoute']
				}
			],
			dts: 'src/auto-imports.d.ts',
			dirs: ['src/store/modules','src/vueUse'],
			eslintrc: {
				enabled: true,
				globalsPropValue: true
			}
		}),
		//小程序无效？
		// Components({
		// 	dirs: ['src/components'],
		// 	extensions: ['vue', 'tsx'],
		// 	        // 配置文件生成位置
		// 	dts: 'src/components.d.ts'
		//   // 指定自动导入的组件位置，默认是 src/components
		//   // dirs: ['src/components'],
		// }),
	],
	define: {
		  ROUTES: new TransformPages().routes, // 注入路由表
	},
	resolve: {
		alias: {
			'@': path.resolve(__dirname, './src'),
			'*': path.resolve('')
		},
	},
	// build: {
	//     sourcemap: true,
	// },
})
