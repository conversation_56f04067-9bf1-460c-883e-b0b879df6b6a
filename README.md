# 金米龙企业微信管理系统

## 项目概述

金米龙企业微信管理系统是一套基于企业微信的综合业务管理平台，涵盖销售管理、客户关系管理、工厂生产管理等核心业务模块。

### 技术架构
- **前端**: Vue 3 + Vite + Vant UI
- **后端**: JFinal + JBoot + MySQL
- **部署**: 支持多环境部署(dev/uat/prod)

## 项目结构

```
├── jml-wxwork-fronted/          # 前端项目
│   ├── src/
│   │   ├── views/               # 页面组件
│   │   ├── components/          # 公共组件
│   │   ├── api/                 # API接口
│   │   ├── router/              # 路由配置
│   │   └── utils/               # 工具类
│   ├── package.json
│   └── vite.config.js
└── jml-wxapp-service/           # 后端项目
    └── jml-wxwork-service/
        ├── jml-wxwork-api/      # 主API服务
        └── jml-wxwork-api-salesforce/  # Salesforce集成服务
```

## 环境要求

### 前端环境
- Node.js 18.0+
- npm/pnpm/yarn

### 后端环境
- JDK 8+
- MySQL 5.7+
- Maven 3.6+

## 快速开始

### 前端启动

```bash
cd jml-wxwork-fronted
npm install

# 开发环境
npm run dev

# UAT环境
npm run uat

# 生产构建
npm run build-prod
```

### 后端启动

```bash
cd jml-wxapp-service/jml-wxwork-service/jml-wxwork-api
mvn clean compile
mvn exec:java
```

## 核心模块

### 1. 销售管理模块
- **销售线索** (`/api/salesLead`)
- **商机管理** (`/api/opportunity`) 
- **客户账户** (`/api/account`)
- **联系人** (`/api/contact`)
- **工作报告** (`/api/salesReport`)

### 2. 工厂管理模块
- **生产线管理** (`/api/pressLineProduction`, `/api/printingLineReport`)
- **设备维修** (`/api/deviceRepair`)
- **能源管理** (`/api/electricityLog`, `/api/naturalGasLog`)
- **质量检查** (`/api/dailyInspection`)
- **采购管理** (`/api/partsPurchase`)

### 3. 客诉管理模块
- **客诉处理** (`/api/case`)
- **投诉跟踪** 
- **处理历史**

### 4. 系统管理模块
- **员工管理** (`/api/jml/staff`)
- **权限控制**
- **菜单管理**
- **字典管理** (`/api/core/dict`)

## 数据库设计

### 核心业务表
```sql
-- 销售管理
jml_sales_lead          -- 销售线索
jml_opportunity         -- 商机
jml_account            -- 客户账户
jml_contact            -- 联系人
jml_sales_report       -- 销售报告

-- 工厂管理
jml_press_line_production    -- 压贴线生产
jml_device_repair           -- 设备维修
jml_electricity_log         -- 用电记录
jml_daily_inspection        -- 日常检查

-- 系统管理
jml_staff              -- 员工表
jml_project            -- 项目表
sys_log                -- 系统日志
```

## API接口文档

### 认证相关
```javascript
POST /openapi/wxwork/auth/sign        // 企业微信签名
GET  /openapi/wxwork/auth/loadConf    // 加载配置
POST /openapi/wxwork/auth/detail      // 用户详情
```

### 销售管理
```javascript
GET  /api/salesLead/querySalesLeadPage     // 销售线索分页
POST /api/salesLead/saveSalesLead          // 保存销售线索
GET  /api/salesLead/querySalesLeadDetails  // 销售线索详情

GET  /api/opportunity/queryOpportunityPageBy  // 商机分页
POST /api/opportunity/saveOpportunity         // 保存商机
GET  /api/opportunity/queryOpportunityDetails // 商机详情
```

### 工厂管理
```javascript
GET  /api/pressLineProduction/queryPageBy  // 压贴线生产分页
POST /api/pressLineProduction/save         // 保存生产记录
GET  /api/deviceRepair/queryPageBy         // 设备维修分页
POST /api/deviceRepair/save                // 提交维修申请
```

## 前端开发指南

### 项目配置
```javascript
// vite.config.js
export default defineConfig({
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './src'),
        },
    },
    server: {
        host: '0.0.0.0',
        open: true,
    },
    envPrefix: ['VITE', 'VUE'],
})
```

### 路由配置
```javascript
// router/index.js
const routes = [
    { path: '/salesLead', component: () => import('@/views/salesLead/List.vue') },
    { path: '/opportunity', component: () => import('@/views/opportunity/List.vue') },
    // ...更多路由
]
```

### API调用示例
```javascript
// api/salesLead.js
import request from '@/utils/request'

export function queryPageData(data) {
    return request({
        url: '/api/salesLead/querySalesLeadPage',
        method: 'get',
        data: data
    })
}
```

### 组件开发规范
```vue
<template>
    <div class="page pd100">
        <van-list>
            <!-- 列表内容 -->
        </van-list>
        <footer-btn @onconfirm="save" />
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const list = ref([])

onMounted(() => {
    loadData()
})
</script>
```

## 后端开发指南

### Controller开发规范
```java
@RequestMapping("/api/moduleName")
public class ModuleController extends Base4JmlAuthController {
    
    @Inject
    private ModuleBusi moduleBusi;
    
    public void queryPageBy() {
        Map<String, Object> paramsMap = initQueryMap();
        paramsMap.put("multiFiled", getPara("multiFiled"));
        renderJson(moduleBusi.queryPageBy(getPageNo(), getPageSize(), paramsMap));
    }
    
    @Before({ModuleSaveValidator.class, Tx.class})
    public void save() {
        renderJson(moduleBusi.save(getAttr("pojo"), initQueryMap()));
    }
}
```

### Business层开发
```java
public class ModuleBusi {
    
    public RestApiResult queryPageBy(int pageNo, int pageSize, Map<String,Object> paramsMap) {
        Page<ModuleModel> page = ModuleModel.dao.queryPage(pageNo, pageSize, paramsMap);
        return RestApiResult.buildSuccess(page);
    }
    
    public RestApiResult save(ModulePojo pojo, Map<String,Object> paramsMap) {
        ModuleModel model = BeanModelKit.printProperties(pojo, ModuleModel.class, true);
        
        if (StringUtils.isEmpty(model.get("id"))) {
            // 新增逻辑
            JmlKit.setSaveModel(model, paramsMap);
            model.save();
        } else {
            // 更新逻辑
            JmlKit.setUpdateModel(model, paramsMap);
            model.update();
        }
        
        return RestApiResult.buildSuccess();
    }
}
```

### Model层开发
```java
@Table(tableName = "jml_module", primaryKey = "id")
public class ModuleModel extends DbXmlModel4Jboot<ModuleModel> implements IBean {
    
    public Page<ModuleModel> queryPage(int pageNo, int pageSize, Map<String,Object> paramsMap) {
        return paginateForXml(pageNo, pageSize, "module.queryPage", paramsMap);
    }
    
    public ModuleModel queryDetails(String id) {
        Map<String,Object> paramsMap = new HashMap<>();
        paramsMap.put("id", id);
        return findFirstForXml("module.queryDetails", paramsMap);
    }
}
```

## 权限控制

### 前端权限指令
```vue
<template>
    <!-- 按钮权限控制 -->
    <van-button v-has="{ menu_code: 'SalesLead', function_code: 'C' }">
        新增
    </van-button>
    
    <!-- 编辑权限 -->
    <footer-btn v-has="{ menu_code: 'SalesLead', function_code: 'U' }" 
                @onconfirm="edit" />
</template>
```

### 后端权限控制
```xml
<!-- SQL权限控制 -->
<sql id="auth">
    1 = 1
    <if test="staff.factRoleGM == true || staff.factRoleGMS == true">
        <!-- 主管/厂长看所有数据 -->
    </if>
    <if test="staff.factRoleSE == true">
        <!-- 普通员工只看自己的数据 -->
        and a.created_staff_id = #{staff.staffId}
    </if>
</sql>
```

## 部署说明

### 环境配置
```bash
# 开发环境
npm run dev
mvn exec:java -Denv=dev

# UAT环境  
npm run build-uat
mvn exec:java -Denv=uat

# 生产环境
npm run build-prod
mvn exec:java -Denv=prod
```

### 配置文件
- 前端: `.env.dev`, `.env.uat`, `.env.prod`
- 后端: `env/dev/`, `env/uat/`, `env/prod/`

## 常见问题

### 1. 企业微信认证失败
检查企业微信配置参数是否正确，确保corpId和secret有效。

### 2. 接口跨域问题
开发环境已配置代理，生产环境需要nginx配置CORS。

### 3. 权限验证失败
确保用户已正确登录并获取到有效的token。

## 联系方式

- **开发者**: tangaga
- **邮箱**: <EMAIL>
- **技术栈**: Vue3 + JFinal + MySQL

## 版权信息

本项目基于以下开源组件：
- Vue 3
- Vant UI
- Element Plus
- JFinal Framework
- JBoot

---

*最后更新: 2024年*
