<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlGroup PUBLIC "-
<!-- 销售线索管理SQL -->
<sqlGroup namespace="salesLead">
	<!--分页查询销售线索-->
	<select id="queryPage" parameterType="map">
		select
			a.id,
			a.`name`,
			a.mobile_phone,
			a.company,
			a.`status`,
			a.is_converted,
			date_format(a.created_time,'%Y-%m-%d %H:%i:%s') as created_time,
			cs.`name` AS createdStaffName
		<pageTag/>
		from
			jml_sales_lead a
		left join
			jml_staff cs ON a.created_staff_id = cs.id
		<where>
			<include refid="common.auth"/>
			<if test="multiFiled != null and multiFiled != ''">
				and a.name like '%${multiFiled}%'
			</if>
		</where>
		order by
			a.created_time desc
	</select>

	<!--获取销售线索明细-->
	<select id="queryDetails" parameterType="map">
		select
			a.id,
			a.name,
			company,
			a.company_phone,
			a.mobile_phone,
			a.email,
			a.wechat,
			a.product,
			a.source,
			a.province,
			a.city,
			a.address,
			a.rating,
			a.status,
			a.longitude,
			a.latitude,
			a.is_converted,
			cs.`name` AS createdStaffName,
			us.`name` AS updateStaffName,
			date_format( a.created_time, '%Y-%m-%d %H:%i:%s' ) as created_time,
			date_format( a.update_time, '%Y-%m-%d %H:%i:%s' ) as update_time,
			a.marketing_id_list,
			a.area_code,
			ar.name as areaName,
			a.district
		from
			jml_sales_lead a
		left join
			jml_staff cs on a.created_staff_id = cs.id
		left join
			jml_staff us ON a.update_staff_id = us.id
		left join
			jml_area ar on a.area_code = ar.area_code
		<where>
			a.id=#{id}
		</where>
	</select>
	<!--根据员工信息获取活动列表信息-->
	<select id="queryListByStaff" parameterType="map">
		select
			a.id,
			a.name
		from
			jml_sales_lead a
		<where>
			<include refid="common.auth"/>
		</where>
	</select>
</sqlGroup>