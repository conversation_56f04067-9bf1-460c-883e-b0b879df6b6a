<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlGroup PUBLIC "-
<!-- 氮气登记表SQL -->
<sqlGroup namespace="electricityLog">
    <select id="queryPageBy" parameterType="map">
        select
            a.id,
            a.name AS name,
            a.daily_reading AS dailyReading,
            a.before_reading AS beforeReading,
            a.before_usage AS beforeUsage,
            date_format( a.created_time, '%Y-%m-%d %H:%i:%s' ) as createdTime,
            cs.`name` AS createdStaffName,
            a.electricity_type AS electricityType
        <pageTag/>
        from
            jml_electricity_log  a
            left join jml_staff cs ON a.created_staff_id = cs.id
        <where>
            <!--<include refid="common-fact.auth"/>-->
            <if test="multiFiled != null and multiFiled != ''">
                and (a.name like '%${multiFiled}%')
            </if>
            <!--创建跨度时间筛选-->
            <if test="startDate != null and startDate != ''">
                and a.created_time <![CDATA[>=]]> str_to_date(#{startDate},'%Y-%m-%d')
            </if>
            <if test="endDate != null and endDate != ''">
                and a.created_time <![CDATA[<=]]> date_add(str_to_date(#{endDate},'%Y-%m-%d'),interval 1 day)
            </if>
            <!--创建跨度时间筛选-->
            <if test="createdStaffId != null and createdStaffId != ''">
                and a.created_staff_id = #{createdStaffId}
            </if>
        </where>
        order by a.update_time desc
    </select>
    <select id="queryDetails" parameterType="map">
        SELECT
            a.id AS id,
            a.name AS name,
            a.daily_reading AS dailyReading,
            CONCAT_WS(";", a.electricity_img1) AS electricityImg,
            a.before_reading AS beforeReading,
            a.before_usage AS beforeUsage,
            a.remark AS remark,
            a.update_staff_id AS updateStaffId,
            a.created_staff_id AS createdStaffId,
            DATE_FORMAT(a.created_time, '%Y-%m-%d %H:%i:%s') AS createdTime,
            DATE_FORMAT(a.update_time, '%Y-%m-%d %H:%i:%s') AS updateTime,
            a.electricity_type AS electricityType,
            cs.`name` AS createdStaffName,
            us.`name` AS updateStaffName
        FROM
            jml_electricity_log a
        left join
            jml_staff cs on a.created_staff_id = cs.id
        left join
            jml_staff us ON a.update_staff_id = us.id
        WHERE
            a.id = #{id}
    </select>
    
    <select id="queryBeforeReading" parameterType="map">
        SELECT
            id,
            daily_reading as beforeReading
        FROM
            jml_electricity_log
        WHERE
            electricity_type = #{electricityType}
        order by
          created_time DESC
          limit 1
    </select>


</sqlGroup>