<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlGroup PUBLIC "-
<!-- 烤漆线-生产工艺单SQL -->
<sqlGroup namespace="sandingBeltReplacement">
    <select id="queryPageBy" parameterType="map">
        select
            a.id AS id,
            a.name as name,
            date_format( a.replace_date, '%Y-%m-%d' ) as replaceDate,
            a.replace_start_time as replaceStartTime,
            a.replace_end_time as replaceEndTime,
            a.replace_reason as replaceReason,
            a.machine_no as machineNo,
            a.product_line as productLine,
            date_format( a.created_time, '%Y-%m-%d %H:%i:%s' ) as createdTime,
            cs.`name` AS createdStaffName
        <pageTag/>
        from
            jml_sanding_belt_replacement a
            left join jml_staff cs ON a.created_staff_id = cs.id
        <where>
            <include refid="common-fact.auth"/>
            <if test="multiFiled != null and multiFiled != ''">
                and (a.name like '%${multiFiled}%'  or a.machine_no like '%${multiFiled}%')
            </if>
            <!--创建跨度时间筛选-->
            <if test="startDate != null and startDate != ''">
                and a.created_time <![CDATA[>=]]> str_to_date(#{startDate},'%Y-%m-%d')
            </if>
            <if test="endDate != null and endDate != ''">
                and a.created_time <![CDATA[<=]]> date_add(str_to_date(#{endDate},'%Y-%m-%d'),interval 1 day)
            </if>
            <!--创建跨度时间筛选-->
            <if test="createdStaffId != null and createdStaffId != ''">
                and a.created_staff_id = #{createdStaffId}
            </if>
        </where>
        order by a.update_time desc
    </select>
    <select id="queryDetails" parameterType="map">
        SELECT
            a.id AS id,
            a.name AS name,
            a.machine_no AS machineNo,
            a.product_line AS productLine,
            a.replace_reason AS replaceReason,
            a.reason_desc AS reasonDesc,
            a.replace_start_time AS replaceStartTime,
            date_format( a.replace_date, '%Y-%m-%d' ) as replaceDate,
            a.replace_cnt AS replaceCnt,
            a.replace_end_time AS replaceEndTime,
            a.sanding_qty AS sandingQty,
            a.sandpaper_model AS sandpaperModel,
            a.sandpaper_sn AS sandpaperSn,
            a.remark AS remark,
            a.update_staff_id AS updateStaffId,
            a.created_staff_id AS createdStaffId,
            DATE_FORMAT(a.created_time, '%Y-%m-%d %H:%i:%s') AS createdTime,
            DATE_FORMAT(a.update_time, '%Y-%m-%d %H:%i:%s') AS updateTime,
            cs.`name` AS createdStaffName,
            us.`name` AS updateStaffName
        FROM
            jml_sanding_belt_replacement  a
        left join
            jml_staff cs on a.created_staff_id = cs.id
        left join
            jml_staff us ON a.update_staff_id = us.id
        WHERE
            a.id = #{id};
    </select>


</sqlGroup>