<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlGroup PUBLIC "-
<!-- 烤漆线-生产工艺单SQL -->
<sqlGroup namespace="printingLineReport">
    <select id="queryPageBy" parameterType="map">
        select
            a.id AS id,
            a.name as name,
            date_format( a.date, '%Y-%m-%d' ) as date,
            a.start_time as startTime,
            a.end_time as endTime,
            a.work_no as workNo,
            a.print_qty AS printQty,
            date_format( a.created_time, '%Y-%m-%d %H:%i:%s' ) as createdTime,
            cs.`name` AS createdStaffName
        <pageTag/>
        from
            jml_printing_line_report a
            left join jml_staff cs ON a.created_staff_id = cs.id
        <where>
            <include refid="common-fact.auth"/>
            <if test="multiFiled != null and multiFiled != ''">
                and (a.name like '%${multiFiled}%' or a.work_no like '%${multiFiled}%')
            </if>
            <!--创建跨度时间筛选-->
            <if test="startDate != null and startDate != ''">
                and a.created_time <![CDATA[>=]]> str_to_date(#{startDate},'%Y-%m-%d')
            </if>
            <if test="endDate != null and endDate != ''">
                and a.created_time <![CDATA[<=]]> date_add(str_to_date(#{endDate},'%Y-%m-%d'),interval 1 day)
            </if>
            <!--创建跨度时间筛选-->
            <if test="createdStaffId != null and createdStaffId != ''">
                and a.created_staff_id = #{createdStaffId}
            </if>
        </where>
        order by a.update_time desc
    </select>
    <select id="queryDetails" parameterType="map">
        SELECT
            a.id AS id,
            a.name AS name,
            a.work_no AS workNo,
            a.start_time AS startTime,
            DATE_FORMAT(a.date, '%Y-%m-%d') AS date,
            a.end_time AS endTime,
            a.remark AS remark,
            a.update_staff_id AS updateStaffId,
            a.created_staff_id AS createdStaffId,
            DATE_FORMAT(a.created_time, '%Y-%m-%d %H:%i:%s') AS createdTime,
            DATE_FORMAT(a.update_time, '%Y-%m-%d %H:%i:%s') AS updateTime,
            cs.`name` AS createdStaffName,
            a.print_qty AS printQty,
            us.`name` AS updateStaffName
        FROM
            jml_printing_line_report  a
        left join
            jml_staff cs on a.created_staff_id = cs.id
        left join
            jml_staff us ON a.update_staff_id = us.id
        WHERE
            a.id = #{id};
    </select>


</sqlGroup>