<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlGroup PUBLIC "-
<!-- 里程认证管理SQL -->
<sqlGroup namespace="mileageCer">
	<!--分页查询里程认证-->
	<select id="queryPageBy" parameterType="map">
		select
			a.id,
			a.`name`,
			a.start_address,
			a.end_address,
			a.mileage,
			a.start_mileage,
			a.status,
			date_format(a.start_time,'%Y-%m-%d %H:%i:%s') as start_time,
			date_format(a.end_time,'%Y-%m-%d %H:%i:%s') as end_time,
			date_format(a.created_time,'%Y-%m-%d %H:%i:%s') as created_time,
			cs.`name` AS createdStaffName,
			ar.name as areaName,
			a.district
		<pageTag/>
		from
			jml_mileage_certification a
		left join
			jml_staff cs ON a.created_staff_id = cs.id
		left join
			jml_area ar on a.area_code = ar.area_code
		<where>
			<if test = 'status== "1"'>
				<include refid="common.auth"/>
			</if>
			<if test="status != null and status != ''">
				and a.status = #{status}
			</if>

			<if test = 'status== "0"'>
				and a.created_staff_id = #{staffId}
			</if>

		</where>
		order by
			a.created_time desc
	</select>

	<!--获取未结束的里程认证-->
	<select id="queryUnEndMileageCer" parameterType="map">
		select
			a.id,
			a.start_address,
			a.start_time,
			a.start_mileage,
			a.`status`
		from
			jml_mileage_certification a
		<where>
			and a.status = '0'
			and a.created_staff_id = #{staffId}
		</where>
	</select>

	<!--获取明细-->
	<select id="queryDetails" parameterType="map">
		select
			a.id,
			a.name,
			a.mileage,
			a.start_mileage,
			a.start_odometer_img,
			a.start_address,
			date_format(a.start_time,'%Y-%m-%d %H:%i:%s') as start_time,
			a.end_mileage,
			a.end_odometer_img,
			a.end_address,
			date_format(a.end_time,'%Y-%m-%d %H:%i:%s') as end_time,
			cs.`name` AS createdStaffName,
			us.`name` AS updateStaffName,
			date_format( a.created_time, '%Y-%m-%d %H:%i:%s' ) as created_time,
			date_format( a.update_time, '%Y-%m-%d %H:%i:%s' ) as update_time,
			ar.name as areaName,
			a.district,
			a.area_code,
			a.created_staff_id
		from
			jml_mileage_certification a
		left join
			jml_staff cs on a.created_staff_id = cs.id
		left join
			jml_staff us ON a.update_staff_id = us.id
		left join
			jml_area ar on a.area_code = ar.area_code
		<where>
			a.id=#{id}
		</where>
	</select>


</sqlGroup>