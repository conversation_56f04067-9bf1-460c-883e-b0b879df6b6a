package com.glenls.jml.validator.factory;

import com.glenls.api.modules.pub.validator.ValidatorBase4Pub;
import com.glenls.jml.pojo.factory.DeviceRepairPojo;
import com.glenls.jml.pojo.factory.DeviceRepairProgressPojo;
import com.jfinal.core.Controller;


public class DeviceRepairRepairValidator extends ValidatorBase4Pub {
    protected void validate(Controller c) {
        super.validate(c);
        DeviceRepairProgressPojo pojo = getPojo(DeviceRepairProgressPojo.class);
        this.validatePojo(pojo);
        c.setAttr("pojo",pojo);
    }
    protected void handleError(Controller c) {
        super.handleError(c);
    }
}
