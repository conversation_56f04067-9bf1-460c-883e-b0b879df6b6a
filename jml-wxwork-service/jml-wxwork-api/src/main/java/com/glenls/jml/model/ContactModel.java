package com.glenls.jml.model;

import com.glenls.commons.jboot.plugins.sqlxml.plugin.activerecord.DbXmlModel4Jboot;
import com.jfinal.plugin.activerecord.IBean;
import com.jfinal.plugin.activerecord.Page;
import io.jboot.db.annotation.Table;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Table(tableName = "jml_contact",primaryKey = "id")
public class ContactModel extends DbXmlModel4Jboot<ContactModel> implements IBean {


    public Page<ContactModel> queryPageBy(int pageNo, int pageSize, Map<String,Object> paramsMap){
        return paginateForXml(pageNo, pageSize, "contact.queryPageBy",paramsMap);
    }


    public ContactModel queryDetails(String id){
        Map<String,Object> paramsMap = new HashMap<>();
        paramsMap.put("id",id);
        return findFirstForXml("contact.queryDetails",paramsMap);
    }


    public List<ContactModel> queryListByStaff(Map<String,Object> paramsMap){
        return findForXml("contact.queryListByStaff",paramsMap);
    }

}
