package com.glenls.jml.modules.privilege.validator;

import com.glenls.jml.modules.pub.validator.JmlBaseValidator;
import com.jfinal.core.Controller;
import lombok.extern.slf4j.Slf4j;


@Slf4j
public class MenuFunctionValidator extends JmlBaseValidator {

    @Override
    protected void validate(Controller c) {
        super.validate(c);
    }

    @Override
    protected void handleError(Controller c) {
        super.handleError(c);
    }
}
