<assembly xmlns="http:
          xmlns:xsi="http:
          xsi:schemaLocation="http:

    <!--
      assembly 打包配置更多配置可参考官司方文档：
      http:
    -->

    <id>release</id>

    <!--
        设置打包格式，可同时设置多种格式，常用格式有：dir、zip、tar、tar.gz
        dir 格式便于在本地测试打包结果
        zip 格式便于 windows 系统下解压运行
        tar、tar.gz 格式便于 linux 系统下解压运行
    -->
    <formats>
        <format>dir</format>
        <format>zip</format>
        <!-- <format>tar.gz</format> -->
    </formats>

    <!-- 打 zip 设置为 true 时，会在 zip 包中生成一个根目录，打 dir 时设置为 false 少层目录 -->
    <includeBaseDirectory>true</includeBaseDirectory>

    <fileSets>
        <!-- src/main/resources 全部 copy 到 config 目录下 -->
        <fileSet>
            <directory>${basedir}/src/main/resources</directory>
            <outputDirectory>config</outputDirectory>
        </fileSet>

        <!-- src/main/webapp 全部 copy 到 webapp 目录下 -->
        <fileSet>
            <directory>${basedir}/src/main/webapp</directory>
            <outputDirectory>webapp</outputDirectory>
        </fileSet>

        <!-- 项目根下面的脚本文件 copy 到根目录下 -->
        <fileSet>
            <directory>${basedir}</directory>
            <outputDirectory></outputDirectory>
            <fileMode>755</fileMode>
            <lineEnding>unix</lineEnding>
            <includes>
                <include>*.sh</include>
            </includes>
        </fileSet>

        <fileSet>
            <directory>${basedir}</directory>
            <outputDirectory></outputDirectory>
            <fileMode>755</fileMode>
            <lineEnding>windows</lineEnding>
            <includes>
                <include>*.bat</include>
            </includes>
        </fileSet>

    </fileSets>

    <!-- 依赖的 jar 包 copy 到 lib 目录下 -->
    <dependencySets>
        <dependencySet>
            <outputDirectory>lib</outputDirectory>
        </dependencySet>
    </dependencySets>

</assembly>