<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlGroup PUBLIC "-
<!-- wxwork_rela_user_dept 的相关SQL -->
<sqlGroup namespace="wxwork_rela_user_dept">
    <!--批量插入-->
    <update id="batchInsert" parameterType="map">
        INSERT INTO wxwork_rela_user_dept (userid, deptid) VALUES(?, ?);
    </update>


    <!--根据部门ID删除用户关联信息-->
    <delete id="delByDeptId" parameterType="map">
        delete from wxwork_rela_user_dept where deptid =#{deptId}
    </delete>

    <!--批量删除用户部门关联信息-->
    <delete id="batchDelByUserId" parameterType="map">
        delete from wxwork_rela_user_dept where userid = ?
    </delete>
</sqlGroup>