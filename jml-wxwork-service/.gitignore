.idea
**/target*.iml
/.vscode/
/jml-wxwork-api/src/main/webapp/WEB-INF/classes/

### Intellij template
# Covers JetBrains IDEs: IntelliJ, RubyMine, PhpStorm, AppCode, PyCharm, CLion, Android Studio, WebStorm and Rider
# Reference: https:

# User-specific stuff
.ideaworkspace.xml
.ideatasks.xml
.ideausage.statistics.xml
.ideadictionaries
.ideashelf

# AWS User-specific
.ideaaws.xml

# Generated files
.ideacontentModel.xml

# Sensitive or high-churn files
.ideadataSources/
.ideadataSources.ids
.ideadataSources.local.xml
.ideasqlDataSources.xml
.ideadynamic.xml
.ideauiDesigner.xml
.ideadbnavigator.xml

# Gradle
.ideagradle.xml
.idealibraries

# Gradle and Maven with auto-import
# When using <PERSON>radle or Maven with auto-import, you should exclude module files,
# since they will be recreated, and may cause churn.  Uncomment if using
# auto-import.
# .idea/artifacts
# .idea/compiler.xml
# .idea/jarRepositories.xml
# .idea/modules.xml
# .idea

# Mongo Explorer plugin
.ideamongoSettings.xml

# File-based project format
*.iws

# IntelliJ
out/

# mpeltonen/sbt-idea plugin
.idea_modules/

# JIRA plugin
atlassian-ide-plugin.xml

# Cursive Clojure plugin
.idea/replstate.xml

# SonarLint plugin
.idea/sonarlint/

# Crashlytics plugin (for Android Studio and IntelliJ)
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties
fabric.properties

# Editor-based Rest Client
.idea/httpRequests

# Android studio 3.1+ serialized cache file
.idea/caches/build_file_checksums.ser

